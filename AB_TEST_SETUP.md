# A/B Test Setup - Landing Page Redesign

## 🎯 Overview

This document describes the A/B testing implementation for the landing page redesign. The test compares the current home page design (Variant A) with a new editorial-style design (Variant B) using a 50/50 traffic split.

## 📊 Test Configuration

### Test Details
- **Test Name**: `landing_page_redesign`
- **Traffic Split**: 50% Variant A / 50% Variant B
- **Persistence**: localStorage (users see consistent variant)
- **Duration**: Ongoing until manually stopped

### Variants
- **Variant A**: Current home page design (`ui/src/screens/Home/index_live.tsx`)
- **Variant B**: Editorial-style design (`ui/src/screens/Home/EditorialLanding.tsx`)

## 🚀 Implementation

### Files Created
1. **`ui/src/hooks/useABTest.ts`** - A/B testing hook with analytics
2. **`ui/src/screens/Home/EditorialLanding.tsx`** - New editorial design
3. **`ui/src/screens/Home/ABTestLanding.tsx`** - A/B test wrapper component
4. **`ui/src/screens/ABTestDashboard.tsx`** - Analytics dashboard
5. **`ui/src/components/ABTestControls.tsx`** - Development controls

### Routes Updated
- **`/`** - Now serves A/B test (random assignment)
- **`/home-original`** - Direct access to original design
- **`/ab-test-dashboard`** - Analytics dashboard
- **`/landing-preview`** - Preview of editorial design

## 📈 Tracked Events

### Automatic Events
- **`ab_test_variant_assigned`** - When user gets assigned to a variant
- **`ab_test_variant_shown`** - When user sees a variant (page view)
- **`quiz_started`** - When user starts the quiz (conversion event)

### Manual Events
- **`cta_click`** - Primary CTA button clicks
- **`secondary_cta_click`** - Secondary button clicks

## 🎛️ Development Controls

In development mode, you'll see floating controls in the bottom-right corner:

### Controls Available
- **Force Variant A** - See current design
- **Force Variant B** - See editorial design  
- **View Dashboard** - Open analytics dashboard
- **Clear Test Data** - Reset all test data

### Usage
```javascript
// Force a specific variant
localStorage.setItem('ab_test_landing_page_redesign', 'A'); // or 'B'
window.location.reload();

// Clear test data
import { clearABTestData } from './hooks/useABTest';
clearABTestData('landing_page_redesign');
```

## 📊 Analytics Dashboard

Access the dashboard at `/ab-test-dashboard` to view:

### Metrics Tracked
- **Total Views** - Page views for both variants
- **Total Clicks** - CTA clicks across variants
- **Total Conversions** - Quiz starts (main goal)
- **Click-Through Rate** - Clicks / Views
- **Conversion Rate** - Conversions / Views
- **Current Winner** - Variant with higher conversion rate

### Variant Comparison
- Side-by-side performance comparison
- Statistical significance indicators
- Real-time updates

## 🔧 How to Use

### 1. Monitor Performance
- Check `/ab-test-dashboard` regularly
- Look for conversion rate differences
- Wait for statistical significance (100+ conversions per variant recommended)

### 2. Analyze Results
```javascript
import { getABTestResults } from './hooks/useABTest';
const results = getABTestResults('landing_page_redesign');
console.log(results);
```

### 3. Choose Winner
Based on conversion rate performance:
- If Variant A wins: Keep current design
- If Variant B wins: Implement editorial design permanently

### 4. Implement Winner
```javascript
// To make Variant B permanent:
// 1. Update App.tsx route from <ABTestLanding /> to <EditorialLanding />
// 2. Remove A/B test components
// 3. Clean up unused code
```

## 📱 Mobile Responsiveness

Both variants are fully responsive:
- **Variant A**: Existing responsive design
- **Variant B**: Editorial design with mobile-first approach

## 🔒 Data Privacy

### Data Storage
- Test assignments stored in localStorage
- Event data stored locally (not sent to external services)
- No personal information collected

### Production Integration
For production analytics, integrate with:
- Google Analytics
- Mixpanel
- Custom analytics endpoint

Example integration in `useABTest.ts`:
```javascript
// Send to Google Analytics
if (window.gtag) {
  window.gtag('event', eventName, {
    test_name: testName,
    variant: variant,
    ...properties
  });
}
```

## 🚨 Important Notes

### Before Going Live
1. **Remove development indicators** - Update `ABTestLanding.tsx` to remove dev banners
2. **Set up production analytics** - Integrate with your analytics platform
3. **Test both variants** - Ensure both designs work correctly
4. **Monitor performance** - Watch for any technical issues

### Statistical Significance
- Wait for at least 100 conversions per variant
- Run test for minimum 1-2 weeks
- Consider seasonal effects and traffic patterns
- Use statistical significance calculators for confidence

### Stopping the Test
When ready to implement the winner:
1. Update the main route in `App.tsx`
2. Remove A/B test components
3. Clean up unused files
4. Document the results

## 📞 Support

For questions about the A/B test implementation:
1. Check the analytics dashboard first
2. Review this documentation
3. Examine the code in the files listed above
4. Use development controls for testing

## 🎉 Success Metrics

### Primary Goal
- **Conversion Rate**: Quiz starts / Page views

### Secondary Goals
- **Click-Through Rate**: CTA clicks / Page views
- **User Engagement**: Time on page, scroll depth
- **Bounce Rate**: Single-page sessions

The A/B test is now live and automatically splitting traffic 50/50 between the current design and the new editorial design! 🚀
