"""
Pytest configuration and fixtures for users tests.
"""

import pytest
from django.contrib.auth import get_user_model
from django.test import Client
from django.core.files.uploadedfile import SimpleUploadedFile
from django_countries.fields import Country
from unittest.mock import Mock, patch

from products.models import Product
from users.models import UserProfile, UserImage

User = get_user_model()


@pytest.fixture
def api_client():
    """Provide a Django test client."""
    return Client()


@pytest.fixture
def user_data():
    """Sample user data for testing."""
    return {
        "email": "<EMAIL>",
        "password": "testpass123",
        "first_name": "Test",
        "last_name": "User"
    }


@pytest.fixture
def user(user_data):
    """Create a test user."""
    return User.objects.create_user(**user_data)


@pytest.fixture
def superuser():
    """Create a test superuser."""
    return User.objects.create_superuser(
        email="<EMAIL>",
        password="adminpass123",
        first_name="Admin",
        last_name="User"
    )


@pytest.fixture
def user_profile_data():
    """Sample user profile data for testing."""
    return {
        "first_name": "Profile",
        "last_name": "User",
        "completed_questions": 5,
        "default_phone_number": "+1234567890",
        "default_street_address1": "123 Test St",
        "default_street_address2": "Apt 1",
        "default_town_or_city": "Test City",
        "default_county": "Test County",
        "default_postcode": "12345",
        "default_country": "US",
        "hair_type": "2A",
        "questionaire_completed": True
    }


@pytest.fixture
def user_profile(user, user_profile_data):
    """Create a test user profile."""
    return UserProfile.objects.create(user=user, **user_profile_data)


@pytest.fixture
def product():
    """Create a test product."""
    return Product.objects.create(
        name="Test Product",
        price=29.99,
        category="Shampoo",
        description="Test product description"
    )


@pytest.fixture
def user_image(user):
    """Create a test user image."""
    # Create a simple test image file
    image_file = SimpleUploadedFile(
        "test_image.jpg",
        b"fake image content",
        content_type="image/jpeg"
    )
    return UserImage.objects.create(user=user, image=image_file)


@pytest.fixture
def authenticated_client(api_client, user):
    """Provide an authenticated client."""
    api_client.force_login(user)
    return api_client


@pytest.fixture
def mock_email_backend():
    """Mock email backend for testing."""
    with patch('users.backends.EmailBackend') as mock:
        yield mock


@pytest.fixture
def mock_token_generator():
    """Mock token generator for password reset tests."""
    with patch('users.api.default_token_generator') as mock:
        yield mock


@pytest.fixture
def mock_logger():
    """Mock logger for testing."""
    with patch('users.api.logger') as mock:
        yield mock


@pytest.fixture
def register_data():
    """Sample registration data."""
    return {
        "email": "<EMAIL>",
        "password": "newpass123",
        "first_name": "New",
        "last_name": "User"
    }


@pytest.fixture
def login_data():
    """Sample login data."""
    return {
        "email": "<EMAIL>",
        "password": "testpass123"
    }


@pytest.fixture
def password_reset_data():
    """Sample password reset data."""
    return {
        "uid": "1",
        "token": "test-token",
        "password": "newpassword123"
    }


@pytest.fixture
def profile_create_data(product):
    """Sample profile creation data."""
    return {
        "user_id": 999,
        "first_name": "Created",
        "last_name": "Profile",
        "completed_questions": 3,
        "default_phone_number": "+9876543210",
        "default_street_address1": "456 Create St",
        "default_town_or_city": "Create City",
        "default_postcode": "54321",
        "default_country": "GB",
        "hair_type": "3B",
        "questionaire_completed": False,
        "recommendations": [product.id]
    }


# Database fixtures
@pytest.fixture(scope='function')
def django_db_setup(django_db_setup, django_db_blocker):
    """Set up test database."""
    with django_db_blocker.unblock():
        # Any additional database setup can go here
        pass


# Mock external dependencies
@pytest.fixture
def mock_recommender_module():
    """Mock RecommenderModule for testing."""
    with patch('users.api.RecommenderModule') as mock:
        yield mock


@pytest.fixture
def mock_reporter():
    """Mock Reporter for testing."""
    with patch('users.api.Reporter') as mock:
        yield mock
