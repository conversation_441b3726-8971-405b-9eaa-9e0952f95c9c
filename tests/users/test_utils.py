"""
Unit tests for users utility functions and edge cases.
"""

import pytest
import json
from unittest.mock import patch, Mock
from django.contrib.auth import get_user_model
from django.test import RequestFactory
from django.http import JsonResponse

from users.api import (
    password_reset_confirm,
    me,
    list_all_profiles,
    create_user_profile,
    get_user_recommendations,
    delete_user_profile,
    get_all_users,
    get_user_by_id,
    get_user_by_email,
    logout_view,
    signup,
    register
)

User = get_user_model()


@pytest.mark.django_db
class TestAPIUtilityFunctions:
    """Test utility functions and edge cases in API views."""

    def setup_method(self):
        """Set up test method."""
        self.factory = RequestFactory()

    def test_json_parsing_error_handling(self):
        """Test API functions handle JSON parsing errors."""
        request = self.factory.post(
            '/api/test',
            data="invalid json",
            content_type="application/json"
        )
        
        # Test password_reset_confirm with invalid JSON
        response = password_reset_confirm(request)
        assert response.status_code == 400
        response_data = json.loads(response.content)
        assert "error" in response_data

    def test_missing_request_body(self):
        """Test API functions handle missing request body."""
        request = self.factory.post('/api/test')
        
        response = password_reset_confirm(request)
        assert response.status_code == 400

    def test_empty_request_body(self):
        """Test API functions handle empty request body."""
        request = self.factory.post(
            '/api/test',
            data="{}",
            content_type="application/json"
        )
        
        response = password_reset_confirm(request)
        assert response.status_code == 400

    def test_malformed_json_request(self):
        """Test API functions handle malformed JSON."""
        request = self.factory.post(
            '/api/test',
            data='{"incomplete": json',
            content_type="application/json"
        )
        
        response = password_reset_confirm(request)
        assert response.status_code == 400

    def test_non_json_content_type(self):
        """Test API functions handle non-JSON content type."""
        request = self.factory.post(
            '/api/test',
            data="some data",
            content_type="text/plain"
        )
        
        response = password_reset_confirm(request)
        assert response.status_code == 400

    @patch('users.api.logger')
    def test_unexpected_exception_logging(self, mock_logger):
        """Test that unexpected exceptions are logged."""
        request = self.factory.post(
            '/api/test',
            data='{"uid": "1", "token": "test", "password": "test"}',
            content_type="application/json"
        )
        
        with patch('users.api.User.objects.get') as mock_get:
            mock_get.side_effect = Exception("Unexpected error")
            
            response = password_reset_confirm(request)
            assert response.status_code == 400
            mock_logger.error.assert_called()

    def test_get_request_to_post_endpoint(self):
        """Test GET request to POST-only endpoint."""
        request = self.factory.get('/api/test')
        
        # Most API endpoints expect POST, should handle GET gracefully
        response = password_reset_confirm(request)
        assert response.status_code == 400

    def test_put_request_handling(self):
        """Test PUT request handling in signup endpoint."""
        request = self.factory.put(
            '/api/signup',
            data=json.dumps({
                "email": "<EMAIL>",
                "password1": "putpass123",
                "password2": "putpass123",
                "first_name": "Put",
                "last_name": "User"
            }),
            content_type="application/json"
        )
        
        response = signup(request)
        assert response.status_code == 200

    def test_delete_request_handling(self, user_profile):
        """Test DELETE request handling."""
        request = self.factory.delete(f'/api/profiles/user/{user_profile.user.id}')
        
        response = delete_user_profile(request, user_profile.user.id)
        assert response.status_code == 200

    def test_options_request_handling(self):
        """Test OPTIONS request handling (CORS preflight)."""
        request = self.factory.options('/api/test')
        
        # Should handle OPTIONS gracefully
        response = password_reset_confirm(request)
        # Might return 400 or 405, but shouldn't crash
        assert response.status_code in [400, 405, 200]

    def test_head_request_handling(self):
        """Test HEAD request handling."""
        request = self.factory.head('/api/test')
        
        response = get_all_users(request)
        # Should handle HEAD gracefully
        assert response.status_code in [200, 405]


@pytest.mark.django_db
class TestDataValidationEdgeCases:
    """Test data validation edge cases."""

    def setup_method(self):
        """Set up test method."""
        self.factory = RequestFactory()

    def test_extremely_long_email(self):
        """Test handling of extremely long email addresses."""
        long_email = "a" * 300 + "@example.com"
        
        request = self.factory.post(
            '/api/register',
            data=json.dumps({
                "email": long_email,
                "password": "testpass123",
                "first_name": "Test",
                "last_name": "User"
            }),
            content_type="application/json"
        )
        
        response = register(request)
        # Should handle gracefully, likely with validation error
        assert response.status_code == 200
        response_data = json.loads(response.content)
        assert response_data["success"] is False

    def test_unicode_characters_in_names(self):
        """Test handling of Unicode characters in names."""
        request = self.factory.post(
            '/api/register',
            data=json.dumps({
                "email": "<EMAIL>",
                "password": "testpass123",
                "first_name": "José",
                "last_name": "Müller"
            }),
            content_type="application/json"
        )
        
        response = register(request)
        assert response.status_code == 200
        response_data = json.loads(response.content)
        assert response_data["success"] is True

    def test_special_characters_in_password(self):
        """Test handling of special characters in passwords."""
        request = self.factory.post(
            '/api/register',
            data=json.dumps({
                "email": "<EMAIL>",
                "password": "p@$$w0rd!#$%^&*()",
                "first_name": "Special",
                "last_name": "User"
            }),
            content_type="application/json"
        )
        
        response = register(request)
        assert response.status_code == 200

    def test_empty_string_fields(self):
        """Test handling of empty string fields."""
        request = self.factory.post(
            '/api/register',
            data=json.dumps({
                "email": "<EMAIL>",
                "password": "testpass123",
                "first_name": "",
                "last_name": ""
            }),
            content_type="application/json"
        )
        
        response = register(request)
        assert response.status_code == 200

    def test_null_values_in_json(self):
        """Test handling of null values in JSON."""
        request = self.factory.post(
            '/api/register',
            data=json.dumps({
                "email": "<EMAIL>",
                "password": "testpass123",
                "first_name": None,
                "last_name": None
            }),
            content_type="application/json"
        )
        
        response = register(request)
        assert response.status_code == 200

    def test_numeric_strings_in_text_fields(self):
        """Test handling of numeric strings in text fields."""
        request = self.factory.post(
            '/api/register',
            data=json.dumps({
                "email": "<EMAIL>",
                "password": "testpass123",
                "first_name": "123",
                "last_name": "456"
            }),
            content_type="application/json"
        )
        
        response = register(request)
        assert response.status_code == 200

    def test_boolean_values_in_string_fields(self):
        """Test handling of boolean values in string fields."""
        request = self.factory.post(
            '/api/register',
            data=json.dumps({
                "email": "<EMAIL>",
                "password": "testpass123",
                "first_name": True,
                "last_name": False
            }),
            content_type="application/json"
        )
        
        response = register(request)
        # Should handle type conversion or validation error
        assert response.status_code == 200


@pytest.mark.django_db
class TestConcurrencyAndRaceConditions:
    """Test concurrency and race condition scenarios."""

    def setup_method(self):
        """Set up test method."""
        self.factory = RequestFactory()

    def test_concurrent_user_creation(self):
        """Test concurrent user creation with same email."""
        email = "<EMAIL>"
        
        # Simulate two concurrent requests
        request1 = self.factory.post(
            '/api/register',
            data=json.dumps({
                "email": email,
                "password": "testpass123",
                "first_name": "First",
                "last_name": "User"
            }),
            content_type="application/json"
        )
        
        request2 = self.factory.post(
            '/api/register',
            data=json.dumps({
                "email": email,
                "password": "testpass123",
                "first_name": "Second",
                "last_name": "User"
            }),
            content_type="application/json"
        )
        
        # First request should succeed
        response1 = register(request1)
        assert response1.status_code == 200
        response1_data = json.loads(response1.content)
        assert response1_data["success"] is True
        
        # Second request should fail due to duplicate email
        response2 = register(request2)
        assert response2.status_code == 200
        response2_data = json.loads(response2.content)
        assert response2_data["success"] is False

    def test_profile_deletion_race_condition(self, user_profile):
        """Test profile deletion race condition."""
        user_id = user_profile.user.id
        
        # Simulate two concurrent deletion requests
        request1 = self.factory.delete(f'/api/profiles/user/{user_id}')
        request2 = self.factory.delete(f'/api/profiles/user/{user_id}')
        
        # First deletion should succeed
        response1 = delete_user_profile(request1, user_id)
        assert response1.status_code == 200
        response1_data = json.loads(response1.content)
        assert response1_data["success"] is True
        
        # Second deletion should handle missing profile gracefully
        response2 = delete_user_profile(request2, user_id)
        assert response2.status_code == 200
        response2_data = json.loads(response2.content)
        assert response2_data["success"] is False


@pytest.mark.django_db
class TestMemoryAndPerformance:
    """Test memory usage and performance edge cases."""

    def test_large_profile_data(self):
        """Test handling of large profile data."""
        large_address = "A" * 1000  # Very long address
        
        request = self.factory.post(
            '/api/profiles',
            data=json.dumps({
                "user_id": 999,
                "first_name": "Large",
                "last_name": "Data",
                "default_street_address1": large_address,
                "default_street_address2": large_address,
                "completed_questions": 10,
                "recommendations": []
            }),
            content_type="application/json"
        )
        
        response = create_user_profile(request)
        # Should handle large data gracefully
        assert response.status_code == 200

    def test_large_recommendations_list(self, product):
        """Test handling of large recommendations list."""
        # Create a large list of product IDs
        large_recommendations = [product.id] * 1000
        
        request = self.factory.post(
            '/api/profiles',
            data=json.dumps({
                "user_id": 999,
                "first_name": "Large",
                "last_name": "Recommendations",
                "completed_questions": 5,
                "recommendations": large_recommendations
            }),
            content_type="application/json"
        )
        
        response = create_user_profile(request)
        assert response.status_code == 200
