"""
Performance tests for Cosmetrics AI.

Tests database query optimization, caching, and API response times.
"""

import json
import time

import pytest
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db import connection
from django.test import Client, TestCase, TransactionTestCase
from django.test.utils import override_settings

from products.models import Category, Product
from recommendation.models import Recommendation
from services.performance import (
    CacheManager,
    QueryOptimizer,
    cache_result,
    get_optimized_products,
    get_user_recommendations_optimized,
    monitor_performance,
)
from users.models import UserProfile

User = get_user_model()


@pytest.mark.performance
class QueryOptimizationTests(TestCase):
    """Test database query optimization."""

    def setUp(self):
        """Set up test data."""
        # Create test users
        self.users = []
        for i in range(5):
            user = User.objects.create_user(
                email=f"user{i}@example.com",
                password="testpass123",
                first_name=f"User{i}",
                last_name="Test",
            )
            self.users.append(user)

        # Create test category
        self.category = Category.objects.create(
            name="Test Category", description="Test category description"
        )

        # Create test products
        self.products = []
        for i in range(10):
            product = Product.objects.create(
                name=f"Product {i}",
                price=10.00 + i,
                category=self.category,
                hairtype="curly",
                porosity="medium",
                texture="coarse",
            )
            self.products.append(product)

    def test_user_query_optimization(self):
        """Test that user queries are optimized to avoid N+1 problems."""
        # Clear any existing queries
        connection.queries_log.clear()

        # Get users with optimization
        queryset = User.objects.all()
        optimized_queryset = QueryOptimizer.optimize_user_queries(queryset)

        # Execute the query and access related data
        users_list = list(optimized_queryset)
        for user in users_list:
            # Access related data that should be prefetched
            _ = user.userprofile
            _ = list(user.userprofile.recommendations.all())

        # Check that we didn't make too many queries
        query_count = len(connection.queries)
        self.assertLess(query_count, 10, f"Too many queries: {query_count}")

    def test_product_query_optimization(self):
        """Test that product queries are optimized."""
        connection.queries_log.clear()

        # Get products with optimization
        queryset = Product.objects.all()
        optimized_queryset = QueryOptimizer.optimize_product_queries(queryset)

        # Execute the query and access related data
        products_list = list(optimized_queryset)
        for product in products_list:
            _ = product.category
            _ = list(product.recommended_users.all())

        # Check query count
        query_count = len(connection.queries)
        self.assertLess(query_count, 8, f"Too many queries: {query_count}")

    def test_userprofile_query_optimization(self):
        """Test UserProfile query optimization."""
        connection.queries_log.clear()

        queryset = UserProfile.objects.all()
        optimized_queryset = QueryOptimizer.optimize_userprofile_queries(queryset)

        profiles_list = list(optimized_queryset)
        for profile in profiles_list:
            _ = profile.user
            _ = list(profile.recommendations.all())

        query_count = len(connection.queries)
        self.assertLess(query_count, 6, f"Too many queries: {query_count}")


@pytest.mark.performance
class CacheTests(TestCase):
    """Test caching functionality."""

    def setUp(self):
        """Set up test data."""
        cache.clear()
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

    def test_cache_manager_user_data(self):
        """Test caching user data."""
        test_data = {"name": "Test User", "preferences": ["pref1", "pref2"]}

        # Cache the data
        CacheManager.cache_user_data(self.user.id, test_data)

        # Retrieve from cache
        cached_data = CacheManager.get_user_data(self.user.id)
        self.assertEqual(cached_data, test_data)

    def test_cache_manager_product_list(self):
        """Test caching product lists with filters."""
        filters = {"category": "shampoo", "price_max": 50}
        test_data = [{"id": 1, "name": "Product 1"}]

        # Cache the data
        CacheManager.cache_product_list(filters, test_data)

        # Retrieve from cache
        cached_data = CacheManager.get_product_list(filters)
        self.assertEqual(cached_data, test_data)

    def test_cache_manager_recommendations(self):
        """Test caching recommendations."""
        test_recommendations = [{"product_id": 1, "score": 0.95}]

        # Cache recommendations
        CacheManager.cache_recommendations(self.user.id, test_recommendations)

        # Retrieve from cache
        cached_recs = CacheManager.get_recommendations(self.user.id)
        self.assertEqual(cached_recs, test_recommendations)

    def test_cache_invalidation(self):
        """Test cache invalidation."""
        test_data = {"test": "data"}

        # Cache some data
        CacheManager.cache_user_data(self.user.id, test_data)
        CacheManager.cache_recommendations(self.user.id, test_data)

        # Verify data is cached
        self.assertIsNotNone(CacheManager.get_user_data(self.user.id))
        self.assertIsNotNone(CacheManager.get_recommendations(self.user.id))

        # Invalidate cache
        CacheManager.invalidate_user_cache(self.user.id)

        # Verify data is removed
        self.assertIsNone(CacheManager.get_user_data(self.user.id))
        self.assertIsNone(CacheManager.get_recommendations(self.user.id))

    def test_cache_result_decorator(self):
        """Test the cache_result decorator."""
        call_count = 0

        @cache_result(timeout=60, key_prefix="test")
        def expensive_function(x, y):
            nonlocal call_count
            call_count += 1
            return x + y

        # First call should execute the function
        result1 = expensive_function(1, 2)
        self.assertEqual(result1, 3)
        self.assertEqual(call_count, 1)

        # Second call should use cache
        result2 = expensive_function(1, 2)
        self.assertEqual(result2, 3)
        self.assertEqual(call_count, 1)  # Should not increment

        # Different arguments should execute function again
        result3 = expensive_function(2, 3)
        self.assertEqual(result3, 5)
        self.assertEqual(call_count, 2)


@pytest.mark.performance
class PerformanceMonitoringTests(TestCase):
    """Test performance monitoring functionality."""

    def test_monitor_performance_decorator(self):
        """Test the performance monitoring decorator."""

        @monitor_performance
        def fast_function():
            return "fast"

        @monitor_performance
        def slow_function():
            time.sleep(0.1)  # Simulate slow operation
            return "slow"

        # Test fast function
        result1 = fast_function()
        self.assertEqual(result1, "fast")

        # Test slow function
        result2 = slow_function()
        self.assertEqual(result2, "slow")

    def test_monitor_performance_with_exception(self):
        """Test performance monitoring with exceptions."""

        @monitor_performance
        def failing_function():
            raise ValueError("Test error")

        with self.assertRaises(ValueError):
            failing_function()


@pytest.mark.performance
class APIPerformanceTests(TransactionTestCase):
    """Test API endpoint performance."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create test user
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

        # Create test category and products
        self.category = Category.objects.create(name="Performance Test Category")

        # Create multiple products for performance testing
        self.products = []
        for i in range(50):  # Create 50 products
            product = Product.objects.create(
                name=f"Performance Product {i}",
                price=10.00 + i,
                category=self.category,
                hairtype="curly" if i % 2 == 0 else "straight",
                porosity="low" if i % 3 == 0 else "medium",
                texture="fine" if i % 4 == 0 else "coarse",
            )
            self.products.append(product)

    def test_products_api_performance(self):
        """Test products API response time."""
        start_time = time.time()

        response = self.client.get("/api/products/")

        end_time = time.time()
        response_time = end_time - start_time

        # API should respond within 1 second
        self.assertLess(
            response_time, 1.0, f"API response too slow: {response_time:.3f}s"
        )
        self.assertEqual(response.status_code, 200)

    def test_products_api_with_filters_performance(self):
        """Test products API with filters performance."""
        start_time = time.time()

        response = self.client.get("/api/products/?hairtype=curly&porosity=medium")

        end_time = time.time()
        response_time = end_time - start_time

        # Filtered API should also respond quickly
        self.assertLess(
            response_time, 1.0, f"Filtered API response too slow: {response_time:.3f}s"
        )
        self.assertEqual(response.status_code, 200)

    def test_users_api_performance(self):
        """Test users API performance."""
        start_time = time.time()

        response = self.client.get("/api/users/")

        end_time = time.time()
        response_time = end_time - start_time

        self.assertLess(
            response_time, 1.0, f"Users API response too slow: {response_time:.3f}s"
        )
        self.assertEqual(response.status_code, 200)


@pytest.mark.performance
class OptimizedFunctionTests(TestCase):
    """Test optimized service functions."""

    def setUp(self):
        """Set up test data."""
        cache.clear()

        # Create test category
        self.category = Category.objects.create(name="Optimized Test Category")

        # Create test products
        for i in range(10):
            Product.objects.create(
                name=f"Optimized Product {i}",
                price=15.00 + i,
                category=self.category,
                hairtype="wavy",
                porosity="high",
                texture="medium",
            )

    def test_get_optimized_products_performance(self):
        """Test optimized products function performance."""
        start_time = time.time()

        # First call should hit database
        products1 = get_optimized_products()

        first_call_time = time.time() - start_time

        start_time = time.time()

        # Second call should use cache
        products2 = get_optimized_products()

        second_call_time = time.time() - start_time

        # Verify results are the same
        self.assertEqual(len(products1), len(products2))

        # Second call should be significantly faster (cached)
        self.assertLess(second_call_time, first_call_time)
        self.assertLess(second_call_time, 0.1)  # Should be very fast from cache

    def test_get_optimized_products_with_filters(self):
        """Test optimized products function with filters."""
        filters = {"hairtype": "wavy", "price_min": 20}

        products = get_optimized_products(filters)

        # Verify filtering worked
        for product in products:
            self.assertEqual(product["hairtype"], "wavy")
            self.assertGreaterEqual(product["price"], 20)


@pytest.mark.performance
@override_settings(
    CACHES={
        "default": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            "LOCATION": "test-cache",
        }
    }
)
class CacheIntegrationTests(TransactionTestCase):
    """Integration tests for caching with real data."""

    def test_cache_integration_with_real_queries(self):
        """Test cache integration with real database queries."""
        # Create test data
        user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

        category = Category.objects.create(name="Cache Test Category")

        products = []
        for i in range(5):
            product = Product.objects.create(
                name=f"Cache Product {i}", price=25.00 + i, category=category
            )
            products.append(product)

        # Test caching with real data
        filters = {"category": category.id}

        # First call - should cache the result
        start_time = time.time()
        result1 = get_optimized_products(filters)
        first_time = time.time() - start_time

        # Second call - should use cache
        start_time = time.time()
        result2 = get_optimized_products(filters)
        second_time = time.time() - start_time

        # Verify results are identical
        self.assertEqual(len(result1), len(result2))
        self.assertEqual(result1, result2)

        # Second call should be faster
        self.assertLess(second_time, first_time)
