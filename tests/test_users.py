"""
Comprehensive test suite for the users app.

Tests the custom User model, UserProfile, authentication, and API endpoints.
"""

import json

import pytest
from django.contrib.auth import authenticate, get_user_model
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.test import Client, TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from users.backends import EmailBackend
from users.models import UserImage, UserProfile

User = get_user_model()


@pytest.mark.unit
class UserModelTests(TestCase):
    """Test the custom User model functionality."""

    def setUp(self):
        """Set up test data."""
        self.user_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User",
        }

    def test_create_user_with_email(self):
        """Test creating a user with email."""
        user = User.objects.create_user(**self.user_data)

        self.assertEqual(user.email, self.user_data["email"])
        self.assertEqual(user.first_name, self.user_data["first_name"])
        self.assertEqual(user.last_name, self.user_data["last_name"])
        self.assertTrue(user.check_password(self.user_data["password"]))
        self.assertIsNone(user.username)
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)

    def test_create_user_without_email(self):
        """Test that creating a user without email raises ValueError."""
        with self.assertRaises(ValueError):
            User.objects.create_user(email="", password="testpass")

    def test_create_superuser(self):
        """Test creating a superuser."""
        admin_data = {"email": "<EMAIL>", "password": "adminpass123"}
        admin = User.objects.create_superuser(**admin_data)

        self.assertEqual(admin.email, admin_data["email"])
        self.assertTrue(admin.is_staff)
        self.assertTrue(admin.is_superuser)
        self.assertTrue(admin.is_active)

    def test_create_superuser_without_is_staff(self):
        """Test that creating superuser without is_staff raises ValueError."""
        with self.assertRaises(ValueError):
            User.objects.create_superuser(
                email="<EMAIL>", password="adminpass", is_staff=False
            )

    def test_create_superuser_without_is_superuser(self):
        """Test that creating superuser without is_superuser raises ValueError."""
        with self.assertRaises(ValueError):
            User.objects.create_superuser(
                email="<EMAIL>", password="adminpass", is_superuser=False
            )

    def test_email_uniqueness(self):
        """Test that email must be unique."""
        User.objects.create_user(**self.user_data)

        with self.assertRaises(IntegrityError):
            User.objects.create_user(
                email=self.user_data["email"], password="anotherpass"
            )

    def test_user_string_representation(self):
        """Test the string representation of User."""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(str(user), self.user_data["email"])

    def test_user_full_name_property(self):
        """Test the full_name property."""
        user = User.objects.create_user(**self.user_data)
        expected_name = f"{self.user_data['first_name']} {self.user_data['last_name']}"
        self.assertEqual(user.full_name, expected_name)

        # Test with empty names
        user_no_names = User.objects.create_user(
            email="<EMAIL>", password="testpass"
        )
        self.assertEqual(user_no_names.full_name, "<EMAIL>")


@pytest.mark.unit
class EmailBackendTests(TestCase):
    """Test the custom email authentication backend."""

    def setUp(self):
        """Set up test data."""
        self.backend = EmailBackend()
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
        )

    def test_authenticate_with_email(self):
        """Test authentication with email."""
        user = self.backend.authenticate(
            None, email="<EMAIL>", password="testpass123"
        )
        self.assertEqual(user, self.user)

    def test_authenticate_with_username_as_email(self):
        """Test authentication with username parameter containing email."""
        user = self.backend.authenticate(
            None, username="<EMAIL>", password="testpass123"
        )
        self.assertEqual(user, self.user)

    def test_authenticate_with_wrong_password(self):
        """Test authentication with wrong password."""
        user = self.backend.authenticate(
            None, email="<EMAIL>", password="wrongpass"
        )
        self.assertIsNone(user)

    def test_authenticate_with_nonexistent_email(self):
        """Test authentication with non-existent email."""
        user = self.backend.authenticate(
            None, email="<EMAIL>", password="testpass123"
        )
        self.assertIsNone(user)

    def test_authenticate_without_email(self):
        """Test authentication without email."""
        user = self.backend.authenticate(None, password="testpass123")
        self.assertIsNone(user)

    def test_get_user(self):
        """Test getting user by ID."""
        retrieved_user = self.backend.get_user(self.user.id)
        self.assertEqual(retrieved_user, self.user)

    def test_get_nonexistent_user(self):
        """Test getting non-existent user."""
        user = self.backend.get_user(99999)
        self.assertIsNone(user)


@pytest.mark.unit
class UserProfileTests(TestCase):
    """Test UserProfile model and signals."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

    def test_userprofile_created_automatically(self):
        """Test that UserProfile is created automatically when User is created."""
        self.assertTrue(hasattr(self.user, "userprofile"))
        self.assertIsInstance(self.user.userprofile, UserProfile)

    def test_userprofile_string_representation(self):
        """Test UserProfile string representation."""
        expected_str = f"{self.user.email} - Profile"
        self.assertEqual(str(self.user.userprofile), expected_str)

    def test_userprofile_fields(self):
        """Test UserProfile fields and defaults."""
        profile = self.user.userprofile

        # Test default values
        self.assertEqual(profile.completed_questions, 0)
        self.assertFalse(profile.questionaire_completed)
        self.assertEqual(profile.first_name, "")
        self.assertEqual(profile.last_name, "")

    def test_userprofile_update(self):
        """Test updating UserProfile fields."""
        profile = self.user.userprofile
        profile.first_name = "Updated"
        profile.last_name = "Name"
        profile.completed_questions = 5
        profile.questionaire_completed = True
        profile.save()

        # Refresh from database
        profile.refresh_from_db()

        self.assertEqual(profile.first_name, "Updated")
        self.assertEqual(profile.last_name, "Name")
        self.assertEqual(profile.completed_questions, 5)
        self.assertTrue(profile.questionaire_completed)


@pytest.mark.api
class UserAPITests(APITestCase):
    """Test User API endpoints."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.user_data = {
            "email": "<EMAIL>",
            "password": "testpass123",
            "first_name": "Test",
            "last_name": "User",
        }
        self.user = User.objects.create_user(**self.user_data)

    def test_user_registration(self):
        """Test user registration endpoint."""
        registration_data = {
            "email": "<EMAIL>",
            "password": "newpass123",
            "first_name": "New",
            "last_name": "User",
        }

        response = self.client.post(
            "/api/users/register",
            data=json.dumps(registration_data),
            content_type="application/json",
        )

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertTrue(response_data.get("success"))

        # Verify user was created
        self.assertTrue(User.objects.filter(email=registration_data["email"]).exists())

    def test_user_registration_duplicate_email(self):
        """Test registration with duplicate email."""
        registration_data = {
            "email": self.user_data["email"],  # Duplicate email
            "password": "newpass123",
        }

        response = self.client.post(
            "/api/users/register",
            data=json.dumps(registration_data),
            content_type="application/json",
        )

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertFalse(response_data.get("success"))
        self.assertIn("already exists", response_data.get("message", ""))

    def test_user_login(self):
        """Test user login endpoint."""
        login_data = {
            "email": self.user_data["email"],
            "password": self.user_data["password"],
        }

        response = self.client.post(
            "/api/users/login",
            data=json.dumps(login_data),
            content_type="application/json",
        )

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertTrue(response_data.get("success"))
        self.assertIn("access", response_data)
        self.assertIn("refresh", response_data)
        self.assertIn("user", response_data)

    def test_user_login_invalid_credentials(self):
        """Test login with invalid credentials."""
        login_data = {"email": self.user_data["email"], "password": "wrongpassword"}

        response = self.client.post(
            "/api/users/login",
            data=json.dumps(login_data),
            content_type="application/json",
        )

        self.assertEqual(response.status_code, 401)


@pytest.mark.integration
class UserIntegrationTests(TestCase):
    """Integration tests for user-related functionality."""

    def test_user_creation_with_profile_and_recommendations(self):
        """Test complete user creation flow with profile and recommendations."""
        # Create user
        user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Integration",
            last_name="Test",
        )

        # Verify profile was created
        self.assertTrue(hasattr(user, "userprofile"))

        # Update profile
        profile = user.userprofile
        profile.hair_type = "curly"
        profile.completed_questions = 10
        profile.questionaire_completed = True
        profile.save()

        # Verify updates
        profile.refresh_from_db()
        self.assertEqual(profile.hair_type, "curly")
        self.assertEqual(profile.completed_questions, 10)
        self.assertTrue(profile.questionaire_completed)

        # Test authentication
        authenticated_user = authenticate(
            email="<EMAIL>", password="testpass123"
        )
        self.assertEqual(authenticated_user, user)
