"""
Integration tests for API endpoints.

This module contains comprehensive integration tests that verify the complete
user journey and API contract compliance.
"""

import uuid
from unittest.mock import patch

from helpers.test_base import APITestCase, TestDataFactory
from questionaire.models import Question, Reply
from recommendation.models import Recommendation
from users.models import User


class UserJourneyIntegrationTest(APITestCase):
    """
    Integration tests for complete user journeys.

    Tests the full flow from user registration to getting recommendations.
    """

    def test_complete_user_journey(self):
        """Test the complete user journey from registration to recommendations."""

        # Step 1: User registration
        registration_data = {
            "username": "journeyuser",
            "email": "<EMAIL>",
            "password": "securepass123",
        }

        response = self.client.post("/api/users/register", json=registration_data)
        self.assertResponseSuccess(response, 201)

        user = User.objects.get(username="journeyuser")
        self.authenticate_user(user)

        # Step 2: Get questionnaire
        response = self.client.get("/api/quiz/questions")
        self.assertResponseSuccess(response)
        questions_data = response.json()
        self.assertGreater(len(questions_data), 0)

        # Step 3: Submit questionnaire responses
        session_guid = str(uuid.uuid4())
        replies_data = []

        for question_data in questions_data[:5]:  # Answer first 5 questions
            if question_data.get("answers"):
                reply_data = {
                    "question_id": question_data["id"],
                    "answer_id": question_data["answers"][0]["id"],
                    "session_guid": session_guid,
                    "text": f"Answer to question {question_data['id']}",
                }
                replies_data.append(reply_data)

        for reply_data in replies_data:
            response = self.client.post("/api/quiz/replies", json=reply_data)
            self.assertResponseSuccess(response, 201)

        # Step 4: Generate recommendations
        with patch(
            "recommendation.services.RecommenderModule.generate_recommendations"
        ) as mock_reco:
            mock_reco.return_value = {
                "shampoos": ["Test Shampoo 1", "Test Shampoo 2"],
                "conditioners": ["Test Conditioner 1", "Test Conditioner 2"],
            }

            response = self.client.post(f"/api/recommendations/generate/{session_guid}")
            self.assertResponseSuccess(response, 201)

            recommendations = response.json()
            self.assertIn("shampoos", recommendations)
            self.assertIn("conditioners", recommendations)

        # Step 5: Generate hair analysis report
        with patch("reporter.services.Reporter.create_report") as mock_report:
            mock_report.return_value = {
                "dry_score_percentage": {"value": 75.0, "description": "Dry"},
                "damage_score_percentage": {"value": 60.0, "description": "Damage"},
                "sensitivity_percentage": {"value": 40.0, "description": "Sensitivity"},
            }

            response = self.client.post(f"/api/reports/sessions/{session_guid}")
            self.assertResponseSuccess(response)

            report = response.json()
            self.assertIn("dry_score_percentage", report)

        # Step 6: Verify user profile is updated
        response = self.client.get(f"/api/users/profiles/user/{user.id}")
        self.assertResponseSuccess(response)

        profile = response.json()
        self.assertGreater(profile["completed_questions"], 0)


class APIContractTest(APITestCase):
    """
    Tests for API contract compliance.

    Verifies that API responses match expected schemas and status codes.
    """

    def test_user_endpoints_contract(self):
        """Test user API endpoints contract compliance."""

        # Test user list endpoint
        response = self.client.get("/api/users/")
        self.assertResponseSuccess(response)
        users_data = response.json()
        self.assertIsInstance(users_data, list)

        # Test user detail endpoint
        response = self.client.get(f"/api/users/id/{self.test_user.id}")
        self.assertResponseSuccess(response)
        user_data = response.json()
        self.assertIn("id", user_data)
        self.assertIn("username", user_data)
        self.assertIn("email", user_data)

        # Test user not found
        response = self.client.get("/api/users/id/99999")
        self.assertResponseError(response, 404)

    def test_questionnaire_endpoints_contract(self):
        """Test questionnaire API endpoints contract compliance."""

        # Test questions list
        response = self.client.get("/api/quiz/questions")
        self.assertResponseSuccess(response)
        questions = response.json()
        self.assertIsInstance(questions, list)

        if questions:
            question = questions[0]
            self.assertIn("id", question)
            self.assertIn("title", question)
            self.assertIn("position", question)

    def test_error_response_format(self):
        """Test that error responses follow consistent format."""

        # Test 404 error
        response = self.client.get("/api/users/id/99999")
        self.assertEqual(response.status_code, 404)

        # Test validation error
        invalid_data = {"username": "", "email": "invalid", "password": ""}
        response = self.client.post("/api/users/register", json=invalid_data)
        self.assertResponseError(response, 422)


class PerformanceIntegrationTest(APITestCase):
    """
    Performance tests for API endpoints.

    Tests response times and database query efficiency.
    """

    def test_user_list_performance(self):
        """Test user list endpoint performance."""

        # Create multiple users
        for i in range(50):
            User.objects.create_user(
                username=f"perfuser{i}",
                email=f"perfuser{i}@example.com",
                password="testpass123",
            )

        # Test query count
        with self.assertMaxQueries(3):  # Should be efficient
            response = self.client.get("/api/users/")
            self.assertResponseSuccess(response)

    def test_questionnaire_with_replies_performance(self):
        """Test questionnaire performance with many replies."""

        # Create user with many replies
        user = TestDataFactory.create_user_with_complete_profile("perfuser")
        questionaire, replies = TestDataFactory.create_questionnaire_with_replies(user)

        self.authenticate_user(user)

        # Test performance of getting user replies
        with self.assertMaxQueries(5):  # Should be efficient with proper joins
            response = self.client.get(f"/api/quiz/replies/user/{user.id}")
            self.assertResponseSuccess(response)


class SecurityIntegrationTest(APITestCase):
    """
    Security tests for API endpoints.

    Tests authentication, authorization, and input validation.
    """

    def test_authentication_required_endpoints(self):
        """Test that protected endpoints require authentication."""

        # Test without authentication
        response = self.client.post(
            "/api/quiz/replies",
            json={"question_id": 1, "answer_id": 1, "session_guid": "test"},
        )
        self.assertResponseError(response, 401)

        # Test with authentication
        self.authenticate_user()
        response = self.client.get("/api/users/profiles/")
        self.assertResponseSuccess(response)

    def test_authorization_user_data_access(self):
        """Test that users can only access their own data."""

        # Create two users
        user1 = TestDataFactory.create_user_with_complete_profile("user1")
        user2 = TestDataFactory.create_user_with_complete_profile("user2")

        # Authenticate as user1
        self.authenticate_user(user1)

        # Try to access user2's profile (should be allowed for admin endpoints)
        response = self.client.get(f"/api/users/profiles/user/{user2.id}")
        # This might be allowed depending on your authorization logic
        # Adjust based on your actual requirements

        # Test accessing own profile
        response = self.client.get(f"/api/users/profiles/user/{user1.id}")
        self.assertResponseSuccess(response)

    def test_input_validation(self):
        """Test input validation for API endpoints."""

        self.authenticate_user()

        # Test invalid email format
        invalid_data = {
            "username": "testuser",
            "email": "invalid-email",
            "password": "validpass123",
        }
        response = self.client.post("/api/users/register", json=invalid_data)
        self.assertResponseError(response, 422)

        # Test SQL injection attempt
        malicious_data = {
            "username": "'; DROP TABLE users; --",
            "email": "<EMAIL>",
            "password": "validpass123",
        }
        response = self.client.post("/api/users/register", json=malicious_data)
        # Should either succeed with escaped data or fail validation
        self.assertIn(response.status_code, [201, 422])


class DataIntegrityTest(APITestCase):
    """
    Tests for data integrity and consistency.

    Verifies that API operations maintain data consistency.
    """

    def test_questionnaire_reply_consistency(self):
        """Test that questionnaire replies maintain consistency."""

        self.authenticate_user()
        session_guid = str(uuid.uuid4())

        # Submit replies
        questions = Question.objects.filter(questionaire=self.test_questionaire)[:3]
        for question in questions:
            answer = question.answers.first()
            reply_data = {
                "question_id": question.id,
                "answer_id": answer.id,
                "session_guid": session_guid,
                "text": f"Reply to {question.title}",
            }

            response = self.client.post("/api/quiz/replies", json=reply_data)
            self.assertResponseSuccess(response, 201)

        # Verify replies are stored correctly
        replies = Reply.objects.filter(user=self.test_user, session_guid=session_guid)
        self.assertEqual(replies.count(), 3)

        # Verify all replies have the same session_guid
        session_guids = set(replies.values_list("session_guid", flat=True))
        self.assertEqual(len(session_guids), 1)

    def test_recommendation_generation_consistency(self):
        """Test that recommendation generation is consistent."""

        user = TestDataFactory.create_user_with_complete_profile()
        questionaire, replies = TestDataFactory.create_questionnaire_with_replies(user)

        self.authenticate_user(user)
        session_guid = replies[0].session_guid

        with patch(
            "recommendation.services.RecommenderModule.generate_recommendations"
        ) as mock_reco:
            mock_reco.return_value = {
                "shampoos": ["Shampoo A", "Shampoo B"],
                "conditioners": ["Conditioner A", "Conditioner B"],
            }

            # Generate recommendations
            response = self.client.post(f"/api/recommendations/generate/{session_guid}")
            self.assertResponseSuccess(response, 201)

            # Verify recommendation is stored
            recommendation = Recommendation.objects.get(
                user=user, session_guid=session_guid
            )
            self.assertEqual(len(recommendation.shampoos), 2)
            self.assertEqual(len(recommendation.conditioners), 2)

            # Generate again - should update existing
            response = self.client.post(f"/api/recommendations/generate/{session_guid}")
            self.assertResponseSuccess(response, 201)

            # Should still have only one recommendation
            recommendations = Recommendation.objects.filter(
                user=user, session_guid=session_guid
            )
            self.assertEqual(recommendations.count(), 1)
