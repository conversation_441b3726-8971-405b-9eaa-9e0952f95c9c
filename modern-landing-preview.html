<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Landing Page Variant - Preview</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4300FF;
            --secondary-color: #FF6B9D;
            --green-color: #10B981;
            --white-color: #FFFFFF;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1F2937;
        }

        /* Hero Section */
        .hero-section {
            min-height: 100vh;
            background: linear-gradient(135deg, 
                rgba(67, 0, 255, 0.08) 0%, 
                rgba(255, 107, 157, 0.06) 50%, 
                #FFFFFF 100%
            );
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 100%;
            height: 200%;
            background: radial-gradient(circle, rgba(67, 0, 255, 0.05) 0%, transparent 70%);
            animation: float 20s ease-in-out infinite;
        }

        .hero-section::after {
            content: '';
            position: absolute;
            bottom: -30%;
            left: -10%;
            width: 80%;
            height: 150%;
            background: radial-gradient(circle, rgba(255, 107, 157, 0.04) 0%, transparent 60%);
            animation: float 25s ease-in-out infinite reverse;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            line-height: 1.1;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            color: #4B5563;
            margin-bottom: 2rem;
            font-weight: 400;
            line-height: 1.6;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .stats-container {
            display: flex;
            justify-content: center;
            gap: 3rem;
            margin: 3rem 0;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-color);
            display: block;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6B7280;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .cta-button {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            padding: 1rem 3rem;
            font-size: 1.125rem;
            font-weight: 600;
            border-radius: 50px;
            box-shadow: 0 10px 30px rgba(67, 0, 255, 0.3);
            transition: all 0.3s ease;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(67, 0, 255, 0.4);
            color: white;
            text-decoration: none;
        }

        /* Features Section */
        .features-section {
            padding: 6rem 0;
            background: var(--white-color);
        }

        .section-title {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title h2 {
            font-size: 3rem;
            font-weight: 800;
            color: #1F2937;
            margin-bottom: 1rem;
        }

        .section-title p {
            font-size: 1.25rem;
            color: #6B7280;
            max-width: 600px;
            margin: 0 auto;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .feature-card {
            background: var(--white-color);
            border-radius: 24px;
            padding: 2.5rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid #F3F4F6;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
            border-color: rgba(67, 0, 255, 0.2);
        }

        .icon-wrapper {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            background: linear-gradient(135deg, rgba(67, 0, 255, 0.08) 0%, rgba(255, 107, 157, 0.08) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            border: 2px solid rgba(67, 0, 255, 0.1);
            font-size: 2rem;
            color: var(--primary-color);
        }

        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: #6B7280;
            line-height: 1.6;
            margin: 0;
        }

        /* Trust Section */
        .trust-section {
            padding: 4rem 0;
            background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
        }

        .trust-indicators {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 4rem;
            flex-wrap: wrap;
            margin-top: 3rem;
        }

        .trust-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: #374151;
            font-weight: 600;
        }

        .trust-item .icon {
            color: var(--green-color);
            font-size: 1.5rem;
        }

        /* Preview Header */
        .preview-header {
            background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);
            color: white;
            padding: 1rem;
            text-align: center;
            font-size: 0.875rem;
            font-weight: 600;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.25rem;
            }
            
            .stats-container {
                flex-direction: column;
                gap: 1.5rem;
            }
            
            .section-title h2 {
                font-size: 2rem;
            }
            
            .trust-indicators {
                gap: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Preview Header -->
    <div class="preview-header">
        🎨 DESIGN PREVIEW: Modern Landing Page Variant - Not Yet Implemented
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Discover Your Hair's<br>
                    Perfect Match
                </h1>
                <p class="hero-subtitle">
                    Revolutionary AI technology meets expert knowledge to give you personalized hair care recommendations that actually work. No more guessing, no more wasted money.
                </p>
                
                <div class="stats-container">
                    <div class="stat-item">
                        <span class="stat-number">50K+</span>
                        <span class="stat-label">Happy Users</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">1000+</span>
                        <span class="stat-label">Products Analyzed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">95%</span>
                        <span class="stat-label">Accuracy Rate</span>
                    </div>
                </div>
                
                <a href="#" class="cta-button">
                    <i class="fas fa-sparkles"></i>
                    Start Your Free Analysis
                    <i class="fas fa-arrow-right"></i>
                </a>
                
                <div style="margin-top: 2rem; color: #6B7280; font-size: 0.875rem;">
                    ✨ Free forever • No credit card required • Results in 5 minutes
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="section-title">
                <h2>How It Works</h2>
                <p>
                    Our cutting-edge technology makes hair care simple and effective. 
                    Get professional-grade analysis from the comfort of your home.
                </p>
            </div>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="icon-wrapper">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>AI-Powered Analysis</h3>
                    <p>Advanced machine learning algorithms analyze your hair photos to provide precise condition assessment and personalized recommendations.</p>
                </div>
                
                <div class="feature-card">
                    <div class="icon-wrapper">
                        <i class="fas fa-camera"></i>
                    </div>
                    <h3>Photo Diagnostic</h3>
                    <p>Simply upload 3 clear photos of your hair. Our technology examines texture, damage, dryness, and scalp health in seconds.</p>
                </div>
                
                <div class="feature-card">
                    <div class="icon-wrapper">
                        <i class="fas fa-award"></i>
                    </div>
                    <h3>Expert Recommendations</h3>
                    <p>Get curated product suggestions from our database of 1000+ hair care products, matched specifically to your hair's needs.</p>
                </div>
                
                <div class="feature-card">
                    <div class="icon-wrapper">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Progress Tracking</h3>
                    <p>Monitor your hair health journey over time with detailed reports and see how your hair improves with the right products.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Trust Section -->
    <section class="trust-section">
        <div class="container">
            <div class="section-title">
                <h2>Trusted by Hair Care Enthusiasts</h2>
                <p>Join thousands who have transformed their hair care routine with our AI-powered recommendations.</p>
            </div>
            
            <div class="trust-indicators">
                <div class="trust-item">
                    <i class="fas fa-shield-alt icon"></i>
                    <span>100% Secure & Private</span>
                </div>
                <div class="trust-item">
                    <i class="fas fa-star icon"></i>
                    <span>Expert Approved</span>
                </div>
                <div class="trust-item">
                    <i class="fas fa-users icon"></i>
                    <span>50,000+ Happy Users</span>
                </div>
                <div class="trust-item">
                    <i class="fas fa-heart icon"></i>
                    <span>Free Forever</span>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
