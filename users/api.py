from django.conf import settings
from django.contrib.auth import authenticate, get_user_model, logout
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth.tokens import default_token_generator
from django.core.exceptions import ValidationError
from django.core.mail import send_mail
from django.db import transaction
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.views.decorators.csrf import ensure_csrf_cookie
from loguru import logger
from ninja import File, Router
from ninja.files import UploadedFile
from ninja.security import django_auth
from ninja_jwt.tokens import RefreshToken

from helpers.api_error_handling import (
    crud_endpoint,
    handle_api_error,
    list_endpoint,
)

# Import shared schemas
from helpers.schemas import MessageOut, RegisterResponseSchema
from products.models import Product
from questionaire.models import Question, Reply
from recommendation.services import RecommenderModule
from reporter.services import Reporter

from .models import UserProfile
from .schemas import (
    CreateUserProfileSchema,
    CurrentUserSchema,
    LoginSchema,
    PasswordResetConfirmSchema,
    PasswordResetRequestSchema,
    RegisterSchema,
    ReportSchema,
    UserIn,
    UserOut,
    UserOutSchema,
    UserProfileCreateSchema,
    UserProfileInSchema,
    UserProfileOutSchema,
    UserProfileSchema,
    UserProfileStatusOutSchema,
    UserSchema,
)

User = get_user_model()


router = Router()
reporter = Reporter()
recommender = RecommenderModule()


@router.post("/password-reset", auth=None)
def request_password_reset(request, data: PasswordResetRequestSchema):
    user = User.objects.filter(email=data.email).first()
    if user:
        uid = user.id
        token = default_token_generator.make_token(user)
        reset_link = f"{settings.FRONTEND_URL}/reset-password/{uid}-{token}"

        try:
            send_mail(
                "Password Reset Request",
                f"Click to reset your password:\n{reset_link}",
                settings.DEFAULT_FROM_EMAIL,
                [data.email],
                fail_silently=False,
            )
        except Exception as e:
            logger.error(f"Failed to send password reset: {e}")

    return JsonResponse(
        {"message": "If an account with this email exists, a reset link has been sent."}
    )


@router.post("/password-reset/confirm", auth=None)
def reset_password(request, data: PasswordResetConfirmSchema):
    try:
        user = get_object_or_404(User, pk=data.uid)
        if default_token_generator.check_token(user, data.token):
            user.set_password(data.password)
            user.save()
            return JsonResponse({"message": "Password reset successful!"})
        return JsonResponse({"error": "Invalid or expired token"}, status=400)
    except Exception as e:
        logger.error(f"Reset error: {e}")
        return JsonResponse({"error": "Invalid request"}, status=400)


@router.get("/me", response=CurrentUserSchema, auth=django_auth)
def me(request):
    logger.info(request.user)
    return request.user


@router.get(
    "/profiles/check/all", response=list[UserProfileOutSchema], tags=["Profiles"]
)
def get_all_profiles(request):
    return [
        UserProfileOutSchema.from_django(profile)
        for profile in UserProfile.objects.all()
    ]


@router.get(
    "/profiles/user/{int:user_id}", response=UserProfileOutSchema, tags=["Profiles"]
)
def get_user_profile(request, user_id: int):
    profile = get_object_or_404(UserProfile, user__id=user_id)
    return UserProfileOutSchema.from_django(profile)


@router.post("/profiles", response=UserProfileCreateSchema, tags=["Profiles"])
@transaction.atomic
def create_user_profile(request, payload: UserProfileCreateSchema):
    user = User.objects.create_user(email=f"user_{payload.user_id}@temp.com")
    data = payload.to_django_model()
    user_profile = UserProfile.objects.create(user=user, **data)

    for product_id in payload.recommendations:
        product = Product.objects.get(id=product_id)
        user_profile.recommendations.add(product)

    return UserProfileOutSchema.from_django(user_profile)


@router.get(
    "/profiles/recommendations/user/{int:user_id}",
    response=list[int],
    tags=["Profiles"],
)
def get_user_recommendations_only(request, user_id: int):
    user_profile = get_object_or_404(UserProfile, user_id=user_id)
    return list(user_profile.recommendations.values_list("id", flat=True))


@router.get(
    "/profiles/status",
    response=list[UserProfileStatusOutSchema],
    tags=["Profiles"],
)
def get_profiles_with_status(request, complete: bool = False):
    return [
        UserProfileStatusOutSchema.from_orm(profile)
        for profile in UserProfile.objects.filter(questionaire_completed=complete)
    ]


@router.get(
    "/profiles/user/{int:user_id}/quiz-status/",
    response=list[UserProfileStatusOutSchema],
    tags=["Profiles"],
)
def get_user_profile_status(request, user_id: int):
    return [
        UserProfileStatusOutSchema.from_orm(profile)
        for profile in UserProfile.objects.filter(user_id=user_id)
    ]


@router.post("/profiles", response=UserProfileOutSchema)
def create_profile(request, data: CreateUserProfileSchema):
    user = get_object_or_404(User, id=data.user_id)
    profile = UserProfile.objects.create(
        user=user,
        first_name=data.first_name,
        last_name=data.last_name,
        completed_questions=data.completed_questions,
        default_phone_number=data.default_phone_number,
        default_street_address1=data.default_street_address1,
        default_street_address2=data.default_street_address2,
        default_town_or_city=data.default_town_or_city,
        default_county=data.default_county,
        default_postcode=data.default_postcode,
        default_country=data.default_country,
        hair_type=data.hair_type,
        questionaire_completed=data.questionaire_completed,
    )
    if data.recommendations:
        profile.recommendations.set(Product.objects.filter(id__in=data.recommendations))
    return UserProfileOutSchema.from_django(profile)


@router.put(
    "/profiles/user/{int:user_id}",
    response={200: UserProfileOutSchema, 404: MessageOut, 500: MessageOut},
)
def update_profile(request, user_id: int, data: CreateUserProfileSchema):
    try:
        profile = get_object_or_404(UserProfile, user__id=user_id)
        for attr, value in data.items():
            if attr == "recommendations":
                profile.recommendations.set(Product.objects.filter(id__in=value))
            elif attr == "user_id":
                continue
            else:
                logger.info(f"Setting attribute {attr} to {value}")
                setattr(profile, attr, value)
        profile.save()
        return UserProfileOutSchema.from_django(profile)
    except Exception as e:
        logger.error(f"Failed to update user profile: {e}")
        return None


@router.delete("/profiles/user/{int:user_id}", response={"success": bool})
def delete_profile(request, user_id: int):
    try:
        profile = get_object_or_404(UserProfile, user__id=user_id)
        profile.delete()
        return {"success": True}
    except Exception as e:
        logger.error(f"Failed to delete user profile: {e}")
        return {"success": False}


@router.get("/")
@list_endpoint(list[UserSchema])
@handle_api_error
def get_all_users(request):
    """Get all users with standardized error handling and optimized queries."""
    # Optimize query to avoid N+1 problems
    users = User.objects.select_related().prefetch_related("userprofile")
    return [UserSchema.from_orm(user) for user in users]


@router.get("/id/{int:user_id}")
@crud_endpoint(UserOutSchema)
@handle_api_error
def get_user_by_id(request, user_id: int):
    """Get user by ID with standardized error handling."""
    user = get_object_or_404(User, pk=user_id)
    return UserOutSchema.from_orm(user)


@router.get("/email/{str:email}")
@crud_endpoint(UserOutSchema)
@handle_api_error
def get_user_by_email(request, email: str):
    """Get user by email with standardized error handling."""
    user = get_object_or_404(User, email=email)
    return UserOutSchema.from_orm(user)


@router.post("/login", auth=None, response=UserOutSchema)
def login(request, data: LoginSchema):
    # Handle backward compatibility: use username as email if email is not provided
    email = data.email or data.username
    if not email:
        return JsonResponse(
            {"success": False, "message": "Email is required"}, status=400
        )

    user = authenticate(request, email=email, password=data.password)
    if not user:
        return JsonResponse(
            {"success": False, "message": "Invalid credentials"}, status=401
        )

    refresh = RefreshToken.for_user(user)
    return JsonResponse(
        {
            "success": True,
            "access": str(refresh.access_token),
            "refresh": str(refresh),
            "user": {
                "id": user.id,
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
            },
        }
    )


@router.post("/logout", auth=django_auth)
def logout_view(request):
    logout(request)
    return JsonResponse({"message": "Logged out successfully"}, status=200)


@router.put("/signup", response=UserOut, auth=None)
@transaction.atomic
def signup(request, data: UserIn):
    return User.objects.create_user(
        email=data.email,
        password=data.password1,
        first_name=data.first_name,
        last_name=data.last_name,
    )


@router.post("/register", response=RegisterResponseSchema, auth=None)
@transaction.atomic
def register_user(request, payload: RegisterSchema) -> dict:
    if User.objects.filter(email=payload.email).exists():
        return {"success": False, "message": "Email already exists"}
    try:
        validate_password(payload.password)
    except ValidationError as e:
        return {"success": False, "message": " ".join(e.messages)}

    User.objects.create_user(
        email=payload.email,
        password=payload.password,
        first_name=getattr(payload, "first_name", ""),
        last_name=getattr(payload, "last_name", ""),
    )
    return {"success": True, "message": "User registered successfully"}


@router.put("/profiles/{int:user_id}", response=UserProfileOutSchema, tags=["Profiles"])
@transaction.atomic
def update_user_profile(request, user_id: int, payload: UserProfileSchema):
    user_profile = get_object_or_404(UserProfile, user_id=user_id)
    data = payload.to_django_model()

    for key, value in data.items():
        setattr(user_profile, key, value)

    user_profile.recommendations.set(
        Product.objects.filter(id__in=payload.recommendations)
    )
    user_profile.save()
    return UserProfileSchema.from_django(user_profile)


@router.delete(
    "/profiles/{int:user_id}", auth=django_auth, response={204: None}, tags=["Profiles"]
)
def delete_user_profile(request, user_id: int):
    user_profile = get_object_or_404(UserProfile, user_id=user_id)
    user_profile.delete()
    return 204


@router.get(
    "/id/{int:user_id}/session/{str:session_guid}/scores",
    response={200: ReportSchema, 404: MessageOut, 500: MessageOut},
    tags=["Scores"],
)
def get_score_by_user_id(request, user_id: int, session_guid: str):
    try:
        report = reporter.create_report(user_id, session_guid)
        data = report
        return 200, data
    except Reply.DoesNotExist as e:
        return 404, {"message": str(e)}
    except Exception as e:
        return 500, {"message": str(e)}


@router.get(
    "/id/{int:user_id}/questions/{int:question_id}/scores",
    response={200: dict, 404: MessageOut, 500: MessageOut},
    tags=["Answers", "Scores"],
)
def get_user_score_for_question(request, user_id: int, question_id: int):
    user = get_object_or_404(User, id=user_id)
    question = get_object_or_404(Question, id=question_id)
    score = reporter.get_answer_score(user, question.position)
    logger.info(f"\n\tQuestion Score: {score}\n")
    return {"score": score}


@router.get(
    "/id/{int:user_id}/session/{str:session_guid}/questions/{int:question_id}/scores",
    response={200: dict, 400: MessageOut, 404: MessageOut, 500: MessageOut},
    tags=["Answers", "Scores"],
)
def get_user_score_for_session_question(
    request, user_id: int, question_id: int, session_guid: str
):
    logger.info("\tGetting score for session user\n")
    user = get_object_or_404(User, id=user_id)
    question = get_object_or_404(Question, id=question_id)
    score = reporter.get_answer_score(
        user, question.position, session_guid=session_guid
    )
    return {"score": score}


# ──────────────────────────────────────────────
# Helpers
# ──────────────────────────────────────────────
def _save_m2m(profile: UserProfile, product_ids: list[int]) -> None:
    qset = Product.objects.filter(id__in=product_ids)
    profile.recommendations.set(qset)


# ──────────────────────────────────────────────
# End‑points
# ──────────────────────────────────────────────


@router.get("/", response=list[UserProfileOutSchema])
def list_profiles(request):
    """Return every profile (consider pagination for large sets)."""
    logger.warning("list_profiles: getting user profiles for all\n")
    profiles = UserProfile.objects.select_related("user").prefetch_related(
        "recommendations"
    )
    return [UserProfileOutSchema.from_django(profile) for profile in profiles]


@router.get("/{profile_id}", response=UserProfileOutSchema)
def get_profile(request, profile_id: int):
    logger.warning("get_profile: getting user profile for id: %s\n", profile_id)
    profile = get_object_or_404(UserProfile, id=profile_id)
    return UserProfileOutSchema.from_django(profile)


@router.post("/", response=UserProfileOutSchema)
def create_profile_single(
    request,
    payload: UserProfileInSchema,
    hair_image: UploadedFile = File(None),  # multipart/form‑data for file upload
):
    user = get_object_or_404(User, id=payload.user_id)
    profile = UserProfile.objects.create(
        user=user,
        first_name=payload.first_name,
        last_name=payload.last_name,
        completed_questions=payload.completed_questions,
        default_phone_number=payload.default_phone_number,
        default_street_address1=payload.default_street_address1,
        default_street_address2=payload.default_street_address2,
        default_town_or_city=payload.default_town_or_city,
        default_county=payload.default_county,
        default_postcode=payload.default_postcode,
        default_country=payload.default_country,
        hair_type=payload.hair_type,
        questionaire_completed=payload.questionaire_completed,
        hair_image=hair_image,  # will be None if not supplied
    )
    _save_m2m(profile, payload.recommendations)
    return UserProfileOutSchema.from_django(profile)


# @router.put("/id/{profile_id}", response=UserProfileOutSchema)
# @router.put("/id/{profile_id}")
def update_profile_by_id(
    request,
    profile_id: int,
    payload: UserProfileInSchema,
    hair_image: UploadedFile = File(None),
):
    profile = get_object_or_404(UserProfile, id=profile_id)

    # core fields
    for field, value in payload.dict().items():
        if field != "recommendations":
            setattr(profile, field, value)

    if hair_image is not None:
        profile.hair_image = hair_image

    profile.save()
    _save_m2m(profile, payload.recommendations)
    return profile


@router.delete("/id/{profile_id}", response={204: None})
def delete_profile_by_id(request, profile_id: int):
    profile = get_object_or_404(UserProfile, id=profile_id)
    profile.delete()
    return 204, None
