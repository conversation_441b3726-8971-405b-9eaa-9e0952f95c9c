from django import forms
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.password_validation import validate_password

User = get_user_model()
from django.core.exceptions import ValidationError

from .models import UserProfile


class BaseForm(forms.ModelForm):
    """Base form class to apply common attributes to form fields."""

    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        for field in self.fields.values():
            field.widget.attrs.update({"class": "form-control"})


class UserProfileForm(BaseForm):
    class Meta:
        model = UserProfile
        fields = [
            "first_name",
            "last_name",
            "default_phone_number",
            "default_street_address1",
            "default_street_address2",
            "default_town_or_city",
            "default_county",
            "default_postcode",
            "default_country",
        ]


class UserLoginForm(forms.Form):
    username = forms.CharField(widget=forms.TextInput(attrs={"class": "form-control"}))
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={"class": "form-control"}),
    )


class UserRegisterForm(UserCreationForm):
    first_name = forms.CharField(
        max_length=150,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": "First Name"}
        ),
    )
    last_name = forms.CharField(
        max_length=150,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": "Last Name"}
        ),
    )

    class Meta:
        model = User
        fields = ["email", "first_name", "last_name", "password1", "password2"]
        widgets = {
            "email": forms.EmailInput(
                attrs={"class": "form-control", "placeholder": "Email Address"}
            ),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add styling to password fields
        self.fields["password1"].widget.attrs.update(
            {"class": "form-control", "placeholder": "Password"}
        )
        self.fields["password2"].widget.attrs.update(
            {"class": "form-control", "placeholder": "Confirm Password"}
        )

    def clean_email(self):
        email = self.cleaned_data.get("email")
        if User.objects.filter(email=email).exists():
            msg = "This email is already taken."
            raise forms.ValidationError(msg)
        return email

    def clean_password2(self):
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")

        if password1 and password2 and password1 != password2:
            msg = "Passwords must match."
            raise forms.ValidationError(msg)

        try:
            validate_password(password1)
        except ValidationError as error:
            msg = f"Password is not valid: {' '.join(error)}"
            raise forms.ValidationError(msg)

        return password2


class PasswordResetForm(forms.Form):
    email = forms.EmailField(
        label="Enter your email",
        max_length=254,
        widget=forms.EmailInput(attrs={"class": "form-control"}),
    )


# from django import forms
# from django.contrib.auth.forms import UserCreationForm
# from django.contrib.auth.models import User
# from django.contrib.auth.password_validation import validate_password
# from django.core.exceptions import ValidationError
# from .models import UserProfile
#
#
# class UserProfileForm(forms.ModelForm):
#     class Meta:
#         model = UserProfile
#         fields = [
#             "first_name",
#             "last_name",
#             "default_phone_number",
#             "default_street_address1",
#             "default_street_address2",
#             "default_town_or_city",
#             "default_county",
#             "default_postcode",
#             "default_country",
#         ]
#         widgets = {
#             field: forms.TextInput(attrs={"class": "input_field"}) for field in fields
#         }
#
#
# class UserLoginForm(forms.Form):
#     username = forms.CharField(widget=forms.TextInput(attrs={"class": "input_field"}))
#     password = forms.CharField(
#         widget=forms.PasswordInput(attrs={"class": "input_field"})
#     )
#
#
# class UserRegisterForm(UserCreationForm):
#     def clean(self):
#         cleaned_data = super().clean()
#         password1 = cleaned_data.get("password1")
#         password2 = cleaned_data.get("password2")
#
#         # check if the passwords match
#         if password1 and password2 and password1 != password2:
#             raise forms.ValidationError("Password and Confirm Password must match")
#         return cleaned_data
#
#     class Meta:
#         model = User
#         fields = ["email", "username", "password1", "password2"]
#
#     # email = forms.EmailField(widget=forms.EmailInput(attrs={"class": "input_field"}))
#     # username = forms.CharField(widget=forms.TextInput(attrs={"class": "input_field"}))
#     # password = forms.CharField(
#     #     widget=forms.PasswordInput(attrs={"class": "input_field"})
#     # )
#     # password_confirmation = forms.CharField(
#     #     widget=forms.PasswordInput(attrs={"class": "input_field"})
#     # )
#
#     def clean_username(self):
#         username = self.cleaned_data.get("username")
#         if User.objects.filter(username=username).exists():
#             raise forms.ValidationError("This username is already taken.")
#         return username
#
#     def clean_email(self):
#         email = self.cleaned_data.get("email")
#         if User.objects.filter(email=email).exists():
#             raise forms.ValidationError("This email is already taken.")
#         return email
#
#     def clean_password_confirmation(self):
#         cleaned_data = self.cleaned_data
#         password = cleaned_data.get("password1")
#         password_confirmation = cleaned_data.get("password2")
#
#         if password != password_confirmation:
#             raise forms.ValidationError("Password and Confirm Password must be equal")
#
#         try:
#             validate_password(password)
#         except ValidationError as error:
#             raise forms.ValidationError(f"Password is not valid: {' '.join(error)}")
#
#         return password_confirmation
#
#
# class PasswordResetForm(forms.Form):
#     email = forms.EmailField(
#         label="Enter your email",
#         max_length=254,
#         widget=forms.EmailInput(attrs={"class": "input_field"}),
#     )
