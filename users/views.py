import pandas as pd
from django.contrib import messages
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth import views as auth_views
from django.contrib.auth.decorators import login_required
from django.shortcuts import redirect, render
from django.urls import reverse_lazy
from loguru import logger

# Import new base views and mixins
from helpers.base_views import (
    BaseDashboardView,
    BaseLoginView,
    BaseRegisterView,
)

# Import consolidated constants
from helpers.constants import HAIR_ID_QUESTIONS_CORE, QuestionPositions
from questionaire.models import Question, Reply
from src.report.reporter import Reporter

from .forms import PasswordResetForm, UserLoginForm, UserProfileForm, UserRegisterForm
from .models import UserProfile


@login_required
def profile(request):
    user_profile = UserProfile.objects.select_related("user").get(
        user=request.user,
    )  # Use select_related if user is accessed in the template
    if request.method == "POST":
        form = UserProfileForm(request.POST, instance=user_profile)
        if form.is_valid():
            form.save()
            messages.success(request, "Profile updated successfully.")
            return redirect(
                "profile",
            )  # Redirect after POST to prevent form resubmission
        messages.error(request, "Update failed. Please ensure the form is valid.")
    else:
        form = UserProfileForm(instance=user_profile)

    return render(request, "users/profile.html", {"form": form})


@login_required
def report(request):
    replies = (
        Reply.objects.filter(user=request.user)
        .select_related("question", "answer")
        .order_by("id")
    )  # Assuming Reply has relations with Question and Answer
    return render(request, "users/report.html", {"replies": replies})


@login_required
def hair_id(request):
    user_profile = UserProfile.objects.select_related("user").get(user=request.user)
    context = {"error": "Questionnaire not completed yet"}

    if user_profile.completed_questions >= Question.objects.count():
        replies = Reply.objects.filter(
            user=request.user,
            question__position__in=HAIR_ID_QUESTIONS_CORE,
        ).select_related("answer")

        # Map question positions to reply values
        reply_map = {reply.question.position: reply.answer.value for reply in replies}

        context = {
            "hair_type": reply_map.get(QuestionPositions.HAIR_TYPE),
            "hair_texture": reply_map.get(QuestionPositions.HAIR_TEXTURE),
            "porosity": reply_map.get(QuestionPositions.HAIR_POROSITY),
            "hair_density": reply_map.get(QuestionPositions.HAIR_DENSITY),
        }

    return render(request, "users/hair_id.html", context)


@login_required
def recommendation(request):
    user_profile = UserProfile.objects.prefetch_related("recommendations").get(
        user=request.user,
    )  # Prefetch recommendations if frequently accessed
    if user_profile.completed_questions < Question.objects.count():
        context = {
            "error": "Can't get recommendations if questionnaire is not completed.",
        }
    else:
        recommendations = user_profile.recommendations.all()
        context = (
            {"recommendations": recommendations}
            if recommendations
            else {
                "error": "Recommendations are being generated, please refresh the page in a couple of seconds.",
            }
        )

    return render(request, "users/recommendation.html", context)


def sign_in_try(request):
    if request.method == "POST":
        username = request.POST["username"]
        password = request.POST["password"]

        user = authenticate(username=username, password=password)

        if user is not None:
            login(request, user)
            fname = user.first_name
            return render(request, "index.html", {"fname": fname})

        messages.error(request, "Bad credentials")
        return redirect("home")

    return render(request, "users/login.html")


# Use the new base login view
class LoginView(BaseLoginView):
    """Login view using the standardized base class."""

    form_class = UserLoginForm
    success_url = "/users/dashboard/"


# Create function-based view for backward compatibility
def sign_in(request):
    """Function-based wrapper for the class-based login view."""
    view = LoginView.as_view()
    return view(request)


# Use the new base dashboard view
class DashboardView(BaseDashboardView):
    """Dashboard view using the standardized base class."""

    pass


# Create function-based view for backward compatibility
@login_required
def dashboard(request):
    """Function-based wrapper for the class-based dashboard view."""
    view = DashboardView.as_view()
    return view(request)


@login_required
def sign_out(request):
    logger.info("LOGout called...!\n")
    # Use a guard clause to handle the POST method and logout directly
    # if request.method != "POST":
    #     return redirect("homepage")

    logout(request)
    return redirect("login")


# Use the new base registration view
class RegisterView(BaseRegisterView):
    """Registration view using the standardized base class."""

    form_class = UserRegisterForm

    def get_form_data(self, form):
        """Extract form data for user creation."""
        return {
            "username": form.cleaned_data["username"],
            "password": form.cleaned_data["password1"],
            "email": form.cleaned_data["email"],
        }


# Create function-based view for backward compatibility
def register(request):
    """Function-based wrapper for the class-based registration view."""
    view = RegisterView.as_view()
    return view(request)


@login_required
def condition_score(request):
    user_profile = UserProfile.objects.get(user=request.user)
    if user_profile.completed_questions >= Question.objects.count():
        reporter = Reporter()
        report = reporter.create_report(request.user.id)
        context = {"report": report}
    else:
        context = {"error": "Questionnaire has not been completed yet."}

    return render(request, "users/condition_score.html", context)


def load_contextual_report(request) -> None:
    raw_df = (
        pd.read_csv()
    )  # Add the file path or necessary parameters for reading the CSV
    raw_df.info()


class CustomPasswordResetView(auth_views.PasswordResetView):
    template_name = "registration/password_reset_form.html"
    email_template_name = "registration/password_reset_email.html"
    subject_template_name = "registration/password_reset_subject.txt"
    success_url = reverse_lazy("password_reset_done")
    form_class = PasswordResetForm
