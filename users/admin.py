from django.contrib import admin
from django.contrib.auth import get_user_model
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin

# Import base admin classes to reduce duplication
from .models import UserImage, UserProfile

User = get_user_model()


class UserAdmin(BaseUserAdmin):
    """Custom admin for the custom User model."""

    # Fields to display in the user list
    list_display = (
        "email",
        "first_name",
        "last_name",
        "is_staff",
        "is_active",
        "date_joined",
    )
    list_filter = ("is_staff", "is_superuser", "is_active", "date_joined")
    search_fields = ("email", "first_name", "last_name")
    ordering = ("email",)

    # Fields for the user detail/edit form
    fieldsets = (
        (None, {"fields": ("email", "password")}),
        ("Personal info", {"fields": ("first_name", "last_name")}),
        (
            "Permissions",
            {
                "fields": (
                    "is_active",
                    "is_staff",
                    "is_superuser",
                    "groups",
                    "user_permissions",
                )
            },
        ),
        ("Important dates", {"fields": ("last_login", "date_joined")}),
    )

    # Fields for the add user form
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "email",
                    "password1",
                    "password2",
                    "first_name",
                    "last_name",
                ),
            },
        ),
    )


# Register your models here.


class UserImageAdmin(admin.ModelAdmin):
    """
    Admin configuration for UserImage model.
    Inherits user-related functionality from UserRelatedModelAdmin.
    """

    list_display = ("user", "image")
    search_fields = ["user__email", "user__first_name", "user__last_name"]


class UserProfileAdmin(admin.ModelAdmin):
    """
    Admin configuration for UserProfile model.
    Inherits user-related functionality from UserRelatedModelAdmin.
    """

    list_display = ("user", "first_name", "last_name", "completed_questions")
    list_filter = ("questionaire_completed",)
    search_fields = [
        "user__email",
        "user__first_name",
        "user__last_name",
        "first_name",
        "last_name",
    ]


admin.site.register(User, UserAdmin)
admin.site.register(UserImage, UserImageAdmin)
admin.site.register(UserProfile, UserProfileAdmin)
