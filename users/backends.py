"""
Custom authentication backends for email-based authentication.
"""

from django.contrib.auth import get_user_model
from django.contrib.auth.backends import ModelBackend
from django.db.models import Q

User = get_user_model()


class EmailBackend(ModelBackend):
    """
    Custom authentication backend that allows users to log in using their email address.

    This backend supports both email and username authentication for backward compatibility,
    but primarily focuses on email-based authentication.
    """

    def authenticate(self, request, username=None, password=None, email=None, **kwargs):
        """
        Authenticate a user using email or username.

        Args:
            request: The HTTP request object
            username: Username (for backward compatibility) or email
            password: User's password
            email: User's email address
            **kwargs: Additional keyword arguments

        Returns:
            User object if authentication successful, None otherwise
        """
        if email is None and username is not None:
            # If email is not provided but username is, treat username as email
            email = username

        if email is None:
            return None

        try:
            # Try to get user by email
            user = User.objects.get(Q(email__iexact=email))
        except User.DoesNotExist:
            # Run the default password hasher once to reduce the timing
            # difference between an existing and a nonexistent user
            User().set_password(password)
            return None

        if user.check_password(password) and self.user_can_authenticate(user):
            return user

        return None

    def get_user(self, user_id):
        """
        Get user by ID.

        Args:
            user_id: The user's primary key

        Returns:
            User object if found, None otherwise
        """
        try:
            user = User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None

        return user if self.user_can_authenticate(user) else None
