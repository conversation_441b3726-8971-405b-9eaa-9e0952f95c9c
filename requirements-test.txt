# Testing dependencies for Cosmetrics AI Users API

# Core testing framework
pytest>=7.4.0
pytest-django>=4.5.2
pytest-cov>=4.1.0
pytest-mock>=3.11.1
pytest-xdist>=3.3.1

# Coverage reporting
coverage>=7.2.7

# Test utilities
factory-boy>=3.3.0
faker>=19.3.0
freezegun>=1.2.2
responses>=0.23.1

# Performance testing
pytest-benchmark>=4.0.0
pytest-timeout>=2.1.0

# Mocking and fixtures
pytest-factoryboy>=2.5.1

# HTML reporting
pytest-html>=3.2.0

# Test data generation
model-bakery>=1.12.0

# API testing
requests-mock>=1.11.0

# Code quality (for CI)
flake8>=6.0.0
black>=23.7.0
isort>=5.12.0

# Django testing utilities
django-test-plus>=2.2.0

# Environment management
python-dotenv>=1.0.0

# JSON schema validation
jsonschema>=4.19.0

# Image testing (for UserImage model)
Pillow>=10.0.0

# Phone number testing
phonenumbers>=8.13.19

# Country code testing
django-countries>=7.5.1

# Authentication testing
PyJWT>=2.8.0

# Cryptography for token testing
cryptography>=41.0.3

# Data validation testing
pydantic>=2.1.1
