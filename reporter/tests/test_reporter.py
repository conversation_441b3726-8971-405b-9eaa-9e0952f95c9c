import pytest
from django.contrib.auth import get_user_model

from reporter import Reporter
from users.models import UserProfile

User = get_user_model()

from .questionaire.models import Answer, Question, Reply


@pytest.mark.django_db
def test_reporter_creates_valid_report():
    user = User.objects.create_user(email="<EMAIL>", password="test")
    UserProfile.objects.create(user=user, completed_questions=15)

    # Create mock questions and answers
    positions = list(Reporter.QUESTION_POSITIONS.values())
    for pos in positions:
        question = Question.objects.create(position=pos, text=f"Q{pos}")
        answer = Answer.objects.create(score=3)
        Reply.objects.create(user=user, question=question, answer=answer)

    report = Reporter().create_report(user.id)

    assert "dry_score_percentage" in report
    assert report["dry_score_percentage"]["value"] > 0
    assert report["dry_score_percentage"]["description"] == "Dry"
