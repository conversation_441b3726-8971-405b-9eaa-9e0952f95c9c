from django.db import transaction
from loguru import logger
from ninja import Router
from ninja.errors import HttpError

# Import shared schemas
from helpers.schemas import MessageOut
from questionaire.models import Question, Reply
from reporter.models import HairScoreReport

# # from reporter.services import get_answer_score
from .schemas import ReportOut
from .services import Reporter

router: Router = Router(tags=["001Reports"])
reporter: Reporter = Reporter()


@router.post(
    "/sessions/{str:session_guid}",
    response={200: ReportOut, 400: MessageOut, 404: MessageOut, 500: MessageOut},
    tags=["001Reports"],
)
def generate_report(request, session_guid: str):
    """Generate (and persist) a hair‑metrics report for the given questionnaire session."""
    # 1) Pull all replies for this session in one query, including answer + question meta.

    try:
        replies = Reply.objects.filter(session_guid=session_guid).select_related(
            "answer", "question", "user"
        )
    except Exception as e:
        logger.error(f"Error fetching replies for session {session_guid}: {e}")
        raise HttpError(500, "Error fetching replies")

    logger.info(f"\n\tFrom API in Report: Replies: {list(replies)}\n")

    if not replies.exists():
        raise HttpError(404, "Session not found")

    user = replies[0].user  # every reply in a session belongs to the same user

    logger.info(f"\n\tFrom API in Report: USER: {user}\n")

    # 2) Make sure the questionnaire is complete.
    total_questions = Question.objects.count()
    if replies.count() < total_questions:
        raise HttpError(
            400,
            f"questionnaire not completed - {replies.count()}/{total_questions} questions answered",
        )

    # 3) Build a quick lookup {position: score}
    logger.info(f"{replies.count()} replies found for session {session_guid}")
    logger.info(f"Replies in Raw format: {replies.values()}")
    # TODO: reply does now contain answers. NEVER!  Refactor such that Reply contains Answer.
    scores: dict[str, str] = {
        r.question.position: r.answer for r in replies if r.answer is not None
    }

    logger.info(f"Scores: {scores}")

    # 4) Pull out the individual scores we care about.

    issue: int = reporter.get_answer_score(user, 2, session_guid)
    hair_overall: int = reporter.get_answer_score(user, 3, session_guid)
    split_ends: int = reporter.get_answer_score(user, 7, session_guid)
    daily_water: int = reporter.get_answer_score(user, 24, session_guid)
    hair_feels: int = reporter.get_answer_score(user, 30, session_guid)
    treatments: int = reporter.get_answer_score(user, 34, session_guid)
    enhancers: int = reporter.get_answer_score(user, 35, session_guid)
    hair_behaviour: int = reporter.get_answer_score(user, 37, session_guid)
    styling_hair: int = reporter.get_answer_score(user, 38, session_guid)
    apply_heat: int = reporter.get_answer_score(user, 39, session_guid)
    scalp_feeling: int = reporter.get_answer_score(user, 41, session_guid)
    scalp_flaky: int = reporter.get_answer_score(
        user, 42, session_guid
    )  # this does not appear to be correct.
    oily_scalp: int = reporter.get_answer_score(user, 43, session_guid)
    dry_scalp: int = reporter.get_answer_score(user, 44, session_guid)

    # 5) Calculate aggregate indices.
    dry_score: int = (
        issue
        + split_ends
        + hair_overall
        + daily_water
        + hair_feels
        + scalp_flaky
        + dry_scalp
    )
    dry_score_max: int = 13
    dry_score_percentage: float = min(dry_score, dry_score_max) / dry_score_max

    damage_score: int = (
        split_ends + treatments + enhancers + hair_behaviour + styling_hair + apply_heat
    )
    damage_score_max = 15
    damage_score_percentage: float = (
        min(damage_score, damage_score_max) / damage_score_max
    )

    sensitivity_score: float = scalp_feeling
    sensitivity_score_max: float = 4.4
    sensitivity_percentage: float = (
        min(sensitivity_score, sensitivity_score_max) / sensitivity_score_max
    )

    sebum_oily_score: float = oily_scalp
    sebum_oily_score_max = 4.2
    sebum_oily_percentage: float = (
        min(sebum_oily_score, sebum_oily_score_max) / sebum_oily_score_max
    )

    sebum_dry_score: float = dry_scalp
    sebum_dry_score_max: float = 4.2
    sebum_dry_percentage: float = (
        min(sebum_dry_score, sebum_dry_score_max) / sebum_dry_score_max
    )

    dsc_percentage = sebum_dry_score / sebum_dry_score_max if sebum_dry_score_max else 0

    flake_score: float = scalp_flaky
    flake_score_max = 4.1
    flake_score_percentage: float = min(flake_score, flake_score_max) / flake_score_max

    # 6) Assemble the payload
    data: dict[str, dict[str, float | str]] = {
        "dry_score_percentage": {
            "value": round(dry_score_percentage * 100, 2),
            "description": "Dry",
        },
        "damage_score_percentage": {
            "value": round(damage_score_percentage * 100, 2),
            "description": "Damage",
        },
        "sensitivity_percentage": {
            "value": round(sensitivity_percentage * 100, 2),
            "description": "Sensitivity",
        },
        "sebum_oily_percentage": {
            "value": round(sebum_oily_percentage * 100, 2),
            "description": "Sebum Oily",
        },
        "sebum_dry_percentage": {
            "value": round(sebum_dry_percentage * 100, 2),
            "description": "Sebum Dry",
        },
        "dsc_percentage": {
            "value": round(dsc_percentage * 100, 2),
            "description": "Dry Scalp",
        },
        "flake_score_percentage": {
            "value": round(flake_score_percentage * 100, 2),
            "description": "Flake",
        },
    }

    # 7) Persist / upsert into the reports table in a single transaction
    @transaction.atomic
    def _update_report():
        logger.info(f"\n\tUpdating report for session {session_guid}\n")
        HairScoreReport.objects.update_or_create(
            session_guid=session_guid,
            defaults={
                "user": user,
                "data": data,  # JSONField so you can version later without schema changes
            },
        )

    _update_report()

    return data
