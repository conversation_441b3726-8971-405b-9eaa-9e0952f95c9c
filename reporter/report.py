# api/report.py
from django.db import transaction
from ninja import Router, Schema
from ninja.errors import HttpError
from replies.models import Reply
from reports.models import HairScoreReport

from questionaire.models import Question
from services.scores import HairScoreEngine

router = Router(tags=["reports"])


class Score(Schema):
    value: float
    description: str


class ReportOut(Schema):
    dry_score_percentage: Score
    damage_score_percentage: Score
    sensitivity_percentage: Score
    sebum_oily_percentage: Score
    sebum_dry_percentage: Score
    dsc_percentage: Score
    flakes_score_percentage: Score  # alias example


@router.post("/reports/{session_guid}", response=ReportOut)
def generate_report(request, session_guid: str):
    user = request.user
    if not user.is_authenticated:
        raise HttpError(401, "Authentication required")

    # Grab ALL replies for that session in one query
    replies = Reply.objects.filter(user=user, session_guid=session_guid).select_related(
        "answer", "question"
    )

    if replies.count() != Question.objects.count():
        raise HttpError(400, "Questionnaire not complete for this session")

    data = HairScoreEngine.compute(replies)

    with transaction.atomic():
        HairScoreReport.objects.update_or_create(
            user=user, session_guid=session_guid, defaults={"data": data}
        )

    return data  # Ninja → Pydantic → JSON
