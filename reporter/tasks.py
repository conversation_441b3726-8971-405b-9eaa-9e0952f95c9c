from celery import shared_task
from django.core.mail import EmailMessage
from django.template.loader import render_to_string
from django.utils.timezone import now
from weasyprint import HTML

from .models import HairScoreReport


@shared_task
def email_pdf_report(session_guid: str):
    try:
        report = HairScoreReport.objects.select_related("user").get(
            session_guid=session_guid
        )
        if report.emailed:
            return "Already emailed"

        html = render_to_string("report_template.html", {"report": report})
        pdf_file = HTML(string=html).write_pdf()

        email = EmailMessage(
            subject="Your Hair Score Report",
            body="Please find attached your PDF hair score report.",
            to=[report.user.email],
        )
        email.attach(
            f"hair_report_{report.session_guid}.pdf", pdf_file, "application/pdf"
        )
        email.send()

        report.emailed = True
        report.emailed_at = now()
        report.save()
        return "Email sent successfully"
    except HairScoreReport.DoesNotExist:
        return "Report not found"
