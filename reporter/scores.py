# services/scores.py
from __future__ import annotations

from django.db.models import QuerySet

from questionaire.models import Reply


class HairScoreEngine:
    """
    Pure‑function style engine.
    Call `compute(replies)` with a queryset of Reply objects
    that have .question.position and .answer.score selected.
    """

    #: map score‑bucket -> (question.positions, max_score)
    METRICS: dict[str, tuple[list[int], int]] = {
        "dry": ([2, 3, 7, 22, 28, 40, 42], 13),
        "damage": ([7, 32, 33, 35, 36, 37], 15),
        "sensitivity": ([39], 4.4),
        "sebum_oily": ([41], 4.2),
        "sebum_dry": ([42], 4.2),
        "flakes": ([40], 4.1),
    }

    LABELS = {
        "dry": "Dry",
        "damage": "Damage",
        "sensitivity": "Sensitivity",
        "sebum_oily": "Sebum Oily",
        "sebum_dry": "Sebum Dry",
        "flakes": "Flake",
    }

    @classmethod
    def compute(cls, replies: QuerySet[Reply]) -> dict[str, dict[str, float | str]]:
        # Build {position: score} map in one pass
        pos_to_score = {
            r.question.position: r.answer.score if r.answer else 0 for r in replies
        }

        report: dict[str, dict[str, float | str]] = {}
        for key, (positions, max_score) in cls.METRICS.items():
            bucket_total = sum(pos_to_score.get(p, 0) for p in positions)
            pct = round(min(bucket_total, max_score) / max_score * 100, 2)
            report[f"{key}_percentage"] = {"value": pct, "description": cls.LABELS[key]}
        # Special: Dry‑scalp composite (alias / convenience)
        report["dsc_percentage"] = report["sebum_dry_percentage"]
        return report
