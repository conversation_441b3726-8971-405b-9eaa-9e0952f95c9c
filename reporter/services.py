from django.contrib.auth import get_user_model
from loguru import logger

User = get_user_model()

from questionaire.models import Answer, Question, Reply
from recommendation.services import RecommenderModule

# from users.models import UserProfile
from .models import ScoreLabel


class Reporter:
    QUESTION_POSITIONS = {
        "goals": 1,
        "issue": 2,
        "hair_overall": 3,
        "split_ends": 7,
        "daily_water": 22,
        "hair_feels": 28,
        "treatments": 32,
        "enhancers": 33,
        "hair_behaviour": 35,
        "styling_hair": 36,
        "apply_heat": 37,
        "scalp_feeling": 39,
        "scalp_flaky": 42,
        "oily_scalp": 43,
        "dry_scalp": 41,
    }

    # __prev_recommender = RecommenderModule()
    recommender = RecommenderModule()
    # recommender = NewRecommender()

    def fetch_latest_session_guid(self, user_id: int) -> str:
        try:
            return self.recommender.fetch_latest_session_guid(user_id)
        except Exception as e:
            logger.error(f"Failed to fetch latest session_guid: {e}")
            return None

    def get_answer_score(self, user, position, session_guid: str) -> int:
        reply = Reply.objects.filter(
            user=user, question_id=position, session_guid=session_guid
        ).first()
        if not reply or reply is None:
            logger.info("Reply not found. Return 0!")
            return 0

        answer = Answer.objects.filter(
            question_id=position, title__icontains=reply.text
        ).first()
        if not answer or answer is None:
            logger.info("Answer not found. Return 0!")
            return 0
        return answer.score

    def get_label(self, description_key):
        """usage: "description": get_label("dry")"""
        return ScoreLabel.objects.filter(key=description_key).first().label

    def create_report(self, user_id: int, session_guid: str):
        user = User.objects.filter(id=user_id).first()
        if user is None:
            return {"error": "User not found"}
        # user_profile: UserProfile = UserProfile.objects.filter(user=user).first()
        total_questions: int = Question.objects.count()

        try:
            replies_count: int = Reply.objects.filter(
                user=user, session_guid=session_guid
            ).count()
            logger.info(
                f"\n\tReplies_count: {replies_count} Total_Questions_Count: {total_questions}\n"
            )
        except Exception as e:
            replies_count = 0
            logger.error(f"Failed to fetch replies count: {e}")

        error_count = 0

        # if user_profile.completed_questions < total_questions:
        if replies_count < total_questions:
            logger.info(
                f"User {user_id} has not completed the questionnaire - {replies_count}/{total_questions} questions answered"
            )
            error_count += 1
            return {
                "error": f"questionnaire not completed - {replies_count}/{total_questions} questions answered"
            }

        scores: dict = {
            name: self.get_answer_score(user, pos, session_guid)
            for name, pos in self.QUESTION_POSITIONS.items()
        }

        dry_score: int = sum(
            scores[key]
            for key in [
                "issue",
                "split_ends",
                "hair_overall",
                "daily_water",
                "hair_feels",
                "scalp_flaky",
                "dry_scalp",
            ]
        )
        dry_score_max = 13
        dry_score_pct: float = dry_score / dry_score_max

        damage_score: int = sum(
            scores[key]
            for key in [
                "split_ends",
                "treatments",
                "enhancers",
                "hair_behaviour",
                "styling_hair",
                "apply_heat",
            ]
        )
        damage_score_max = 15
        damage_score_pct: float = damage_score / damage_score_max

        sensitivity_score: float = scores["scalp_feeling"]
        sensitivity_max = 4.4
        sensitivity_pct: float = sensitivity_score / sensitivity_max

        sebum_oily_score: float = scores["oily_scalp"]
        sebum_oily_max = 4.2
        sebum_oily_pct: float = sebum_oily_score / sebum_oily_max

        sebum_dry_score: float = scores["dry_scalp"]
        sebum_dry_max = 4.2
        sebum_dry_pct: float = sebum_dry_score / sebum_dry_max

        flake_score: float = scores["scalp_flaky"]
        flake_max = 4.1
        flake_pct: float = flake_score / flake_max

        # TODO:  Why do we have 2 dry scores?

        return {
            "dry_score_percentage": {
                "value": round(dry_score_pct * 100, 2),
                "description": "Dry",
            },
            "damage_score_percentage": {
                "value": round(damage_score_pct * 100, 2),
                "description": "Damage",
            },
            "sensitivity_percentage": {
                "value": round(sensitivity_pct * 100, 2),
                "description": "Sensitivity",
            },
            "sebum_oily_percentage": {
                "value": round(sebum_oily_pct * 100, 2),
                "description": "Sebum Oily",
            },
            "sebum_dry_percentage": {
                "value": round(sebum_dry_pct * 100, 2),
                "description": "Sebum Dry",
            },
            "dsc_percentage": {
                "value": round(dry_score_pct * 100, 2),
                "description": "Dry Scalp",
            },
            "flake_score_percentage": {
                "value": round(flake_pct * 100, 2),
                "description": "Flake",
            },
        }
