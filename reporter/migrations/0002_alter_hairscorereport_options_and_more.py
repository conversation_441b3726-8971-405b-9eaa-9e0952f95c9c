# Generated by Django 5.2 on 2025-07-19 17:11

import datetime

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("reporter", "0001_initial"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="hairscorereport",
            options={"ordering": ["-created_at"]},
        ),
        migrations.AlterUniqueTogether(
            name="hairscorereport",
            unique_together=set(),
        ),
        migrations.RemoveField(
            model_name="scorelabel",
            name="label",
        ),
        migrations.AddField(
            model_name="hairscorereport",
            name="metadata",
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AddField(
            model_name="hairscorereport",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name="scorelabel",
            name="description",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="scorelabel",
            name="name",
            field=models.Char<PERSON>ield(
                default=datetime.datetime(
                    2025, 7, 19, 17, 11, 45, 495113, tzinfo=datetime.timezone.utc
                ),
                max_length=255,
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="hairscorereport",
            name="data",
            field=models.JSONField(default=dict),
        ),
    ]
