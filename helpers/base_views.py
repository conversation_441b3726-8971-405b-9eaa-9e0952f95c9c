"""
Base view implementations for common patterns across the application.

This module provides ready-to-use view classes that implement common
patterns like authentication, form handling, and data display.
"""

from django.contrib import messages
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.db import IntegrityError
from django.shortcuts import redirect, render
from loguru import logger

from .view_mixins import (
    AuthenticatedFormView,
    AuthenticatedTemplateView,
    BaseFormView,
    BaseTemplateView,
    JsonResponseMixin,
    QuestionnaireProgressMixin,
    UserAccessMixin,
    UserProfileMixin,
    require_user_profile,
    validate_user_access,
)

# ============================================================================
# Authentication Views
# ============================================================================


class BaseLoginView(JsonResponseMixin, BaseFormView):
    """
    Base login view with common authentication logic.
    """

    template_name = "users/login.html"
    success_url = "/dashboard/"

    def get_success_url(self):
        """Get redirect URL after successful login."""
        return self.request.GET.get("next", self.success_url)

    def process_form(self, form):
        """Process login form."""
        username = form.cleaned_data.get("username")
        password = form.cleaned_data.get("password")

        user = authenticate(self.request, username=username, password=password)

        if user:
            login(self.request, user)
            logger.info(f"Login successful for user: {user.username}")

            # Handle AJAX requests
            if self.request.headers.get("Content-Type") == "application/json":
                return self.json_success(
                    {
                        "redirect_url": self.get_success_url(),
                        "user": {
                            "id": user.id,
                            "username": user.username,
                            "email": user.email,
                        },
                    }
                )

            return redirect(self.get_success_url())
        else:
            error_msg = "Invalid username or password."

            # Handle AJAX requests
            if self.request.headers.get("Content-Type") == "application/json":
                return self.json_error(error_msg, status=401)

            messages.error(self.request, error_msg)
            return self.form_invalid(form)


class BaseRegisterView(JsonResponseMixin, BaseFormView):
    """
    Base registration view with common registration logic.
    """

    template_name = "users/register.html"
    success_url = "/users/login/"

    def get_form_data(self, form):
        """Extract form data for user creation. Override in subclasses."""
        return {
            "username": form.cleaned_data.get("username"),
            "email": form.cleaned_data.get("email"),
            "password": form.cleaned_data.get("password"),
        }

    def process_form(self, form):
        """Process registration form."""
        try:
            form_data = self.get_form_data(form)

            # Create user
            user = User.objects.create_user(**form_data)

            logger.info(f"User created successfully: {user.username}")

            # Handle AJAX requests
            if self.request.headers.get("Content-Type") == "application/json":
                return self.json_success(
                    {
                        "redirect_url": self.success_url,
                        "message": "Account created successfully.",
                    }
                )

            messages.success(self.request, "Account created successfully.")
            return redirect(self.success_url)

        except IntegrityError:
            error_msg = "Username already exists."
            logger.warning(f"Registration failed: {error_msg}")

            # Handle AJAX requests
            if self.request.headers.get("Content-Type") == "application/json":
                return self.json_error(error_msg, status=400)

            form.add_error("username", error_msg)
            return self.form_invalid(form)

        except Exception as e:
            error_msg = "An error occurred during registration."
            logger.error(f"Registration error: {e}")

            # Handle AJAX requests
            if self.request.headers.get("Content-Type") == "application/json":
                return self.json_error(error_msg, status=500)

            messages.error(self.request, error_msg)
            return self.form_invalid(form)


class BaseLogoutView(UserProfileMixin):
    """
    Base logout view.
    """

    def get(self, request):
        """Handle logout."""
        if request.user.is_authenticated:
            logger.info(f"User logged out: {request.user.username}")
            logout(request)
            messages.success(request, "You have been logged out successfully.")

        return redirect("/")


# ============================================================================
# Dashboard and Profile Views
# ============================================================================


class BaseDashboardView(AuthenticatedTemplateView, QuestionnaireProgressMixin):
    """
    Base dashboard view with common dashboard functionality.
    """

    template_name = "users/dashboard.html"
    page_title = "Dashboard"

    def get_context_data(self, **kwargs):
        """Add dashboard-specific context."""
        context = super().get_context_data(**kwargs)
        user_profile = self.get_user_profile()

        context.update(
            {
                "questionnaire_status": self.get_questionnaire_status(user_profile),
                "completed_questions": user_profile.completed_questions
                if user_profile
                else 0,
            }
        )

        return context


class BaseProfileView(AuthenticatedFormView, UserAccessMixin):
    """
    Base profile view for user profile management.
    """

    template_name = "users/profile.html"
    success_url = "/users/profile/"
    page_title = "Profile"

    def get_object(self):
        """Get the user profile object."""
        user_id = self.kwargs.get("user_id")
        user = self.get_target_user(user_id)
        return self.get_user_profile(user)

    def get_form_kwargs(self):
        """Add instance to form kwargs."""
        kwargs = super().get_form_kwargs()
        kwargs["instance"] = self.get_object()
        return kwargs

    def process_form(self, form):
        """Process profile form."""
        profile = form.save()
        logger.info(f"Profile updated for user: {profile.user.username}")
        return redirect(self.success_url)


# ============================================================================
# Questionnaire Views
# ============================================================================


class BaseQuestionnaireView(AuthenticatedTemplateView, QuestionnaireProgressMixin):
    """
    Base questionnaire view with progress tracking.
    """

    template_name = "questionaire/questions.html"

    def get(self, request, *args, **kwargs):
        """Handle GET request for questionnaire."""
        user_profile = self.get_user_profile()
        status = self.get_questionnaire_status(user_profile)

        # Redirect if questionnaire is completed
        if status == "Completed":
            return redirect("/users/recommendation/")

        # Get current question
        current_question = self.get_current_question(user_profile)
        if not current_question:
            return redirect("/users/recommendation/")

        return super().get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        """Add questionnaire context."""
        context = super().get_context_data(**kwargs)
        user_profile = self.get_user_profile()
        current_question = self.get_current_question(user_profile)

        context.update(
            {
                "current_question": current_question,
                "questionnaire_status": self.get_questionnaire_status(user_profile),
                "progress_percentage": self.get_progress_percentage(user_profile),
            }
        )

        return context

    def get_progress_percentage(self, user_profile):
        """Calculate questionnaire progress percentage."""
        if not user_profile:
            return 0

        from questionaire.models import Question

        total_questions = Question.objects.count()

        if total_questions == 0:
            return 100

        return int((user_profile.completed_questions / total_questions) * 100)


# ============================================================================
# Product Views
# ============================================================================


class BaseProductListView(BaseTemplateView):
    """
    Base product list view with search and filtering.
    """

    template_name = "products/products.html"
    page_title = "Products"
    search_fields = ["name", "ingredients", "insights"]
    allowed_sorts = ["name", "price", "rating"]
    default_sort = "name"

    def get_queryset(self):
        """Get products queryset."""
        from products.models import Product

        return Product.objects.all()

    def get_context_data(self, **kwargs):
        """Add product list context."""
        context = super().get_context_data(**kwargs)

        # Get products with filters applied
        products = self.get_queryset()
        products = self.apply_search(products)
        products = self.apply_filters(products)
        products = self.apply_sorting(products)

        context.update(
            {
                "products": products,
                "search_term": self.request.GET.get("q", ""),
                "current_category": self.request.GET.get("category"),
            }
        )

        return context

    def apply_search(self, queryset):
        """Apply search to products."""
        query = self.request.GET.get("q", "").strip()
        if query:
            from django.db.models import Q

            search_q = Q()
            for field in self.search_fields:
                search_q |= Q(**{f"{field}__icontains": query})
            queryset = queryset.filter(search_q)
        return queryset

    def apply_filters(self, queryset):
        """Apply category filter."""
        category = self.request.GET.get("category")
        if category:
            queryset = queryset.filter(category__name=category)
        return queryset

    def apply_sorting(self, queryset):
        """Apply sorting to products."""
        sort = self.request.GET.get("sort", self.default_sort)
        direction = self.request.GET.get("direction", "asc")

        if sort in self.allowed_sorts:
            order_field = sort if direction == "asc" else f"-{sort}"
            queryset = queryset.order_by(order_field)

        return queryset


class BaseProductDetailView(BaseTemplateView):
    """
    Base product detail view.
    """

    template_name = "products/product_detail.html"

    def get_object(self):
        """Get the product object."""
        from django.shortcuts import get_object_or_404

        from products.models import Product

        product_id = self.kwargs.get("product_id")
        return get_object_or_404(Product, pk=product_id)

    def get_context_data(self, **kwargs):
        """Add product detail context."""
        context = super().get_context_data(**kwargs)
        product = self.get_object()

        context.update(
            {
                "product": product,
                "page_title": product.name,
            }
        )

        return context


# ============================================================================
# Utility Functions for Function-Based Views
# ============================================================================


def simple_template_view(template_name, page_title="", extra_context=None):
    """
    Create a simple template view function.
    """

    def view(request):
        context = {
            "page_title": page_title,
        }
        if extra_context:
            context.update(extra_context)
        return render(request, template_name, context)

    return view


def authenticated_template_view(template_name, page_title="", extra_context=None):
    """
    Create an authenticated template view function.
    """

    @require_user_profile
    def view(request):
        context = {
            "page_title": page_title,
            "user_profile": request.user.userprofile,
        }
        if extra_context:
            context.update(extra_context)
        return render(request, template_name, context)

    return view


def user_specific_view(template_name, page_title="", extra_context=None):
    """
    Create a user-specific view function with access validation.
    """

    @validate_user_access
    def view(request, user_id=None):
        from django.shortcuts import get_object_or_404

        target_user = get_object_or_404(User, id=user_id) if user_id else request.user
        user_profile = target_user.userprofile

        context = {
            "page_title": page_title,
            "target_user": target_user,
            "user_profile": user_profile,
        }
        if extra_context:
            context.update(extra_context)
        return render(request, template_name, context)

    return view
