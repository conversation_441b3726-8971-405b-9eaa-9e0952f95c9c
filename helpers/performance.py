"""
Performance optimization utilities for the Cosmetrics AI application.

This module provides database query optimization, caching strategies,
and performance monitoring tools.
"""

import functools
import time
from collections.abc import Callable
from typing import Any

from django.core.cache import cache
from django.db import models
from django.db.models import Prefetch, QuerySet
from loguru import logger


class QueryOptimizer:
    """
    Database query optimization utilities.

    Provides methods for optimizing common query patterns and reducing N+1 queries.
    """

    @staticmethod
    def optimize_user_profile_queries(queryset: QuerySet) -> QuerySet:
        """Optimize queries for UserProfile with related data."""
        return queryset.select_related("user").prefetch_related(
            "recommendations", "user__replies__question", "user__replies__answer"
        )

    @staticmethod
    def optimize_questionnaire_queries(queryset: QuerySet) -> QuerySet:
        """Optimize queries for Questionnaire with questions and answers."""
        return queryset.prefetch_related(
            Prefetch(
                "questions",
                queryset=models.Q(questions__position__isnull=False).order_by(
                    "position"
                ),
            ),
            "questions__answers",
        )

    @staticmethod
    def optimize_reply_queries(queryset: QuerySet) -> QuerySet:
        """Optimize queries for Reply with related question and answer data."""
        return queryset.select_related(
            "user", "question", "answer", "question__questionaire"
        )

    @staticmethod
    def optimize_recommendation_queries(queryset: QuerySet) -> QuerySet:
        """Optimize queries for Recommendation with user data."""
        return queryset.select_related("user").prefetch_related("user__userprofile")

    @staticmethod
    def optimize_product_queries(queryset: QuerySet) -> QuerySet:
        """Optimize queries for Product with related data."""
        return queryset.prefetch_related(
            "recommended_users",
            "category",  # If you have categories
        )


class CacheManager:
    """
    Caching strategy manager.

    Provides consistent caching patterns for different types of data.
    """

    # Cache timeouts (in seconds)
    CACHE_TIMEOUTS = {
        "user_profile": 300,  # 5 minutes
        "questionnaire": 3600,  # 1 hour
        "products": 1800,  # 30 minutes
        "recommendations": 600,  # 10 minutes
        "reports": 1800,  # 30 minutes
    }

    @classmethod
    def get_cache_key(cls, prefix: str, identifier: str | int) -> str:
        """Generate a consistent cache key."""
        return f"cosmetrics:{prefix}:{identifier}"

    @classmethod
    def cache_user_profile(cls, user_id: int, profile_data: dict[str, Any]) -> None:
        """Cache user profile data."""
        cache_key = cls.get_cache_key("user_profile", user_id)
        cache.set(cache_key, profile_data, cls.CACHE_TIMEOUTS["user_profile"])

    @classmethod
    def get_cached_user_profile(cls, user_id: int) -> dict[str, Any] | None:
        """Get cached user profile data."""
        cache_key = cls.get_cache_key("user_profile", user_id)
        return cache.get(cache_key)

    @classmethod
    def cache_questionnaire(
        cls, questionnaire_id: int, questionnaire_data: dict[str, Any]
    ) -> None:
        """Cache questionnaire data."""
        cache_key = cls.get_cache_key("questionnaire", questionnaire_id)
        cache.set(cache_key, questionnaire_data, cls.CACHE_TIMEOUTS["questionnaire"])

    @classmethod
    def get_cached_questionnaire(cls, questionnaire_id: int) -> dict[str, Any] | None:
        """Get cached questionnaire data."""
        cache_key = cls.get_cache_key("questionnaire", questionnaire_id)
        return cache.get(cache_key)

    @classmethod
    def cache_recommendations(
        cls, user_id: int, session_guid: str, recommendations: dict[str, Any]
    ) -> None:
        """Cache user recommendations."""
        cache_key = cls.get_cache_key("recommendations", f"{user_id}:{session_guid}")
        cache.set(cache_key, recommendations, cls.CACHE_TIMEOUTS["recommendations"])

    @classmethod
    def get_cached_recommendations(
        cls, user_id: int, session_guid: str
    ) -> dict[str, Any] | None:
        """Get cached recommendations."""
        cache_key = cls.get_cache_key("recommendations", f"{user_id}:{session_guid}")
        return cache.get(cache_key)

    @classmethod
    def invalidate_user_cache(cls, user_id: int) -> None:
        """Invalidate all cache entries for a user."""
        patterns = [
            cls.get_cache_key("user_profile", user_id),
            cls.get_cache_key("recommendations", f"{user_id}:*"),
        ]

        for pattern in patterns:
            if "*" in pattern:
                # For patterns with wildcards, you'd need a more sophisticated cache backend
                # For now, we'll just log the invalidation
                logger.info(f"Should invalidate cache pattern: {pattern}")
            else:
                cache.delete(pattern)


def cache_result(cache_key_func: Callable, timeout: int = 300):
    """
    Decorator to cache function results.

    Args:
        cache_key_func: Function that generates cache key from function args
        timeout: Cache timeout in seconds
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = cache_key_func(*args, **kwargs)

            # Try to get from cache
            result = cache.get(cache_key)
            if result is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return result

            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            logger.debug(f"Cache set for key: {cache_key}")

            return result

        return wrapper

    return decorator


def monitor_performance(func: Callable) -> Callable:
    """
    Decorator to monitor function performance.

    Logs execution time and can be extended to track other metrics.
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()

        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time

            logger.info(
                f"Performance: {func.__name__} executed in {execution_time:.3f}s"
            )

            # Log slow queries (> 1 second)
            if execution_time > 1.0:
                logger.warning(
                    f"Slow execution: {func.__name__} took {execution_time:.3f}s"
                )

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(
                f"Performance: {func.__name__} failed after {execution_time:.3f}s: {e}"
            )
            raise

    return wrapper


class DatabaseOptimizer:
    """
    Database optimization utilities and best practices.
    """

    @staticmethod
    def get_optimized_user_recommendations(
        user_id: int, session_guid: str
    ) -> dict[str, Any] | None:
        """Get user recommendations with optimized queries."""
        from recommendation.models import Recommendation

        # Check cache first
        cached = CacheManager.get_cached_recommendations(user_id, session_guid)
        if cached:
            return cached

        # Optimized database query
        try:
            recommendation = Recommendation.objects.select_related("user").get(
                user_id=user_id, session_guid=session_guid
            )

            result = {
                "shampoos": recommendation.shampoos,
                "conditioners": recommendation.conditioners,
                "created_at": recommendation.created_at.isoformat(),
                "user_id": recommendation.user_id,
                "session_guid": recommendation.session_guid,
            }

            # Cache the result
            CacheManager.cache_recommendations(user_id, session_guid, result)

            return result

        except Recommendation.DoesNotExist:
            return None

    @staticmethod
    def get_optimized_user_profile(user_id: int) -> dict[str, Any] | None:
        """Get user profile with optimized queries."""
        from users.models import UserProfile

        # Check cache first
        cached = CacheManager.get_cached_user_profile(user_id)
        if cached:
            return cached

        # Optimized database query
        try:
            profile = (
                UserProfile.objects.select_related("user")
                .prefetch_related("recommendations")
                .get(user_id=user_id)
            )

            result = {
                "id": profile.id,
                "user_id": profile.user_id,
                "first_name": profile.first_name,
                "last_name": profile.last_name,
                "completed_questions": profile.completed_questions,
                "questionaire_completed": profile.questionaire_completed,
                "hair_type": profile.hair_type,
                "recommendation_count": profile.recommendations.count(),
            }

            # Cache the result
            CacheManager.cache_user_profile(user_id, result)

            return result

        except UserProfile.DoesNotExist:
            return None


# Example usage decorators
def cache_user_profile_result(user_id: int, *args, **kwargs) -> str:
    """Generate cache key for user profile functions."""
    return CacheManager.get_cache_key("user_profile", user_id)


def cache_questionnaire_result(questionnaire_id: int, *args, **kwargs) -> str:
    """Generate cache key for questionnaire functions."""
    return CacheManager.get_cache_key("questionnaire", questionnaire_id)


# Performance monitoring context manager
class PerformanceMonitor:
    """Context manager for monitoring performance of code blocks."""

    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None

    def __enter__(self):
        self.start_time = time.time()
        logger.debug(f"Starting performance monitoring for: {self.operation_name}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        execution_time = time.time() - self.start_time

        if exc_type is None:
            logger.info(
                f"Performance: {self.operation_name} completed in {execution_time:.3f}s"
            )
        else:
            logger.error(
                f"Performance: {self.operation_name} failed after {execution_time:.3f}s"
            )

        if execution_time > 2.0:
            logger.warning(
                f"Slow operation: {self.operation_name} took {execution_time:.3f}s"
            )


# Example usage:
"""
# Using the cache decorator
@cache_result(cache_user_profile_result, timeout=300)
def get_user_profile_data(user_id: int):
    # Expensive operation
    return expensive_user_profile_calculation(user_id)

# Using the performance monitor
@monitor_performance
def expensive_operation():
    # Some expensive operation
    pass

# Using the context manager
with PerformanceMonitor("Complex calculation"):
    result = complex_calculation()
"""
