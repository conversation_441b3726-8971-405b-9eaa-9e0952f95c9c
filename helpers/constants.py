"""
Shared constants and configuration values.

This module consolidates all duplicate constants, magic numbers, and configuration
values used across the application to eliminate duplication and provide a single
source of truth for common values.
"""

from typing import Final

# ============================================================================
# Hair Analysis Constants
# ============================================================================

# Hair ID question positions - used across multiple modules
HAIR_ID_QUESTIONS: Final[list[int]] = [6, 7, 8, 9, 37, 42]
HAIR_ID_QUESTIONS_CORE: Final[list[int]] = [6, 8, 9, 37]  # Most commonly used subset


# Question position mappings
class QuestionPositions:
    """Question position constants for hair analysis."""

    HAIR_GOAL = 1
    CHOSEN_ISSUE = 2
    HAIR_TYPE = 6
    HAIR_DENSITY = 8
    HAIR_TEXTURE = 9
    HAIR_POROSITY = 37
    ADDITIONAL_HAIR_TYPE = 7  # Alternative hair type question
    EXTENDED_HAIR_TYPE = 42  # Extended hair type question


# Question labels mapping
QUESTION_LABELS: Final[dict[int, str]] = {
    QuestionPositions.HAIR_GOAL: "hair goal",
    QuestionPositions.CHOSEN_ISSUE: "chosen issue",
    QuestionPositions.HAIR_TYPE: "hair type",
    QuestionPositions.HAIR_DENSITY: "hair density",
    QuestionPositions.HAIR_TEXTURE: "hair texture",
    QuestionPositions.HAIR_POROSITY: "hair porosity",
}


# Hair type values
class HairTypes:
    """Standard hair type classifications."""

    TYPE_1_STRAIGHT = "Type 1 Straight"
    TYPE_2_WAVY = "Type 2 Wavy"
    TYPE_3_CURLY = "Type 3 Curly"
    TYPE_4A = "Type 4a"
    TYPE_4B = "Type 4b"
    TYPE_4C = "Type 4c"


# Hair density values
class HairDensity:
    """Hair density classifications."""

    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"


# Hair texture values
class HairTexture:
    """Hair texture classifications."""

    THIN_OR_FINE = "Thin or Fine"
    MEDIUM = "Medium"
    THICK_OR_COARSE = "Thick or Coarse"


# Hair porosity values
class HairPorosity:
    """Hair porosity classifications."""

    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"


# ============================================================================
# API Configuration Constants
# ============================================================================


# API timeouts and limits
class APIConfig:
    """API configuration constants."""

    DEFAULT_TIMEOUT = 10000  # 10 seconds in milliseconds
    DEFAULT_TIMEOUT_SECONDS = 10  # 10 seconds
    CACHE_TIMEOUT = 300  # 5 minutes in seconds
    MAX_RETRY_ATTEMPTS = 3
    RETRY_WAIT_SECONDS = 2

    # Pagination defaults
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100
    MIN_PAGE_SIZE = 1

    # File upload limits
    MAX_FILE_SIZE_KB = 5000
    MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_KB * 1024


# Default API URLs
class APIUrls:
    """Default API URL configurations."""

    DEFAULT_BACKEND_API = "http://localhost:8000/api"
    DEFAULT_QUIZ_API_BASE = "http://localhost:8000"
    DEFAULT_FRONTEND_DEV = "http://localhost:3000"


# ============================================================================
# Time Constants
# ============================================================================


class TimeConstants:
    """Time-related constants in milliseconds and seconds."""

    # Milliseconds
    ONE_SECOND_MS = 1000
    ONE_MINUTE_MS = 60 * ONE_SECOND_MS
    FIVE_MINUTES_MS = 5 * ONE_MINUTE_MS
    TEN_MINUTES_MS = 10 * ONE_MINUTE_MS
    ONE_HOUR_MS = 60 * ONE_MINUTE_MS

    # Seconds
    ONE_MINUTE_S = 60
    FIVE_MINUTES_S = 5 * ONE_MINUTE_S
    TEN_MINUTES_S = 10 * ONE_MINUTE_S
    ONE_HOUR_S = 60 * ONE_MINUTE_S


# ============================================================================
# Database Constants
# ============================================================================


class DatabaseConfig:
    """Database-related constants."""

    # Field lengths
    MAX_EMAIL_LENGTH = 255
    MAX_USERNAME_LENGTH = 150
    MAX_NAME_LENGTH = 255
    MAX_PHONE_LENGTH = 20
    MAX_ADDRESS_LENGTH = 100
    MAX_CITY_LENGTH = 40
    MAX_COUNTY_LENGTH = 80
    MAX_POSTCODE_LENGTH = 20
    MAX_SKU_LENGTH = 254
    MAX_CATEGORY_NAME_LENGTH = 254
    MAX_PRODUCT_NAME_LENGTH = 254
    MAX_TEXT_FIELD_LENGTH = 1024

    # Decimal field configurations
    PRICE_MAX_DIGITS = 10
    PRICE_DECIMAL_PLACES = 2
    RATING_MAX_DIGITS = 3
    RATING_DECIMAL_PLACES = 2


# ============================================================================
# File and Media Constants
# ============================================================================


class MediaConfig:
    """Media and file handling constants."""

    # Upload directories
    IMAGES_UPLOAD_DIR = "images/"
    QUESTION_IMAGES_DIR = "question_images/"
    REPLY_UPLOADS_DIR = "uploads/replies"

    # Allowed file types
    ALLOWED_IMAGE_EXTENSIONS = [".jpg", ".jpeg", ".png", ".gif", ".webp"]
    ALLOWED_DOCUMENT_EXTENSIONS = [".pdf", ".doc", ".docx", ".txt"]

    # File size limits (in bytes)
    MAX_IMAGE_SIZE = 5 * 1024 * 1024  # 5MB
    MAX_DOCUMENT_SIZE = 10 * 1024 * 1024  # 10MB


# ============================================================================
# Validation Constants
# ============================================================================


class ValidationConfig:
    """Validation-related constants."""

    # Password requirements
    MIN_PASSWORD_LENGTH = 8
    MAX_PASSWORD_LENGTH = 128

    # Username requirements
    MIN_USERNAME_LENGTH = 3
    MAX_USERNAME_LENGTH = 30

    # Text field limits
    MIN_DESCRIPTION_LENGTH = 10
    MAX_DESCRIPTION_LENGTH = 1000
    MAX_TITLE_LENGTH = 200

    # Numeric limits
    MIN_AGE = 13
    MAX_AGE = 120
    MIN_RATING = 0.0
    MAX_RATING = 5.0


# ============================================================================
# Error Messages
# ============================================================================


class ErrorMessages:
    """Standard error messages."""

    # Authentication errors
    INVALID_CREDENTIALS = "Invalid username or password"
    ACCOUNT_DISABLED = "This account has been disabled"
    TOKEN_EXPIRED = "Authentication token has expired"
    PERMISSION_DENIED = "You don't have permission to perform this action"

    # Validation errors
    REQUIRED_FIELD = "This field is required"
    INVALID_EMAIL = "Please enter a valid email address"
    PASSWORD_TOO_SHORT = (
        f"Password must be at least {ValidationConfig.MIN_PASSWORD_LENGTH} characters"
    )
    INVALID_FILE_TYPE = "File type not supported"
    FILE_TOO_LARGE = "File size exceeds maximum limit"

    # Data errors
    NOT_FOUND = "The requested resource was not found"
    ALREADY_EXISTS = "A record with this information already exists"
    INVALID_DATA = "The provided data is invalid"

    # System errors
    INTERNAL_ERROR = "An internal error occurred. Please try again later"
    SERVICE_UNAVAILABLE = "Service temporarily unavailable"
    NETWORK_ERROR = "Network connection error"


# ============================================================================
# Success Messages
# ============================================================================


class SuccessMessages:
    """Standard success messages."""

    # Authentication
    LOGIN_SUCCESS = "Login successful!"
    LOGOUT_SUCCESS = "Logout successful!"
    SIGNUP_SUCCESS = "Signup successful! Please login."
    PASSWORD_RESET_SENT = "Check your inbox for the reset link ✉️"

    # Data operations
    CREATED_SUCCESS = "Created successfully!"
    UPDATED_SUCCESS = "Updated successfully!"
    DELETED_SUCCESS = "Deleted successfully!"
    SAVED_SUCCESS = "Saved successfully!"

    # File operations
    UPLOAD_SUCCESS = "File uploaded successfully!"
    DOWNLOAD_SUCCESS = "File downloaded successfully!"


# ============================================================================
# HTTP Status Codes
# ============================================================================


class HTTPStatus:
    """HTTP status code constants."""

    # Success
    OK = 200
    CREATED = 201
    ACCEPTED = 202
    NO_CONTENT = 204

    # Client errors
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    METHOD_NOT_ALLOWED = 405
    CONFLICT = 409
    UNPROCESSABLE_ENTITY = 422
    TOO_MANY_REQUESTS = 429

    # Server errors
    INTERNAL_SERVER_ERROR = 500
    BAD_GATEWAY = 502
    SERVICE_UNAVAILABLE = 503
    GATEWAY_TIMEOUT = 504


# ============================================================================
# Environment Constants
# ============================================================================


class Environment:
    """Environment-related constants."""

    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


# ============================================================================
# Cache Keys
# ============================================================================


class CacheKeys:
    """Cache key patterns and prefixes."""

    USER_SESSION_PREFIX = "latest_session_guid_user_"
    USER_PROFILE_PREFIX = "user_profile_"
    QUESTION_CACHE_PREFIX = "question_"
    PRODUCT_CACHE_PREFIX = "product_"
    RECOMMENDATION_PREFIX = "recommendation_"

    @staticmethod
    def user_session_key(user_id: int) -> str:
        """Generate cache key for user session."""
        return f"{CacheKeys.USER_SESSION_PREFIX}{user_id}"

    @staticmethod
    def user_profile_key(user_id: int) -> str:
        """Generate cache key for user profile."""
        return f"{CacheKeys.USER_PROFILE_PREFIX}{user_id}"


# ============================================================================
# Recommendation Engine Constants
# ============================================================================


class RecommendationConfig:
    """Constants for the recommendation engine."""

    DEFAULT_TOP_N_PRODUCTS = 12
    MIN_RECOMMENDATIONS = 1
    MAX_RECOMMENDATIONS = 20

    # Feature weights
    HAIR_TYPE_WEIGHT = 0.3
    TEXTURE_WEIGHT = 0.25
    POROSITY_WEIGHT = 0.25
    GOALS_WEIGHT = 0.2

    # Product categories
    SHAMPOO_CATEGORY = "shampoo"
    CONDITIONER_CATEGORY = "conditioner"
    TREATMENT_CATEGORY = "treatment"
    STYLING_CATEGORY = "styling"


# ============================================================================
# Frontend Constants (for reference)
# ============================================================================


class FrontendConfig:
    """Frontend-related constants for reference."""

    # These should be kept in sync with ui/src/constants/index.ts
    HAIR_ID_QUESTIONS_JS = [6, 8, 9, 37]  # Should match HAIR_ID_QUESTIONS_CORE
    FIVE_MINUTES_MS_JS = 5 * 60 * 1000  # Should match TimeConstants.FIVE_MINUTES_MS
    API_TIMEOUT_MS_JS = 10000  # Should match APIConfig.DEFAULT_TIMEOUT
