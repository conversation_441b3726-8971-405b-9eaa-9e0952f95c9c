from base_page import BasePage


class ProductPage(BasePage):
    """
    A class used to represent an Amazon product page.

    Inherits from BasePage and provides methods to extract specific details from an Amazon product page.

    Methods:
        get_product_title(): Returns the product title.
        get_product_price(): Returns the product price.
        get_product_image_url(): Returns the URL of the product image.
        get_product_description(): Returns the product description.
    """

    def __init__(self, url) -> None:
        """
        Initializes the ProductPage with a URL.

        Args:
            url (str): The URL of the Amazon product page to be loaded.
        """
        super().__init__(url)

    def get_product_title(self):
        """
        Extracts and returns the product title from the page.

        Returns:
            str: The product title, or "Title not found" if the title element is not present.
        """
        soup = self.get_soup()
        title = soup.find("span", id="productTitle")
        return title.get_text(strip=True) if title else "Title not found"

    def get_product_price(self):
        """
        Extracts and returns the product price from the page.

        Returns:
            str: The product price, or "Price not found" if the price element is not present.
        """
        soup = self.get_soup()
        price = soup.find("span", id="priceblock_ourprice") or soup.find(
            "span",
            id="priceblock_dealprice",
        )
        return price.get_text(strip=True) if price else "Price not found"

    def get_product_image_url(self):
        """
        Extracts and returns the URL of the product image from the page.

        Returns:
            str: The URL of the product image, or "Image URL not found" if the image element is not present.
        """
        soup = self.get_soup()
        image = soup.find("img", id="landingImage")
        return image["src"] if image else "Image URL not found"

    def get_product_description(self):
        """
        Extracts and returns the product description from the page.

        Returns:
            str: The product description, or "Description not found" if the description element is not present.
        """
        soup = self.get_soup()
        description = soup.find("div", id="productDescription")
        return (
            description.get_text(strip=True) if description else "Description not found"
        )
