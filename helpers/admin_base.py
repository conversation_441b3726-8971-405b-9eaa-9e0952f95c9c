"""
Base admin classes to reduce duplication across admin configurations.

This module provides common admin configurations that can be inherited
by specific model admin classes to maintain consistency and reduce code duplication.
"""

from django.contrib import admin


class BaseModelAdmin(admin.ModelAdmin):
    """
    Base admin class with common configurations for all models.
    """

    # Common configurations
    save_on_top = True
    show_full_result_count = False

    # Default actions
    actions = ["delete_selected"]

    def get_readonly_fields(self, request, obj=None):
        """
        Make created_at and updated_at fields readonly by default.
        Override in subclasses if needed.
        """
        readonly_fields = list(super().get_readonly_fields(request, obj))

        # Add common timestamp fields if they exist
        if hasattr(self.model, "created_at") and "created_at" not in readonly_fields:
            readonly_fields.append("created_at")
        if hasattr(self.model, "updated_at") and "updated_at" not in readonly_fields:
            readonly_fields.append("updated_at")

        return readonly_fields


class TimestampModelAdmin(BaseModelAdmin):
    """
    Admin class for models with timestamp fields (created_at, updated_at).
    """

    list_filter = ("created_at", "updated_at")
    ordering = ("-created_at",)

    def get_list_display(self, request):
        """
        Add timestamp fields to list_display if they exist and aren't already included.
        """
        list_display = list(super().get_list_display(request))

        # Add created_at if it exists and isn't already in list_display
        if (
            hasattr(self.model, "created_at")
            and "created_at" not in list_display
            and len(list_display) < 10
        ):  # Avoid too many columns
            list_display.append("created_at")

        return tuple(list_display)


class UserRelatedModelAdmin(TimestampModelAdmin):
    """
    Admin class for models that have a user relationship.
    """

    def get_list_display(self, request):
        """
        Add user field to list_display if it exists.
        """
        list_display = list(super().get_list_display(request))

        # Add user field if it exists and isn't already in list_display
        if (
            hasattr(self.model, "user")
            and "user" not in list_display
            and len(list_display) < 10
        ):
            list_display.insert(0, "user")  # Put user first

        return tuple(list_display)

    def get_search_fields(self, request):
        """
        Add user search fields by default.
        """
        search_fields = list(super().get_search_fields(request))

        # Add user search fields if user relationship exists
        if hasattr(self.model, "user"):
            user_search_fields = [
                "user__username",
                "user__email",
                "user__first_name",
                "user__last_name",
            ]
            for field in user_search_fields:
                if field not in search_fields:
                    search_fields.append(field)

        return search_fields

    def get_list_filter(self, request):
        """
        Add user filter to list_filter.
        """
        list_filter = list(super().get_list_filter(request))

        # Add user filter if it exists and isn't already in list_filter
        if hasattr(self.model, "user") and "user" not in list_filter:
            list_filter.insert(0, "user")

        return tuple(list_filter)


class SessionRelatedModelAdmin(UserRelatedModelAdmin):
    """
    Admin class for models that have session_guid field.
    """

    def get_list_display(self, request):
        """
        Add session_guid to list_display if it exists.
        """
        list_display = list(super().get_list_display(request))

        # Add session_guid if it exists and isn't already in list_display
        if (
            hasattr(self.model, "session_guid")
            and "session_guid" not in list_display
            and len(list_display) < 10
        ):
            # Insert after user if user exists, otherwise at beginning
            insert_index = 1 if "user" in list_display else 0
            list_display.insert(insert_index, "session_guid")

        return tuple(list_display)

    def get_search_fields(self, request):
        """
        Add session_guid to search fields.
        """
        search_fields = list(super().get_search_fields(request))

        # Add session_guid search if it exists
        if hasattr(self.model, "session_guid") and "session_guid" not in search_fields:
            search_fields.append("session_guid")

        return search_fields


class ReadOnlyModelAdmin(BaseModelAdmin):
    """
    Admin class for models that should be read-only (like reports, logs).
    """

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class SimpleModelAdmin(BaseModelAdmin):
    """
    Admin class for simple models with minimal configuration.
    Automatically configures basic list_display based on model fields.
    """

    def get_list_display(self, request):
        """
        Auto-generate list_display from model fields, excluding complex fields.
        """
        if hasattr(self, "list_display") and self.list_display != ("__str__",):
            return self.list_display

        # Get model fields and create a sensible list_display
        fields = []
        excluded_field_types = ["TextField", "JSONField", "BinaryField"]

        for field in self.model._meta.fields:
            # Skip complex fields that don't display well in lists
            if (
                field.__class__.__name__ not in excluded_field_types
                and not field.name.endswith("_id")  # Skip foreign key ID fields
                and len(fields) < 8
            ):  # Limit to 8 fields max
                fields.append(field.name)

        return tuple(fields) if fields else ("__str__",)


class CategoryModelAdmin(SimpleModelAdmin):
    """
    Admin class for category-like models (name, description, etc.).
    """

    search_fields = ["name", "friendly_name", "description"]
    ordering = ["name"]

    def get_list_display(self, request):
        """
        Prioritize name-like fields for categories.
        """
        list_display = []

        # Prioritize common category fields
        priority_fields = ["name", "friendly_name", "title", "label"]
        for field_name in priority_fields:
            if hasattr(self.model, field_name):
                list_display.append(field_name)

        # Add other fields from parent class
        parent_display = super().get_list_display(request)
        for field in parent_display:
            if field not in list_display and len(list_display) < 6:
                list_display.append(field)

        return tuple(list_display)


class ProductModelAdmin(SimpleModelAdmin):
    """
    Admin class for product-like models.
    """

    search_fields = ["name", "sku", "title"]
    list_filter = (
        ["category", "created_at"]
        if hasattr(admin.ModelAdmin, "created_at")
        else ["category"]
    )
    ordering = ["name"]

    def get_list_display(self, request):
        """
        Prioritize product-specific fields.
        """
        list_display = []

        # Prioritize common product fields
        priority_fields = ["sku", "name", "title", "category", "price", "rating"]
        for field_name in priority_fields:
            if hasattr(self.model, field_name):
                list_display.append(field_name)

        # Add timestamp fields
        if hasattr(self.model, "created_at") and len(list_display) < 8:
            list_display.append("created_at")

        return (
            tuple(list_display) if list_display else super().get_list_display(request)
        )


# Utility functions for common admin customizations
def make_published(modeladmin, request, queryset):
    """Action to mark items as published."""
    queryset.update(is_published=True)


make_published.short_description = "Mark selected items as published"


def make_unpublished(modeladmin, request, queryset):
    """Action to mark items as unpublished."""
    queryset.update(is_published=False)


make_unpublished.short_description = "Mark selected items as unpublished"


def export_as_csv(modeladmin, request, queryset):
    """Action to export selected items as CSV."""
    import csv

    from django.http import HttpResponse

    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = (
        f'attachment; filename="{modeladmin.model._meta.verbose_name_plural}.csv"'
    )

    writer = csv.writer(response)

    # Write header
    field_names = [field.name for field in modeladmin.model._meta.fields]
    writer.writerow(field_names)

    # Write data
    for obj in queryset:
        writer.writerow([getattr(obj, field) for field in field_names])

    return response


export_as_csv.short_description = "Export selected items as CSV"
