"""
Standardized API error handling for the Cosmetrics AI application.

This module provides consistent error handling patterns, decorators, and response
schemas to ensure uniform error responses across all API endpoints.
"""

import functools
import traceback
from collections.abc import Callable
from typing import Any

from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.http import Http404
from loguru import logger
from ninja.errors import HttpError

from .schemas import MessageOut, NotFoundSchema, Validation422Schema


class APIErrorHandler:
    """
    Centralized error handling for API endpoints.

    Provides consistent error responses and logging across all endpoints.
    """

    @staticmethod
    def handle_validation_error(error: ValidationError) -> dict[str, Any]:
        """Handle Django ValidationError."""
        logger.warning(f"Validation error: {error}")
        return {
            "success": False,
            "message": "Validation failed",
            "errors": error.message_dict
            if hasattr(error, "message_dict")
            else [str(error)],
        }

    @staticmethod
    def handle_integrity_error(error: IntegrityError) -> dict[str, Any]:
        """Handle database integrity errors."""
        logger.error(f"Database integrity error: {error}")
        return {
            "success": False,
            "message": "Database constraint violation",
            "detail": "The operation violates database constraints",
        }

    @staticmethod
    def handle_not_found_error(error: Http404) -> dict[str, Any]:
        """Handle 404 Not Found errors."""
        logger.info(f"Resource not found: {error}")
        return {"success": False, "message": "Resource not found", "detail": str(error)}

    @staticmethod
    def handle_permission_error(error: Exception) -> dict[str, Any]:
        """Handle permission/authorization errors."""
        logger.warning(f"Permission denied: {error}")
        return {
            "success": False,
            "message": "Permission denied",
            "detail": "You don't have permission to access this resource",
        }

    @staticmethod
    def handle_generic_error(error: Exception) -> dict[str, Any]:
        """Handle unexpected errors."""
        logger.error(f"Unexpected error: {error}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            "success": False,
            "message": "Internal server error",
            "detail": "An unexpected error occurred",
        }


def handle_api_error(func: Callable) -> Callable:
    """
    Decorator for standardized API error handling.

    Catches common exceptions and returns consistent error responses.

    Usage:
        @router.get("/endpoint")
        @handle_api_error
        def my_endpoint(request):
            # Your endpoint logic here
            pass
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValidationError as e:
            error_response = APIErrorHandler.handle_validation_error(e)
            raise HttpError(422, error_response["message"])
        except IntegrityError as e:
            error_response = APIErrorHandler.handle_integrity_error(e)
            raise HttpError(400, error_response["message"])
        except Http404 as e:
            error_response = APIErrorHandler.handle_not_found_error(e)
            raise HttpError(404, error_response["message"])
        except PermissionError as e:
            error_response = APIErrorHandler.handle_permission_error(e)
            raise HttpError(403, error_response["message"])
        except Exception as e:
            error_response = APIErrorHandler.handle_generic_error(e)
            raise HttpError(500, error_response["message"])

    return wrapper


def api_response_wrapper(success_schema: type, error_schemas: dict[int, type] = None):
    """
    Decorator factory for standardized API response schemas.

    Args:
        success_schema: Schema for successful responses
        error_schemas: Dict mapping status codes to error schemas

    Usage:
        @router.get("/endpoint")
        @api_response_wrapper(MySuccessSchema, {404: NotFoundSchema, 422: Validation422Schema})
        @handle_api_error
        def my_endpoint(request):
            pass
    """
    if error_schemas is None:
        error_schemas = {
            400: MessageOut,
            404: NotFoundSchema,
            422: Validation422Schema,
            500: MessageOut,
        }

    def decorator(func: Callable) -> Callable:
        # Add response schema metadata to function
        func._response_schemas = {200: success_schema, **error_schemas}
        return func

    return decorator


class StandardResponses:
    """
    Standard response schema combinations for common API patterns.
    """

    @staticmethod
    def crud_responses(success_schema: type) -> dict[int, type]:
        """Standard CRUD operation responses."""
        return {
            200: success_schema,
            400: MessageOut,
            404: NotFoundSchema,
            422: Validation422Schema,
            500: MessageOut,
        }

    @staticmethod
    def list_responses(success_schema: type) -> dict[int, type]:
        """Standard list operation responses."""
        return {
            200: success_schema,
            400: MessageOut,
            500: MessageOut,
        }

    @staticmethod
    def auth_responses(success_schema: type) -> dict[int, type]:
        """Standard authentication operation responses."""
        return {
            200: success_schema,
            400: MessageOut,
            401: MessageOut,
            403: MessageOut,
            422: Validation422Schema,
            500: MessageOut,
        }


# Convenience decorators for common patterns
def crud_endpoint(success_schema: type):
    """Decorator for CRUD endpoints with standard error handling."""
    return api_response_wrapper(
        success_schema, StandardResponses.crud_responses(success_schema)
    )


def list_endpoint(success_schema: type):
    """Decorator for list endpoints with standard error handling."""
    return api_response_wrapper(
        success_schema, StandardResponses.list_responses(success_schema)
    )


def auth_endpoint(success_schema: type):
    """Decorator for authentication endpoints with standard error handling."""
    return api_response_wrapper(
        success_schema, StandardResponses.auth_responses(success_schema)
    )


# Example usage patterns
"""
# Basic usage
@router.get("/users/{int:user_id}")
@crud_endpoint(UserOutSchema)
@handle_api_error
def get_user(request, user_id: int):
    return get_object_or_404(User, pk=user_id)

# List endpoint
@router.get("/users/")
@list_endpoint(List[UserOutSchema])
@handle_api_error
def list_users(request):
    return User.objects.all()

# Authentication endpoint
@router.post("/auth/login")
@auth_endpoint(LoginResponseSchema)
@handle_api_error
def login(request, data: LoginSchema):
    # Login logic here
    pass
"""
