"""
Base view classes and mixins to reduce duplication across Django views.

This module provides common view patterns that can be inherited by specific
views to maintain consistency and reduce code duplication.
"""

from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.models import User
from django.core.exceptions import PermissionDenied
from django.db.models import Q
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.views.generic import DetailView, ListView, TemplateView
from django.views.generic.edit import FormView
from loguru import logger

from users.models import UserProfile

# ============================================================================
# Authentication and User Access Mixins
# ============================================================================


class UserProfileMixin:
    """
    Mixin that provides user profile context and validation.
    """

    def get_user_profile(self, user=None):
        """Get or create user profile for the given user."""
        if user is None:
            user = self.request.user

        if user.is_anonymous:
            return None

        try:
            return UserProfile.objects.get(user=user)
        except UserProfile.DoesNotExist:
            # Create profile if it doesn't exist
            return UserProfile.objects.create(user=user)

    def get_context_data(self, **kwargs):
        """Add user profile to context."""
        context = super().get_context_data(**kwargs)
        if hasattr(self.request, "user"):
            context["user_profile"] = self.get_user_profile()
        return context


class UserAccessMixin:
    """
    Mixin that validates user access and provides common user checks.
    """

    def check_user_access(self, user_id=None):
        """Check if current user has access to the requested user data."""
        if self.request.user.is_anonymous:
            raise PermissionDenied("Authentication required")

        if user_id and str(self.request.user.id) != str(user_id):
            if not self.request.user.is_staff:
                raise PermissionDenied("Access denied")

        return True

    def get_target_user(self, user_id=None):
        """Get the target user, defaulting to current user."""
        if user_id:
            self.check_user_access(user_id)
            return get_object_or_404(User, id=user_id)
        return self.request.user


class QuestionnaireProgressMixin:
    """
    Mixin for handling questionnaire progress and status.
    """

    def get_questionnaire_status(self, user_profile=None):
        """Get questionnaire completion status for user."""
        if user_profile is None:
            user_profile = self.get_user_profile()

        if not user_profile:
            return "Forbidden"

        from questionaire.models import Question

        total_questions = Question.objects.count()

        if user_profile.completed_questions == 0:
            return "NotStarted"
        elif user_profile.completed_questions >= total_questions:
            return "Completed"
        else:
            return "Started"

    def get_current_question(self, user_profile=None):
        """Get the current question for the user."""
        if user_profile is None:
            user_profile = self.get_user_profile()

        from questionaire.models import Question

        if user_profile.completed_questions >= Question.objects.count():
            return None

        return Question.objects.get(position=user_profile.completed_questions + 1)


# ============================================================================
# Base View Classes
# ============================================================================


class BaseTemplateView(UserProfileMixin, TemplateView):
    """
    Base template view with common functionality.
    """

    def get_context_data(self, **kwargs):
        """Add common context data."""
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "page_title": getattr(self, "page_title", ""),
                "page_description": getattr(self, "page_description", ""),
            }
        )
        return context


class AuthenticatedTemplateView(LoginRequiredMixin, BaseTemplateView):
    """
    Base template view that requires authentication.
    """

    login_url = "/users/login/"
    redirect_field_name = "next"


class BaseFormView(UserProfileMixin, FormView):
    """
    Base form view with common form handling patterns.
    """

    def form_valid(self, form):
        """Handle successful form submission."""
        try:
            result = self.process_form(form)
            if hasattr(self, "success_message"):
                messages.success(self.request, self.success_message)
            return result
        except Exception as e:
            logger.error(f"Form processing error: {e}")
            messages.error(self.request, "An error occurred. Please try again.")
            return self.form_invalid(form)

    def form_invalid(self, form):
        """Handle form validation errors."""
        if hasattr(self, "error_message"):
            messages.error(self.request, self.error_message)
        return super().form_invalid(form)

    def process_form(self, form):
        """Override this method to handle form processing."""
        return super().form_valid(form)


class AuthenticatedFormView(LoginRequiredMixin, BaseFormView):
    """
    Base form view that requires authentication.
    """

    login_url = "/users/login/"
    redirect_field_name = "next"


# ============================================================================
# Search and Filter Mixins
# ============================================================================


class SearchMixin:
    """
    Mixin for handling search functionality.
    """

    search_fields = []
    search_param = "q"

    def get_search_query(self):
        """Get search query from request."""
        return self.request.GET.get(self.search_param, "").strip()

    def apply_search(self, queryset):
        """Apply search filters to queryset."""
        query = self.get_search_query()
        if not query:
            return queryset

        if not self.search_fields:
            return queryset

        # Build Q objects for search
        search_q = Q()
        for field in self.search_fields:
            search_q |= Q(**{f"{field}__icontains": query})

        return queryset.filter(search_q)

    def get_context_data(self, **kwargs):
        """Add search context."""
        context = super().get_context_data(**kwargs)
        context["search_term"] = self.get_search_query()
        return context


class SortingMixin:
    """
    Mixin for handling sorting functionality.
    """

    sort_param = "sort"
    direction_param = "direction"
    default_sort = None
    allowed_sorts = []

    def get_sort_params(self):
        """Get sort and direction parameters."""
        sort = self.request.GET.get(self.sort_param, self.default_sort)
        direction = self.request.GET.get(self.direction_param, "asc")

        # Validate sort field
        if sort and sort not in self.allowed_sorts:
            sort = self.default_sort

        return sort, direction

    def apply_sorting(self, queryset):
        """Apply sorting to queryset."""
        sort, direction = self.get_sort_params()

        if not sort:
            return queryset

        order_field = sort
        if direction == "desc":
            order_field = f"-{sort}"

        return queryset.order_by(order_field)

    def get_context_data(self, **kwargs):
        """Add sorting context."""
        context = super().get_context_data(**kwargs)
        sort, direction = self.get_sort_params()
        context.update(
            {
                "current_sorting": f"{sort}_{direction}" if sort else "",
                "sort_field": sort,
                "sort_direction": direction,
            }
        )
        return context


class FilterMixin:
    """
    Mixin for handling category/filter functionality.
    """

    filter_param = "category"
    filter_field = "category"

    def get_filter_value(self):
        """Get filter value from request."""
        return self.request.GET.get(self.filter_param)

    def apply_filters(self, queryset):
        """Apply filters to queryset."""
        filter_value = self.get_filter_value()
        if filter_value:
            filter_kwargs = {self.filter_field: filter_value}
            queryset = queryset.filter(**filter_kwargs)
        return queryset

    def get_context_data(self, **kwargs):
        """Add filter context."""
        context = super().get_context_data(**kwargs)
        context["current_filter"] = self.get_filter_value()
        return context


# ============================================================================
# Combined View Classes
# ============================================================================


class BaseListView(SearchMixin, SortingMixin, FilterMixin, ListView):
    """
    Base list view with search, sorting, and filtering.
    """

    paginate_by = 20

    def get_queryset(self):
        """Get filtered, searched, and sorted queryset."""
        queryset = super().get_queryset()
        queryset = self.apply_filters(queryset)
        queryset = self.apply_search(queryset)
        queryset = self.apply_sorting(queryset)
        return queryset


class AuthenticatedListView(LoginRequiredMixin, BaseListView):
    """
    Authenticated list view with search, sorting, and filtering.
    """

    login_url = "/users/login/"


class BaseDetailView(UserProfileMixin, DetailView):
    """
    Base detail view with common functionality.
    """

    def get_context_data(self, **kwargs):
        """Add common detail context."""
        context = super().get_context_data(**kwargs)
        context["page_title"] = str(self.object)
        return context


# ============================================================================
# API Response Mixins
# ============================================================================


class JsonResponseMixin:
    """
    Mixin for handling JSON responses.
    """

    def json_response(self, data, status=200):
        """Return JSON response."""
        return JsonResponse(data, status=status)

    def json_success(self, data=None, message="Success"):
        """Return success JSON response."""
        response_data = {"success": True, "message": message}
        if data:
            response_data["data"] = data
        return self.json_response(response_data)

    def json_error(self, message="Error", status=400, errors=None):
        """Return error JSON response."""
        response_data = {"success": False, "message": message}
        if errors:
            response_data["errors"] = errors
        return self.json_response(response_data, status=status)


# ============================================================================
# Utility Functions for Function-Based Views
# ============================================================================


def require_user_profile(view_func):
    """
    Decorator that ensures user has a profile.
    """

    def wrapper(request, *args, **kwargs):
        if request.user.is_anonymous:
            return redirect("/users/login/")

        try:
            UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            UserProfile.objects.create(user=request.user)

        return view_func(request, *args, **kwargs)

    return wrapper


def validate_user_access(view_func):
    """
    Decorator that validates user access for user-specific views.
    """

    def wrapper(request, user_id=None, *args, **kwargs):
        if request.user.is_anonymous:
            return redirect("/users/login/")

        if user_id and str(request.user.id) != str(user_id):
            if not request.user.is_staff:
                messages.error(request, "Access denied")
                return redirect("/")

        return view_func(request, user_id, *args, **kwargs)

    return wrapper


def add_common_context(template_name, page_title="", extra_context=None):
    """
    Decorator that adds common context to template views.
    """

    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            # Get the response from the view
            response = view_func(request, *args, **kwargs)

            # If it's a render response, we can modify the context
            if hasattr(response, "context_data"):
                context = response.context_data or {}
            else:
                context = {}

            # Add common context
            context.update(
                {
                    "page_title": page_title,
                    "user_profile": None,
                }
            )

            # Add user profile if user is authenticated
            if not request.user.is_anonymous:
                try:
                    context["user_profile"] = UserProfile.objects.get(user=request.user)
                except UserProfile.DoesNotExist:
                    context["user_profile"] = UserProfile.objects.create(
                        user=request.user
                    )

            # Add extra context
            if extra_context:
                context.update(extra_context)

            return render(request, template_name, context)

        return wrapper

    return decorator
