"""
Shared utility functions to eliminate duplication across the codebase.

This module consolidates common utility functions for data processing,
array manipulation, string formatting, and other operations used throughout
the application.
"""

import logging
import os
import re
from collections.abc import Callable
from typing import Any

import numpy as np
import pandas as pd
from django.core.cache import cache

logger = logging.getLogger(__name__)

# ============================================================================
# Array and Collection Utilities
# ============================================================================


def chunk_array(array: list[Any], chunk_size: int) -> list[list[Any]]:
    """
    Split an array into chunks of specified size.

    Args:
        array: The array to split
        chunk_size: Size of each chunk

    Returns:
        List of chunks
    """
    if chunk_size <= 0:
        raise ValueError("Chunk size must be positive")

    chunks = []
    for i in range(0, len(array), chunk_size):
        chunks.append(array[i : i + chunk_size])
    return chunks


def combine_array_into_groups(array: list[Any], num_group: int = 2) -> list[list[Any]]:
    """
    Combine array elements into groups of specified size.

    Args:
        array: The array to group
        num_group: Number of elements per group (default: 2)

    Returns:
        List of grouped elements
    """
    return chunk_array(array, num_group)


def flatten_list(nested_list: list[list[Any]]) -> list[Any]:
    """
    Flatten a nested list into a single list.

    Args:
        nested_list: List of lists to flatten

    Returns:
        Flattened list
    """
    return [item for sublist in nested_list for item in sublist]


def remove_duplicates_preserve_order(items: list[Any]) -> list[Any]:
    """
    Remove duplicates from a list while preserving order.

    Args:
        items: List with potential duplicates

    Returns:
        List with duplicates removed, order preserved
    """
    seen = set()
    result = []
    for item in items:
        if item not in seen:
            seen.add(item)
            result.append(item)
    return result


def pluck_data(objects: list[dict[str, Any]], key: str) -> list[Any]:
    """
    Extract a specific key from a list of dictionaries.

    Args:
        objects: List of dictionaries
        key: Key to extract from each dictionary

    Returns:
        List of extracted values
    """
    return [obj.get(key) for obj in objects if key in obj]


def group_by_key(
    items: list[dict[str, Any]], key: str
) -> dict[Any, list[dict[str, Any]]]:
    """
    Group a list of dictionaries by a specific key.

    Args:
        items: List of dictionaries to group
        key: Key to group by

    Returns:
        Dictionary with grouped items
    """
    groups = {}
    for item in items:
        group_key = item.get(key)
        if group_key not in groups:
            groups[group_key] = []
        groups[group_key].append(item)
    return groups


# ============================================================================
# String and Text Utilities
# ============================================================================


def format_filename(filename: str) -> str:
    """
    Format filename by replacing spaces with underscores and removing special characters.

    Args:
        filename: Original filename

    Returns:
        Formatted filename
    """
    formatted = (
        filename.lower()
        .replace(" ", "_")
        .replace("-", "_")
        .replace(",", "")
        .replace("!", "_")
        .replace("(", "")
        .replace(")", "")
    )

    # Remove multiple consecutive underscores
    formatted = re.sub(r"_{2,}", "_", formatted)

    # Remove leading/trailing underscores
    formatted = formatted.strip("_")

    return formatted


def slugify(text: str) -> str:
    """
    Convert text to a URL-friendly slug.

    Args:
        text: Text to slugify

    Returns:
        URL-friendly slug
    """
    # Convert to lowercase and replace spaces/special chars with hyphens
    slug = re.sub(r"[^\w\s-]", "", text.lower())
    slug = re.sub(r"[-\s]+", "-", slug)
    return slug.strip("-")


def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
    """
    Truncate text to specified length with optional suffix.

    Args:
        text: Text to truncate
        max_length: Maximum length
        suffix: Suffix to add if truncated

    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text

    return text[: max_length - len(suffix)] + suffix


def extract_numbers(text: str) -> list[int]:
    """
    Extract all numbers from a text string.

    Args:
        text: Text to extract numbers from

    Returns:
        List of extracted numbers
    """
    return [int(match) for match in re.findall(r"\d+", text)]


def camel_to_snake(name: str) -> str:
    """
    Convert camelCase to snake_case.

    Args:
        name: camelCase string

    Returns:
        snake_case string
    """
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()


def snake_to_camel(name: str) -> str:
    """
    Convert snake_case to camelCase.

    Args:
        name: snake_case string

    Returns:
        camelCase string
    """
    components = name.split("_")
    return components[0] + "".join(word.capitalize() for word in components[1:])


# ============================================================================
# Data Processing Utilities
# ============================================================================


def safe_get_nested(data: dict[str, Any], keys: list[str], default: Any = None) -> Any:
    """
    Safely get a nested value from a dictionary.

    Args:
        data: Dictionary to search
        keys: List of keys representing the path
        default: Default value if path not found

    Returns:
        Value at the nested path or default
    """
    current = data
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default
    return current


def merge_dicts(*dicts: dict[str, Any]) -> dict[str, Any]:
    """
    Merge multiple dictionaries, with later ones taking precedence.

    Args:
        *dicts: Dictionaries to merge

    Returns:
        Merged dictionary
    """
    result = {}
    for d in dicts:
        if d:
            result.update(d)
    return result


def filter_dict_by_keys(data: dict[str, Any], keys: list[str]) -> dict[str, Any]:
    """
    Filter dictionary to only include specified keys.

    Args:
        data: Dictionary to filter
        keys: Keys to include

    Returns:
        Filtered dictionary
    """
    return {key: data[key] for key in keys if key in data}


def exclude_dict_keys(data: dict[str, Any], keys: list[str]) -> dict[str, Any]:
    """
    Exclude specified keys from dictionary.

    Args:
        data: Dictionary to filter
        keys: Keys to exclude

    Returns:
        Dictionary with excluded keys removed
    """
    return {key: value for key, value in data.items() if key not in keys}


# ============================================================================
# Pandas/NumPy Utilities
# ============================================================================


def get_sheet_values(sheet) -> list[list[Any]]:
    """
    Extract non-empty rows from a sheet/dataframe.

    Args:
        sheet: Sheet or dataframe object

    Returns:
        List of non-empty rows
    """
    return [row for row in sheet.values if any(row)]


def build_dataframe_from_dict(data: dict[str, Any]) -> pd.DataFrame:
    """
    Build a pandas DataFrame from a dictionary.

    Args:
        data: Dictionary with data

    Returns:
        pandas DataFrame
    """
    return pd.DataFrame([data])


def safe_array_conversion(data: Any, dtype: str = "int64") -> np.ndarray:
    """
    Safely convert data to numpy array with specified dtype.

    Args:
        data: Data to convert
        dtype: Target data type

    Returns:
        numpy array
    """
    try:
        return np.array(data, dtype=dtype)
    except (ValueError, TypeError) as e:
        logger.warning(f"Failed to convert to {dtype}: {e}")
        return np.array(data)


# ============================================================================
# File and Path Utilities
# ============================================================================


def safe_file_rename(original_path: str, new_path: str) -> bool:
    """
    Safely rename a file with error handling.

    Args:
        original_path: Original file path
        new_path: New file path

    Returns:
        True if successful, False otherwise
    """
    try:
        os.rename(original_path, new_path)
        logger.info(f"Renamed '{original_path}' to '{new_path}'")
        return True
    except Exception as e:
        logger.error(f"Failed to rename '{original_path}' to '{new_path}': {e}")
        return False


def ensure_directory_exists(directory_path: str) -> bool:
    """
    Ensure a directory exists, creating it if necessary.

    Args:
        directory_path: Path to directory

    Returns:
        True if directory exists or was created successfully
    """
    try:
        os.makedirs(directory_path, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Failed to create directory '{directory_path}': {e}")
        return False


def get_file_extension(filename: str) -> str:
    """
    Get file extension from filename.

    Args:
        filename: Name of the file

    Returns:
        File extension (including the dot)
    """
    return os.path.splitext(filename)[1].lower()


def is_allowed_file_type(filename: str, allowed_extensions: list[str]) -> bool:
    """
    Check if file type is allowed based on extension.

    Args:
        filename: Name of the file
        allowed_extensions: List of allowed extensions (with dots)

    Returns:
        True if file type is allowed
    """
    return get_file_extension(filename) in [ext.lower() for ext in allowed_extensions]


# ============================================================================
# Caching Utilities
# ============================================================================


def get_or_set_cache(key: str, callable_func: Callable, timeout: int = 300) -> Any:
    """
    Get value from cache or set it using a callable function.

    Args:
        key: Cache key
        callable_func: Function to call if cache miss
        timeout: Cache timeout in seconds

    Returns:
        Cached or computed value
    """
    cached_value = cache.get(key)
    if cached_value is not None:
        return cached_value

    value = callable_func()
    cache.set(key, value, timeout=timeout)
    return value


def invalidate_cache_pattern(pattern: str) -> int:
    """
    Invalidate cache keys matching a pattern.

    Args:
        pattern: Pattern to match (simple string matching)

    Returns:
        Number of keys invalidated
    """
    # Note: This is a simplified implementation
    # In production, you might want to use Redis SCAN or similar
    try:
        if hasattr(cache, "_cache"):
            keys_to_delete = [key for key in cache._cache.keys() if pattern in str(key)]
            for key in keys_to_delete:
                cache.delete(key)
            return len(keys_to_delete)
    except Exception as e:
        logger.warning(f"Failed to invalidate cache pattern '{pattern}': {e}")

    return 0


# ============================================================================
# Validation Utilities
# ============================================================================


def is_valid_email(email: str) -> bool:
    """
    Validate email address format.

    Args:
        email: Email address to validate

    Returns:
        True if email format is valid
    """
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return re.match(pattern, email) is not None


def is_valid_phone(phone: str) -> bool:
    """
    Validate phone number format (basic validation).

    Args:
        phone: Phone number to validate

    Returns:
        True if phone format is valid
    """
    # Remove all non-digit characters
    digits_only = re.sub(r"\D", "", phone)
    # Check if it has 10-15 digits (international range)
    return 10 <= len(digits_only) <= 15


def sanitize_input(text: str, max_length: int = 1000) -> str:
    """
    Sanitize user input by removing potentially harmful characters.

    Args:
        text: Text to sanitize
        max_length: Maximum allowed length

    Returns:
        Sanitized text
    """
    if not text:
        return ""

    # Remove HTML tags and script content
    text = re.sub(r"<[^>]+>", "", text)

    # Remove potentially harmful characters
    text = re.sub(r'[<>"\']', "", text)

    # Truncate to max length
    return text[:max_length]
