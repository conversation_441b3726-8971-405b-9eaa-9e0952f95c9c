"""
Consolidated API router utilities to reduce duplication in API endpoint definitions.

This module provides base router classes, common decorators, and standardized
patterns for API endpoint registration and response handling.
"""

from collections.abc import Callable
from functools import wraps
from typing import Any

from django.core.paginator import Paginator
from django.db.models import Model, QuerySet
from django.shortcuts import get_object_or_404
from loguru import logger
from ninja import Query, Router, Schema
from ninja.errors import HttpError

from helpers.error_handlers import handle_api_error
from helpers.schemas import MessageOut, NotFoundSchema, Validation422Schema

# ============================================================================
# Base Router Classes
# ============================================================================


class BaseRouter(Router):
    """
    Enhanced Router with common functionality and standardized patterns.
    """

    def __init__(self, tags: list[str] | None = None, **kwargs):
        """Initialize router with default tags."""
        super().__init__(tags=tags or [], **kwargs)
        self.default_responses = {
            400: MessageOut,
            404: NotFoundSchema,
            422: Validation422Schema,
            500: MessageOut,
        }

    def get_with_standard_responses(
        self,
        path: str,
        response_schema: type[Schema],
        tags: list[str] | None = None,
        **kwargs,
    ):
        """
        GET endpoint decorator with standardized error responses.
        """
        responses = {200: response_schema, **self.default_responses}
        return self.get(path, response=responses, tags=tags or self.tags, **kwargs)

    def post_with_standard_responses(
        self,
        path: str,
        response_schema: type[Schema],
        tags: list[str] | None = None,
        **kwargs,
    ):
        """
        POST endpoint decorator with standardized error responses.
        """
        responses = {201: response_schema, **self.default_responses}
        return self.post(path, response=responses, tags=tags or self.tags, **kwargs)

    def put_with_standard_responses(
        self,
        path: str,
        response_schema: type[Schema],
        tags: list[str] | None = None,
        **kwargs,
    ):
        """
        PUT endpoint decorator with standardized error responses.
        """
        responses = {200: response_schema, **self.default_responses}
        return self.put(path, response=responses, tags=tags or self.tags, **kwargs)

    def delete_with_standard_responses(
        self, path: str, tags: list[str] | None = None, **kwargs
    ):
        """
        DELETE endpoint decorator with standardized error responses.
        """
        responses = {204: None, **self.default_responses}
        return self.delete(path, response=responses, tags=tags or self.tags, **kwargs)


# ============================================================================
# Common Endpoint Patterns
# ============================================================================


class CRUDRouter(BaseRouter):
    """
    Router with built-in CRUD operations for a model.
    """

    def __init__(
        self,
        model: type[Model],
        schema_out: type[Schema],
        schema_in: type[Schema] | None = None,
        tags: list[str] | None = None,
        **kwargs,
    ):
        """
        Initialize CRUD router for a specific model.

        Args:
            model: Django model class
            schema_out: Output schema for the model
            schema_in: Input schema for create/update operations
            tags: API tags for endpoints
        """
        super().__init__(tags=tags, **kwargs)
        self.model = model
        self.schema_out = schema_out
        self.schema_in = schema_in or schema_out
        self._register_crud_endpoints()

    def _register_crud_endpoints(self):
        """Register standard CRUD endpoints."""

        # List endpoint
        @self.get_with_standard_responses("/", list[self.schema_out])
        @handle_api_error
        def list_items(
            request,
            page: int = Query(1, gt=0),
            page_size: int = Query(20, gt=0, le=100),
        ):
            """List all items with pagination."""
            queryset = self.get_queryset(request)
            paginator = Paginator(queryset, page_size)
            return paginator.get_page(page)

        # Detail endpoint
        @self.get_with_standard_responses("/{int:item_id}", self.schema_out)
        @handle_api_error
        def get_item(request, item_id: int):
            """Get a specific item by ID."""
            return get_object_or_404(self.model, pk=item_id)

        # Create endpoint (if schema_in is provided)
        if self.schema_in:

            @self.post_with_standard_responses("/", self.schema_out)
            @handle_api_error
            def create_item(request, payload: self.schema_in):
                """Create a new item."""
                return self.create_object(request, payload)

            @self.put_with_standard_responses("/{int:item_id}", self.schema_out)
            @handle_api_error
            def update_item(request, item_id: int, payload: self.schema_in):
                """Update an existing item."""
                obj = get_object_or_404(self.model, pk=item_id)
                return self.update_object(request, obj, payload)

        # Delete endpoint
        @self.delete_with_standard_responses("/{int:item_id}")
        @handle_api_error
        def delete_item(request, item_id: int):
            """Delete an item."""
            obj = get_object_or_404(self.model, pk=item_id)
            self.delete_object(request, obj)
            return None

    def get_queryset(self, request) -> QuerySet:
        """
        Get the base queryset for list operations.
        Override in subclasses for filtering/permissions.
        """
        return self.model.objects.all()

    def create_object(self, request, payload: Schema) -> Model:
        """
        Create a new object from the payload.
        Override in subclasses for custom creation logic.
        """
        data = payload.dict()
        return self.model.objects.create(**data)

    def update_object(self, request, obj: Model, payload: Schema) -> Model:
        """
        Update an existing object with the payload.
        Override in subclasses for custom update logic.
        """
        data = payload.dict(exclude_unset=True)
        for field, value in data.items():
            setattr(obj, field, value)
        obj.save()
        return obj

    def delete_object(self, request, obj: Model) -> None:
        """
        Delete an object.
        Override in subclasses for custom deletion logic.
        """
        obj.delete()


# ============================================================================
# Common Decorators
# ============================================================================


def paginated_response(
    page_param: str = "page",
    page_size_param: str = "page_size",
    default_page_size: int = 20,
    max_page_size: int = 100,
):
    """
    Decorator for adding pagination to list endpoints.
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(
            request,
            page: int = Query(1, gt=0, alias=page_param),
            page_size: int = Query(
                default_page_size, gt=0, le=max_page_size, alias=page_size_param
            ),
            *args,
            **kwargs,
        ):
            # Get the queryset from the original function
            queryset = func(request, *args, **kwargs)

            # Apply pagination
            paginator = Paginator(queryset, page_size)
            return paginator.get_page(page)

        return wrapper

    return decorator


def require_object_access(model: type[Model], param_name: str = "item_id"):
    """
    Decorator that validates object access and adds it to kwargs.
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            object_id = kwargs.get(param_name)
            if object_id:
                obj = get_object_or_404(model, pk=object_id)
                kwargs["object"] = obj
            return func(request, *args, **kwargs)

        return wrapper

    return decorator


def user_specific_endpoint(user_param: str = "user_id"):
    """
    Decorator for endpoints that operate on user-specific data.
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            user_id = kwargs.get(user_param)

            # Validate user access
            if user_id and str(request.user.id) != str(user_id):
                if not request.user.is_staff:
                    raise HttpError(403, "Access denied")

            return func(request, *args, **kwargs)

        return wrapper

    return decorator


# ============================================================================
# Response Helpers
# ============================================================================


def success_response(
    message: str, data: dict[str, Any] | None = None
) -> dict[str, Any]:
    """
    Create a standardized success response.
    """
    response = {"success": True, "message": message}
    if data:
        response["data"] = data
    return response


def error_response(message: str, error_code: str | None = None) -> dict[str, Any]:
    """
    Create a standardized error response.
    """
    response = {"success": False, "message": message}
    if error_code:
        response["error_code"] = error_code
    return response


# ============================================================================
# Router Registration Utilities
# ============================================================================


class RouterRegistry:
    """
    Central registry for managing API routers.
    """

    def __init__(self):
        self.routers: dict[str, dict[str, Any]] = {}

    def register(
        self,
        name: str,
        router: Router,
        prefix: str,
        tags: list[str] | None = None,
        **kwargs,
    ):
        """
        Register a router with metadata.
        """
        self.routers[name] = {
            "router": router,
            "prefix": prefix,
            "tags": tags or [],
            "metadata": kwargs,
        }
        logger.info(f"Registered router '{name}' with prefix '{prefix}'")

    def get_router(self, name: str) -> Router | None:
        """Get a registered router by name."""
        return self.routers.get(name, {}).get("router")

    def add_all_to_api(self, api):
        """
        Add all registered routers to an API instance.
        """
        for name, config in self.routers.items():
            api.add_router(config["prefix"], config["router"], tags=config["tags"])
            logger.info(f"Added router '{name}' to API")

    def list_routers(self) -> dict[str, dict[str, Any]]:
        """List all registered routers with their metadata."""
        return {
            name: {
                "prefix": config["prefix"],
                "tags": config["tags"],
                "endpoint_count": len(config["router"].urls),
                **config["metadata"],
            }
            for name, config in self.routers.items()
        }


# Global router registry instance
router_registry = RouterRegistry()


# ============================================================================
# Convenience Functions
# ============================================================================


def create_simple_router(
    name: str, prefix: str, tags: list[str] | None = None
) -> BaseRouter:
    """
    Create and register a simple router.
    """
    router = BaseRouter(tags=tags)
    router_registry.register(name, router, prefix, tags)
    return router


def create_crud_router(
    name: str,
    prefix: str,
    model: type[Model],
    schema_out: type[Schema],
    schema_in: type[Schema] | None = None,
    tags: list[str] | None = None,
) -> CRUDRouter:
    """
    Create and register a CRUD router.
    """
    router = CRUDRouter(model, schema_out, schema_in, tags)
    router_registry.register(name, router, prefix, tags)
    return router
