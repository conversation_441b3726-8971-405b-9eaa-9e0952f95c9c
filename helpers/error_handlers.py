import logging
import re
from collections.abc import Callable
from functools import wraps
from typing import Any

from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from ninja.errors import HttpError

logger = logging.getLogger(__name__)


def validate_param_rules(**param_rules):
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            for param, rule in param_rules.items():
                value = kwargs.get(param)
                if value is None:
                    raise HttpError(400, f"Missing required parameter: {param}")

                # Parse rule
                if match := re.match(r"gt(\d+)", rule):
                    threshold = int(match.group(1))
                    if value <= threshold:
                        raise HttpError(
                            400, f"{param} must be greater than {threshold}"
                        )
                elif match := re.match(r"gte(\d+)", rule):
                    threshold = int(match.group(1))
                    if value < threshold:
                        raise HttpError(
                            400, f"{param} must be greater than or equal to {threshold}"
                        )
                elif match := re.match(r"lt(\d+)", rule):
                    threshold = int(match.group(1))
                    if value >= threshold:
                        raise HttpError(400, f"{param} must be less than {threshold}")
                elif match := re.match(r"lte(\d+)", rule):
                    threshold = int(match.group(1))
                    if value > threshold:
                        raise HttpError(
                            400, f"{param} must be less than or equal to {threshold}"
                        )
                elif rule == "positive":
                    if value <= 0:
                        raise HttpError(400, f"{param} must be positive")

            return func(request, *args, **kwargs)

        return wrapper

    return decorator


def validate_user_access(request, user_id: int) -> None:
    """Validate user has access to the requested resource."""
    if not request.user.is_authenticated:
        raise ValueError("Authentication required")
    if not request.user.is_staff and request.user.id != user_id:
        raise ValueError("Unauthorized access")


def handle_api_error(func: Callable[..., Any]) -> Callable[..., Any]:
    """
    Unified decorator for standardizing error handling across all API endpoints.

    This decorator converts domain-level exceptions into appropriate HTTP responses:
    - ObjectDoesNotExist -> 404 with structured error message
    - ValueError -> 400 with error details
    - ValidationError -> 400 with validation details
    - PermissionError -> 403 with permission details
    - Exception -> 500 with generic error (details hidden in production)

    Returns:
        For legacy endpoints: tuple[int, dict] format
        For modern endpoints: raises HttpError for Django-Ninja
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)

        # 404 errors
        except ObjectDoesNotExist as exc:
            error_msg = f"Resource not found: {exc}"
            logger.warning("404 error in %s: %s", func.__name__, error_msg)

            # Check if function expects tuple return (legacy) or HttpError (modern)
            if _is_legacy_endpoint(func):
                return 404, {"message": error_msg}
            else:
                raise HttpError(404, error_msg) from exc

        # 400 errors
        except (ValueError, ValidationError) as exc:
            error_msg = str(exc)
            logger.warning("400 error in %s: %s", func.__name__, error_msg)

            if _is_legacy_endpoint(func):
                return 400, {"message": error_msg}
            else:
                raise HttpError(400, error_msg) from exc

        # 403 errors
        except PermissionError as exc:
            error_msg = f"Permission denied: {exc}"
            logger.warning("403 error in %s: %s", func.__name__, error_msg)

            if _is_legacy_endpoint(func):
                return 403, {"message": error_msg}
            else:
                raise HttpError(403, error_msg) from exc

        # 500 errors
        except Exception as exc:  # noqa: BLE001 – top-level fallback
            # Capture stack trace for observability
            logger.exception("Unhandled exception in %s", func.__name__)

            # In production, don't leak internals; use generic message
            detail = str(exc) if settings.DEBUG else "Internal server error"

            if _is_legacy_endpoint(func):
                return 500, {"message": detail}
            else:
                raise HttpError(500, detail) from exc

    return wrapper


def _is_legacy_endpoint(func: Callable) -> bool:
    """
    Determine if an endpoint uses legacy tuple return format or modern HttpError format.

    Legacy endpoints return tuple[int, dict], modern ones raise HttpError.
    This is determined by checking the function's return type annotations or
    by checking if the response schema includes status codes.
    """
    # Check if function has response annotation with status codes (legacy pattern)
    if hasattr(func, "__annotations__"):
        return_annotation = func.__annotations__.get("return")
        if return_annotation and "tuple" in str(return_annotation).lower():
            return True

    # Check if function is decorated with @router with response dict (legacy pattern)
    if hasattr(func, "_ninja_operation"):
        operation = func._ninja_operation
        if hasattr(operation, "response") and isinstance(operation.response, dict):
            return True

    # Default to modern HttpError approach for new endpoints
    return False


# Aliases for backward compatibility
handle_api_error_new = handle_api_error
