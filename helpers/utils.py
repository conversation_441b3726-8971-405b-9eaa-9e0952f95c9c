import logging
import re
from functools import wraps

from ninja.errors import HttpError

# Import consolidated error handlers

logger = logging.getLogger(__name__)


def validate_param_rules(**param_rules):
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            for param, rule in param_rules.items():
                value = kwargs.get(param)
                if value is None:
                    raise HttpError(400, f"Missing required parameter: {param}")

                # Parse rule
                if match := re.match(r"gt(\d+)", rule):
                    threshold = int(match.group(1))
                    if value <= threshold:
                        raise HttpError(
                            400, f"{param} must be greater than {threshold}"
                        )
                elif match := re.match(r"gte(\d+)", rule):
                    threshold = int(match.group(1))
                    if value < threshold:
                        raise HttpError(400, f"{param} must be at least {threshold}")
                elif match := re.match(r"lt(\d+)", rule):
                    threshold = int(match.group(1))
                    if value >= threshold:
                        raise HttpError(400, f"{param} must be less than {threshold}")
                elif match := re.match(r"lte(\d+)", rule):
                    threshold = int(match.group(1))
                    if value > threshold:
                        raise HttpError(400, f"{param} must be at most {threshold}")
                elif match := re.match(r"between(\d+)_(\d+)", rule):
                    low, high = int(match.group(1)), int(match.group(2))
                    if not (low <= value <= high):
                        raise HttpError(
                            400, f"{param} must be between {low} and {high}"
                        )
                else:
                    raise HttpError(400, f"Unknown validation rule: {rule}")

            return func(request, *args, **kwargs)

        return wrapper

    return decorator


# All error handling functions moved to helpers/error_handlers.py
# Import them from there to avoid duplication
