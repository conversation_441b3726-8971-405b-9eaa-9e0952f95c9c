import itertools
import warnings

import numpy as np
import pandas as pd
from django.contrib.auth.models import User
from loguru import logger
from openpyxl import load_workbook

from questionaire.models import Question, Reply
from users.models import UserProfile

warnings.filterwarnings("ignore")


def get_values(sheet):
    results = []
    for row in sheet.values:
        if not any(value for value in row):
            continue
        results.append(row)
    return results


class Recommender:
    def get_recommendations(self, id):
        product_data = load_workbook(
            filename=("src/data_science/product_data.xlsx"),
            data_only=True,
        )
        shampoo = get_values(product_data["Shampoo"])
        conditioner = get_values(product_data["Conditioners"])
        logger.info(f"\n\t Worksheet names: {product_data.sheetnames} \n")
        shamp_info = get_values(product_data["Shampoo_o"])
        cond_info = get_values(product_data["Conditioners_o"])
        user = User.objects.get(id=id)
        user_profile = UserProfile.objects.get(user=user)
        # goal_question = question = Question.objects.get(position=1)
        goal_question = Question.objects.get(position=1)
        issue_question = Question.objects.get(position=2)
        head_state_question = Question.objects.get(position=8)
        preferences_question = Question.objects.get(position=42)
        # try:
        sample_goal = Reply.objects.get(user=user, question=goal_question).text
        logger.info(f"\n\tGOAL TEXTS: {sample_goal}\n")
        goal = Reply.objects.get(user=user, question=goal_question).text
        # catch(Exception as e)
        issue = Reply.objects.get(user=user, question=issue_question).text
        hair_texture = Reply.objects.get(user=user, question=head_state_question).text
        # preferences_old = Reply.objects.get(user=user, question=preferences_question).text
        try:
            preferences = Reply.objects.get(
                user=user, question=preferences_question
            ).text
        except Reply.DoesNotExist:
            preferences = None

        q_answers = [
            (
                "Customer_ID",
                "Goal ",
                "Issue ",
                "Hair Type ",
                "Hair Texture",
                "Preferences ",
            ),
            (user.id, goal, issue, user_profile.hair_type, hair_texture, preferences),
        ]

        required = {
            "issue": 2,
            "hair_overall": 3,
            "split_ends": 7,
            "daily_water": 24,
            "hair_feels": 30,
            "treatments": 34,
            "enhancers": 35,
            "hair_behaviour": 37,
            "styling_hair": 38,
            "apply_heat": 39,
            "scalp_feeling": 41,
            "scalp flaky": 42,
            "scalp oily": 43,
            "dry_scalp": 44,
        }

        results = [user.username, user.email, user.id]
        for value in required.values():
            question = Question.objects.get(position=value)
            reply = Reply.objects.get(user=user, question=question)
            if reply.text:
                results.append(reply.text)
            if reply.answer:
                results.append(reply.answer.score)

        required_calculations = [
            "total1",
            "dryness_max_score",
            "percentage_dry1",
            "total2",
            "damage_max_score",
            "percentage_dam",
            "total3",
            "sensitivity_max_score",
            "percentage_sen",
            "total4",
            "sebum_max_score_o",
            "percentage_oil",
            "total5",
            "sebum_max_score_dry",
            "percentage_dsc",
            "total6",
            "flakes_max_score",
            "percentage_fl",
            "added_points",
        ]
        for heading in required_calculations:
            # calculate scores
            if heading != "add_points":
                results.append(0)
            else:
                results.append(None)
            # results.append(getattr(user_profile, heading))
        results_tuple = tuple(results)

        q_scorecard = [
            (
                "Name ",
                "Email ",
                "Customer ID",
                "Issue ",
                "Issue score",
                "Hair Overall ",
                "Overall Score ",
                "Split Ends",
                "Split ends score",
                "Daily Water ",
                "Daily water score ",
                "Hair Feels ",
                "Hair feels score ",
                "Treatments",
                "Treatment Score ",
                "Enhancers",
                "Enhancers Score ",
                "Hair Behaviour",
                "Hair Behaviour Score ",
                "Styling Hair ",
                "Styling Hair Score ",
                "Apply Heat ",
                "Apply Heat Score ",
                "Scalp Feeling",
                "Scalp feeling Score Scalp Flaky",
                "Scalp Flaky Score ",
                "Scalp Oily ",
                "Scalp Oily Score ",
                "Dry Scalp",
                "Dry Scalp Score ",
                "Total 1",
                "Dryness Max Score",
                "Percentage Dry1",
                "Total 2",
                "Damage Max Score",
                "Percentage Dam",
                "Total 3",
                "Sensitivity Max Score",
                "Percentage SEN",
                "Total 4",
                "Sebum Max Score Oily",
                "Percentage Oil",
                "Total 5",
                "Sebum Max Score Dry",
                "Percentage DSC",
                "Total 6",
                "Flakes Max Score",
                "Percentage FL ",
                "Added points ",
            ),
            results_tuple,
        ]

        # q_scorecard = load_workbook(filename='data_sci_score_card.xlsx', data_only=True)['Output Data ']
        # q_scorecard =  get_values(q_scorecard)
        # print(q_scorecard)

        # Convert to a DataFrame
        df__s = pd.DataFrame(shampoo)
        df__c = pd.DataFrame(conditioner)
        ds__i = pd.DataFrame(shamp_info)
        dc__i = pd.DataFrame(cond_info)
        df_qa = pd.DataFrame(q_answers)
        df_qsc = pd.DataFrame(q_scorecard)

        # line 1 of spreadsheets contains the name of the columns
        header_row1 = df__s.iloc[0]
        header_row2 = df__c.iloc[0]
        header_row3 = ds__i.iloc[0]
        header_row4 = dc__i.iloc[0]
        header_row5 = df_qa.iloc[0]
        header_row6 = df_qsc.iloc[0]

        # changing the name of the columns in the DataFrames
        ds = pd.DataFrame(df__s.values[1:], columns=header_row1)
        dc = pd.DataFrame(df__c.values[1:], columns=header_row2)
        pd.DataFrame(ds__i.values[1:], columns=header_row3)
        pd.DataFrame(dc__i.values[1:], columns=header_row4)
        # .convert_dtypes(infer_objects=True, convert_string=True, convert_integer=False, convert_boolean=False, convert_floating=False)
        d_a = pd.DataFrame(df_qa.values[1:], columns=header_row5)
        d_sc = pd.DataFrame(df_qsc.values[1:], columns=header_row6)

        # selecting what is needed from the datasets
        dsc = d_sc[
            [
                "Customer ID",
                "Percentage Dry1",
                "Percentage Dam",
                "Percentage SEN",
                "Percentage Oil",
                "Percentage DSC",
                "Percentage FL ",
            ]
        ].set_index("Customer ID")

        # converting the types of the data from DSC
        dsc["Percentage Dry1"] = pd.to_numeric(dsc["Percentage Dry1"])
        dsc["Percentage Dam"] = pd.to_numeric(dsc["Percentage Dam"])
        dsc["Percentage SEN"] = pd.to_numeric(dsc["Percentage SEN"])
        dsc["Percentage Oil"] = pd.to_numeric(dsc["Percentage Oil"])
        dsc["Percentage DSC"] = pd.to_numeric(dsc["Percentage DSC"])
        dsc["Percentage FL "] = pd.to_numeric(dsc["Percentage FL "])

        # getting the highest scores within the DSC dataset
        dsc["Highest_Issue"] = dsc.idxmax(axis=1)

        # organising the User Rating for product
        dc["User_Rating"] = np.where(
            dc["User Rating"].str.contains("Good"),
            "1",
            np.where(dc["User Rating"].str.contains("High"), "2", "0"),
        ).astype("int32")

        ds["User_Rating"] = np.where(
            ds["User Rating"].str.contains("Good"),
            "1",
            np.where(ds["User Rating"].str.contains("High"), "2", "0"),
        ).astype("int32")

        # renaming columns
        ds = ds.rename(columns={"Hair Type": "Hair_Type", "Product ": "Shampoo_name"})
        dc = dc.rename(
            columns={"Hair Type": "Hair_Type", "Product ": "Conditioner_name"},
        )
        ds.rename(columns={"Hair Type": "Hair_Type", "Product ": "Shampoo_name"})
        dc.rename(
            columns={"Hair Type": "Hair_Type", "Product ": "Conditioner_name"},
        )
        d_a = d_a.rename(
            columns={"Prefrences": "Preferences", "Customer ID": "Customer_ID"},
        )

        # organising and replacing strings into int
        # Hair Type
        d_a["Hair_TypeID"] = np.where(
            d_a["Hair Type "].str.contains("Type 2a"),
            "1",
            np.where(
                d_a["Hair Type "].str.contains("Type 2b"),
                "2",
                np.where(
                    d_a["Hair Type "].str.contains("Type 2c"),
                    "3",
                    np.where(
                        d_a["Hair Type "].str.contains("Type 3a"),
                        "4",
                        np.where(
                            d_a["Hair Type "].str.contains("Type 3b"),
                            "5",
                            np.where(
                                d_a["Hair Type "].str.contains("Type 3c"),
                                "6",
                                np.where(
                                    d_a["Hair Type "].str.contains("Type 4a"),
                                    "7",
                                    np.where(
                                        d_a["Hair Type "].str.contains("Type 4b"),
                                        "8",
                                        np.where(
                                            d_a["Hair Type "].str.contains("Type 4c"),
                                            "9",
                                            "0",
                                        ),
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
        )

        ds["Hair_TypeID"] = np.where(
            ds["Hair_Type"].str.contains("2A"),
            "1",
            np.where(
                ds["Hair_Type"].str.contains("2B"),
                "2",
                np.where(
                    ds["Hair_Type"].str.contains("2C"),
                    "3",
                    np.where(
                        ds["Hair_Type"].str.contains("3A"),
                        "4",
                        np.where(
                            ds["Hair_Type"].str.contains("3B"),
                            "5",
                            np.where(
                                ds["Hair_Type"].str.contains("3C"),
                                "6",
                                np.where(
                                    ds["Hair_Type"].str.contains("4A"),
                                    "7",
                                    np.where(
                                        ds["Hair_Type"].str.contains("4B"),
                                        "8",
                                        np.where(
                                            ds["Hair_Type"].str.contains("4C"),
                                            "9",
                                            "0",
                                        ),
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
        )

        dc["Hair_TypeID"] = np.where(
            dc["Hair_Type"].str.contains("2A"),
            "1",
            np.where(
                dc["Hair_Type"].str.contains("2B"),
                "2",
                np.where(
                    dc["Hair_Type"].str.contains("2C"),
                    "3",
                    np.where(
                        dc["Hair_Type"].str.contains("3A"),
                        "4",
                        np.where(
                            dc["Hair_Type"].str.contains("3B"),
                            "5",
                            np.where(
                                dc["Hair_Type"].str.contains("3C"),
                                "6",
                                np.where(
                                    dc["Hair_Type"].str.contains("4A"),
                                    "7",
                                    np.where(
                                        dc["Hair_Type"].str.contains("4B"),
                                        "8",
                                        np.where(
                                            dc["Hair_Type"].str.contains("4C"),
                                            "9",
                                            "0",
                                        ),
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
        )

        # TextureID
        ds["TextureID"] = np.where(
            ds["Texture "].str.contains("Fine"),
            "10",
            np.where(
                ds["Texture "].str.contains("Medium"),
                "11",
                np.where(ds["Texture "].str.contains("Thick"), "12", "0"),
            ),
        )

        d_a["TextureID"] = np.where(
            d_a["Hair Texture"].str.contains("Fine"),
            "10",
            np.where(
                d_a["Hair Texture"].str.contains("Medium"),
                "11",
                np.where(d_a["Hair Texture"].str.contains("Thick"), "12", "0"),
            ),
        )

        dc["TextureID"] = np.where(
            dc["Texture "].str.contains("Fine"),
            "10",
            np.where(
                dc["Texture "].str.contains("Medium"),
                "11",
                np.where(dc["Texture "].str.contains("Thick"), "12", "0"),
            ),
        )

        # PorosityID - There is no data from customers here
        ds["PorosityID"] = np.where(
            ds["Porosity"].str.contains("Low"),
            "13",
            np.where(
                ds["Porosity"].str.contains("Medium"),
                "14",
                np.where(ds["Porosity"].str.contains("High"), "15", "0"),
            ),
        )

        dc["PorosityID"] = np.where(
            dc["Porosity"].str.contains("Low"),
            "13",
            np.where(
                dc["Porosity"].str.contains("Medium"),
                "14",
                np.where(dc["Porosity"].str.contains("High"), "15", "0"),
            ),
        )

        # GoalsID
        d_a["GoalsID"] = np.where(
            d_a["Goal "].str.contains("Growth"),
            "16",
            np.where(
                d_a["Goal "].str.contains("Healthy"),
                "17",
                np.where(
                    d_a["Goal "].str.contains("Moisture"),
                    "18",
                    np.where(d_a["Goal "].str.contains("Ultra"), "19", "0"),
                ),
            ),
        )

        ds["GoalsID"] = np.where(
            ds["Hair Goals"].str.contains("Growth"),
            "16",
            np.where(
                ds["Hair Goals"].str.contains("Healthy"),
                "17",
                np.where(
                    ds["Hair Goals"].str.contains("Moisture"),
                    "18",
                    np.where(ds["Hair Goals"].str.contains("Ultra"), "19", "0"),
                ),
            ),
        )

        dc["GoalsID"] = np.where(
            dc["Hair Goals"].str.contains("Growth"),
            "16",
            np.where(
                dc["Hair Goals"].str.contains("Healthy"),
                "17",
                np.where(
                    dc["Hair Goals"].str.contains("Moisture"),
                    "18",
                    np.where(dc["Hair Goals"].str.contains("Ultra"), "19", "0"),
                ),
            ),
        )

        # IssueID
        d_a["IssuesID"] = np.where(
            d_a["Issue "].str.contains("Scalp dryness"),
            "20",
            np.where(
                d_a["Issue "].str.contains("breakage"),
                "21",
                np.where(
                    d_a["Issue "].str.contains("Thinning"),
                    "22",
                    np.where(d_a["Issue "].str.contains("Not me"), "23", "0"),
                ),
            ),
        )

        ds["IssuesID"] = np.where(
            ds["Hair Issues"].str.contains("Scalp dryness"),
            "20",
            np.where(
                ds["Hair Issues"].str.contains("Breakage"),
                "21",
                np.where(
                    ds["Hair Issues"].str.contains("Thinning"),
                    "22",
                    np.where(ds["Hair Issues"].str.contains("Not me"), "23", "0"),
                ),
            ),
        )

        dc["IssuesID"] = np.where(
            dc["Hair Issues"].str.contains("Scalp dryness"),
            "20",
            np.where(
                dc["Hair Issues"].str.contains("Breakage"),
                "21",
                np.where(
                    dc["Hair Issues"].str.contains("Thinning"),
                    "22",
                    np.where(dc["Hair Issues"].str.contains("Not me"), "23", "0"),
                ),
            ),
        )

        # selecting columns
        customer = d_a[
            ["Customer_ID", "Hair_TypeID", "TextureID", "GoalsID", "IssuesID"]
        ]
        dsc[["Highest_Issue"]].reset_index()
        shamp = ds.loc[
            :,
            [
                "Shampoo_name",
                "GoalsID",
                "IssuesID",
                "TextureID",
                "Hair_TypeID",
                "PorosityID",
            ],
        ]
        cond = dc.loc[
            :,
            [
                "Conditioner_name",
                "GoalsID",
                "IssuesID",
                "TextureID",
                "Hair_TypeID",
                "PorosityID",
            ],
        ]

        # merging answers dataset with score card dataset and selecting columns of interest
        # customer = customer.merge(dsc2, left_on='Customer_ID', right_on='Customer ID').loc[:, ['Customer_ID', 'Hair_TypeID', 'TextureID', 'GoalsID', 'IssuesID']]
        # customer['Customer_ID'] = pd.to_numeric(customer['Customer_ID'])

        # for col in ds_i.columns:
        #     print(col)

        # Category
        # Shampoo_name
        # User Rating
        # Hair_Type
        # Porosity
        # Texture
        # Hair Goals
        # Hair Issues
        # User_Rating

        # Separating the matches for the recommendation dictionary

        # working with the dictionary of matches, this will be useful when the datasets are fulfilled

        dict_info = load_workbook("src/data_science/Dict_info.xlsx", data_only=True)
        dict_hair = get_values(dict_info["Hair_Type"])
        dict_texture = get_values(dict_info["Texture"])
        dict_porosity = get_values(dict_info["Porosity"])
        dict_goals = get_values(dict_info["Hair_Goals"])
        dict_issues = get_values(dict_info["Hair_Issues"])

        df_dict_hair = pd.DataFrame(dict_hair)
        df_dict_texture = pd.DataFrame(dict_texture)
        df_dict_porosity = pd.DataFrame(dict_porosity)
        df_dict_goals = pd.DataFrame(dict_goals)
        df_dict_issues = pd.DataFrame(dict_issues)

        header_row_hair = df_dict_hair.iloc[0]
        header_row_texture = df_dict_texture.iloc[0]
        header_row_porosity = df_dict_porosity.iloc[0]
        header_row_goals = df_dict_goals.iloc[0]
        header_row_issues = df_dict_issues.iloc[0]

        ddict_hair = pd.DataFrame(df_dict_hair.values[1:], columns=header_row_hair)
        ddict_texture = pd.DataFrame(
            df_dict_texture.values[1:],
            columns=header_row_texture,
        )
        ddict_porosity = pd.DataFrame(
            df_dict_porosity.values[1:],
            columns=header_row_porosity,
        )
        ddict_goals = pd.DataFrame(df_dict_goals.values[1:], columns=header_row_goals)
        ddict_issues = pd.DataFrame(
            df_dict_issues.values[1:],
            columns=header_row_issues,
        )

        unique_hair2 = list(ddict_hair["Hair_Type"].unique())
        unique_texture2 = list(ddict_texture["Texture"].unique())
        list(ddict_porosity["Porosity"].unique())
        unique_goals2 = list(ddict_goals["Hair_Goals"].unique())
        unique_issue2 = list(ddict_issues["Hair_Issues"].unique())

        combos = []
        _ = [
            combos.append(t)
            for t in itertools.product(
                *[unique_hair2, unique_texture2, unique_goals2, unique_issue2],
            )
        ]
        # It is to be decided if put or not the porosity variable
        combos = np.array(combos, dtype="int64")

        # Creating the Recommendation Dataset

        list(
            map(
                dict(
                    zip(
                        pd.unique(ds.Shampoo_name), list(np.arange(1, 16)), strict=False
                    )
                ).get,
                ds.Shampoo_name,
            ),
        )
        unique_hair_type_shamp = list(ds.Hair_TypeID)
        unique_goalsID_shamp = list(ds.GoalsID)
        unique_textureID_shamp = list(ds.TextureID)
        unique_issueID_shamp = list(ds.IssuesID)

        shamp_info = np.vstack(
            (
                np.array(unique_hair_type_shamp, dtype="int64"),
                np.array(unique_goalsID_shamp, dtype="int64"),
                np.array(unique_textureID_shamp, dtype="int64"),
                np.array(unique_issueID_shamp, dtype="int64"),
            ),
        ).T

        list(
            map(
                dict(
                    zip(
                        pd.unique(dc.Conditioner_name),
                        list(np.arange(1, 16)),
                        strict=False,
                    )
                ).get,
                dc.Conditioner_name,
            ),
        )
        unique_hair_type_cond = list(dc.Hair_TypeID)
        unique_goalsID_cond = list(dc.GoalsID)
        unique_textureID_cond = list(dc.TextureID)
        unique_issueID_cond = list(dc.IssuesID)

        cond_info = np.vstack(
            (
                np.array(unique_hair_type_cond, dtype="int64"),
                np.array(unique_goalsID_cond, dtype="int64"),
                np.array(unique_textureID_cond, dtype="int64"),
                np.array(unique_issueID_cond, dtype="int64"),
            ),
        ).T

        # tic = time.perf_counter()

        unique_hair_type = list(customer.Hair_TypeID)
        unique_goalsID = list(customer.GoalsID)
        unique_textureID = list(customer.TextureID)
        unique_issueID = list(customer.IssuesID)

        customer_info = np.vstack(
            (
                np.array(unique_hair_type, dtype="int64"),
                np.array(unique_textureID, dtype="int64"),
                np.array(unique_goalsID, dtype="int64"),
                np.array(unique_issueID, dtype="int64"),
            ),
        ).T

        shamps_list = shamp.to_numpy()
        shamps_list = np.array(shamps_list)
        cond_list = cond.to_numpy()
        cond_list = np.array(cond_list)

        # Now perform dictionary lookup by multiplying each customer's hair values by the values in the products dictionary
        # This multiplication compares the values of the two matrices
        dictionary_lookup_shamp = np.zeros(
            (customer_info.shape[0], shamp_info.shape[0]),
        )
        dictionary_lookup_cond = np.zeros((customer_info.shape[0], cond_info.shape[0]))

        for i in range(customer_info.shape[0]):
            dictionary_lookup_shamp[i, :] = np.matmul(shamp_info, customer_info[i, :])
            dictionary_lookup_cond[i, :] = np.matmul(cond_info, customer_info[i, :])

        # Find matching shampoo for each customer

        # The closer the matrices are, the higher the values will be.
        # n = 3
        # selelection_shamp= shamps_list[np.argsort(dictionary_lookup)]
        # selelection_cond=cond_list[np.argsort(dictionary_lookup)]

        # selected_shamp2 = np.argpartition(selelection_shamp, -4)[-4:][:,4,:]
        # selected_cond2 =selelection_cond[-n : ]

        # very big number to ensure there are at least 4 unique shampoos
        num_largest = 100
        # Find indexes of num_largest best recommendations
        selected_shamps_idx = dictionary_lookup_shamp[i, :].argpartition(
            num_largest,
            axis=None,
        )[:num_largest]
        selected_conds_idx = dictionary_lookup_cond[i, :].argpartition(
            num_largest,
            axis=None,
        )[:num_largest]
        # Find the names of the corresponding recommendations
        selected_shamps = np.array(
            [
                np.array(shamps_list[selected_shamps_idx])
                for i in range(customer_info.shape[0])
            ],
        )
        selected_conds = np.array(
            [
                np.array(cond_list[selected_conds_idx])
                for i in range(customer_info.shape[0])
            ],
        )

        # toc = time.perf_counter()
        # print(f"Code run in {toc - tic:0.4f} seconds")

        # _=[print('Customer {}: Shampoo: {}'. format(customer.Customer_ID[i], selected_shamp2[i], )) for i in range(selected_shamp2.shape[0])]
        # _=[print('Customer {}: Conditioner: {}'. format(customer.Customer_ID[i], selected_cond[i], )) for i in range(selected_cond.shape[0])]

        # Product recommendations
        # THIS COULD BE DONE AS A MATRIX MULTIPLICATION
        # selected_shamps.shape = (no_customers) x (num_largest) x 6
        #  tic = time.perf_counter()

        # All shampoos/conditioners with the same name have the same the same rating
        # Find the unique shampoo names from the recommended list
        unique_selected_shamps = np.array(
            [
                np.unique(np.array(selected_shamps[i, :, 0]))
                for i in range(selected_shamps.shape[0])
            ],
        )
        unique_selected_conds = np.array(
            [
                np.unique(np.array(selected_conds[i, :, 0]))
                for i in range(selected_conds.shape[0])
            ],
        )

        cond_ratings, shamp_ratings = [], []
        # Map the ratings labels to numbers. High - 1, Good - 2, Missing - 3
        new_cond_ratings = list(
            map(
                dict(zip(pd.unique(dc["User Rating"]), [1, 2], strict=False)).get,
                dc["User Rating"],
            ),
        )
        new_shamp_ratings = list(
            map(
                dict(zip(pd.unique(ds["User Rating"]), [1, 2], strict=False)).get,
                ds["User Rating"],
            ),
        )

        # replace missing values (None) with 3. They will be ranked lower in the list
        new_shamp_ratings = [3 if v is None else v for v in new_shamp_ratings]

        # Find a list of ratings and conditioner names from the spreadsheet
        # delete repetitions by using np.unique
        _ = [
            cond_ratings.append([new_cond_ratings[i], dc["Conditioner_name"][i]])
            for i in range(dc.shape[0])
        ]
        cond_ratings = np.unique(cond_ratings, axis=0)

        _ = [
            shamp_ratings.append([new_shamp_ratings[i], ds["Shampoo_name"][i]])
            for i in range(ds.shape[0])
        ]
        shamp_ratings = np.unique(shamp_ratings, axis=0)

        all_cond_recommendations, all_shamp_recommendations = [], []

        for i in range(unique_selected_conds.shape[0]):
            cust_conds = unique_selected_conds[i]
            cust_shamps = unique_selected_shamps[i]
            # find where the recommended product matches the rating and assign the rating
            # Change the rank of the product based on the product rating and its current position
            new_cond_positions = [
                new_cond_ratings[np.where(cust_conds[j] == cond_ratings[:, 1])[0][0]]
                for j in range(cust_conds.shape[0])
            ] * np.linspace(0, 1, len(cust_conds) + 1, endpoint=True)[1:]
            new_shamp_positions = [
                new_shamp_ratings[np.where(cust_shamps[j] == shamp_ratings[:, 1])[0][0]]
                for j in range(cust_shamps.shape[0])
            ] * np.linspace(0, 1, len(cust_shamps) + 1, endpoint=True)[1:]

            # sort the products by the new ranking
            new_cond_positions = np.vstack((new_cond_positions, cust_conds)).T
            all_cond_recommendations.append(
                new_cond_positions[np.argsort(new_cond_positions[:, 0])][:4],
            )

            new_shamp_positions = np.vstack((new_shamp_positions, cust_shamps)).T
            all_shamp_recommendations.append(
                new_shamp_positions[np.argsort(new_shamp_positions[:, 0])][:4],
            )

            # toc = time.perf_counter()
            # print(f"Code run in {toc - tic:0.4f} seconds")

            # The recommendations have the shape (no_customers) x 4 recommendations x
            # ranking of each product (they're in order)
            return [x[1] for x in all_shamp_recommendations[0]]
        return None

        # Loading the datasets MUST BE IN SPREADSHEET FORMAT. it does not understand .xsls
