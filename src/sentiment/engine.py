import matplotlib.pyplot as plt
import nltk
import pandas as pd
import seaborn as sns
from nltk.corpus import movie_reviews
from nltk.sentiment import SentimentIntensityAnalyzer
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics import (
    accuracy_score,
    confusion_matrix,
    f1_score,
    precision_score,
    recall_score,
)
from sklearn.model_selection import train_test_split
from sklearn.svm import LinearSVC

# Download the necessary NLTK data files
nltk.download("movie_reviews")
nltk.download("vader_lexicon")


class SentimentDataset:
    def __init__(self) -> None:
        self.data = None
        self.vectorizer = TfidfVectorizer(max_features=5000)

    def load_data(self) -> None:
        """Loads the movie reviews dataset from NLTK."""
        positive_reviews = [
            (movie_reviews.raw(fileid), "pos")
            for fileid in movie_reviews.fileids("pos")
        ]
        negative_reviews = [
            (movie_reviews.raw(fileid), "neg")
            for fileid in movie_reviews.fileids("neg")
        ]

        reviews = positive_reviews + negative_reviews
        self.data = pd.DataFrame(reviews, columns=["review", "sentiment"])

    def preprocess(self):
        """Preprocesses the text data by vectorizing it."""
        self.data["review"] = self.data["review"].apply(lambda x: x.lower())
        X = self.vectorizer.fit_transform(self.data["review"])
        y = self.data["sentiment"].apply(lambda x: 1 if x == "pos" else 0)

        return train_test_split(X, y, test_size=0.2, random_state=42)


class SentimentModel:
    def __init__(self) -> None:
        self.model = LinearSVC()
        self.vectorizer = TfidfVectorizer(max_features=5000)

    def train(self, X_train, y_train) -> None:
        """Trains the sentiment analysis model."""
        self.model.fit(X_train, y_train)

    def evaluate(self, X_test, y_test):
        """Evaluates the model on the test dataset."""
        y_pred = self.model.predict(X_test)
        return {
            "accuracy": accuracy_score(y_test, y_pred),
            "precision": precision_score(y_test, y_pred),
            "recall": recall_score(y_test, y_pred),
            "f1": f1_score(y_test, y_pred),
            "confusion_matrix": confusion_matrix(y_test, y_pred),
        }

    def predict(self, text):
        """Predicts sentiment for a given text input."""
        X_input = self.vectorizer.transform([text.lower()])
        return self.model.predict(X_input)


class SentimentAnalyzer:
    def __init__(self) -> None:
        self.analyzer = SentimentIntensityAnalyzer()

    def analyze(self, text):
        """Analyzes sentiment using VADER."""
        return self.analyzer.polarity_scores(text)


class SentimentVisualizer:
    @staticmethod
    def plot_confusion_matrix(cm) -> None:
        """Plots the confusion matrix."""
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt="d", cmap="Blues")
        plt.xlabel("Predicted")
        plt.ylabel("Actual")
        plt.title("Confusion Matrix")
        plt.show()

    @staticmethod
    def plot_metrics(metrics) -> None:
        """Plots the evaluation metrics."""
        plt.figure(figsize=(8, 6))
        plt.bar(
            metrics.keys(),
            metrics.values(),
            color=["skyblue", "orange", "green", "red"],
        )
        plt.xlabel("Metric")
        plt.ylabel("Score")
        plt.ylim(0, 1)
        plt.title("Model Evaluation Metrics")
        plt.show()


# Usage example
# Initialize dataset and load data
dataset = SentimentDataset()
dataset.load_data()
X_train, X_test, y_train, y_test = dataset.preprocess()

# Train the model
model = SentimentModel()
model.train(X_train, y_train)

# Evaluate the model
metrics = model.evaluate(X_test, y_test)

# Visualize results
visualizer = SentimentVisualizer()
visualizer.plot_confusion_matrix(metrics["confusion_matrix"])
visualizer.plot_metrics(
    {
        "accuracy": metrics["accuracy"],
        "precision": metrics["precision"],
        "recall": metrics["recall"],
        "f1": metrics["f1"],
    },
)

# VADER Sentiment Analysis
analyzer = SentimentAnalyzer()
sample_text = "The movie was fantastic! I really loved it."
vader_scores = analyzer.analyze(sample_text)

# Predict new text using the trained model
prediction = model.predict("This movie is terrible!")
