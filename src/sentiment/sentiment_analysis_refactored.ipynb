{"cells": [{"cell_type": "markdown", "id": "f6eeea5e", "metadata": {}, "source": ["# Hair‑Care Product Scraper + Sentiment Pipeline\n", "Refactored, modular notebook – keeps scraping & analysis logic separate, adds logging, retries and typed helper functions.\n", "\n", "**Sections**\n", "1. Configuration & Imports  \n", "2. Scrape Amazon product pages  \n", "3. NLP utilities (sentiment, keywords)  \n", "4. Feature extraction (hair type, benefits, etc.)  \n", "5. Normalisation & scoring  \n", "6. Inspect / export results"]}, {"cell_type": "code", "execution_count": null, "id": "cf7a4504", "metadata": {}, "outputs": [], "source": ["from __future__ import annotations\n", "\n", "import logging\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# Local module from previous refactor (see amazon_scraper.py)\n", "from amazon_scraper import AmazonScraper\n", "from textblob import TextBlob\n", "\n", "logging.basicConfig(level=logging.INFO, format=\"%(levelname)s ‣ %(message)s\")\n", "\n", "# ── parameters ────────────────────────────────────────────────\n", "SEARCH_URL = (\n", "    \"https://www.amazon.com/s?k=haircare+cream+shampoo+spray+products+for+women\"\n", ")\n", "MAX_WORKERS = 12  # concurrency for scraping\n", "REQUEST_DELAY = (1, 3)  # seconds – min/max between product page requests\n", "\n", "# Hair‑type & benefits keyword dictionaries (extend as needed)\n", "HAIR_TYPE_KW = {\n", "    \"Type 1\": [\"straight\", \"smooth\"],\n", "    \"Type 2\": [\"wavy\", \"beachy waves\"],\n", "    \"Type 3\": [\"curly\", \"ringlets\"],\n", "    \"Type 4\": [\"coily\", \"kinky\", \"afro\"],\n", "}\n", "\n", "BENEFIT_KW = {\n", "    \"moisturizing\": [\"moisturiz\", \"hydrat\"],\n", "    \"smoothing\": [\"smooth\", \"silky\"],\n", "    \"repairing\": [\"repair\", \"restore\"],\n", "    \"strengthening\": [\"strength\", \"fortif\"],\n", "    \"volumizing\": [\"volume\", \"thicken\"],\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "1cc1621b", "metadata": {}, "outputs": [], "source": ["# ── scrape ────────────────────────────────────────────────────\n", "scraper = AmazonScraper(\n", "    SEARCH_URL,\n", "    delay=REQUEST_DELAY,\n", "    max_workers=MAX_WORKERS,\n", "    loglevel=logging.INFO,\n", ")\n", "scraper.scrape()\n", "amazon_df = scraper.to_dataframe()\n", "logging.info(f\"Scraped {len(amazon_df)} products\")\n", "amazon_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "603eb179", "metadata": {}, "outputs": [], "source": ["# ── nlp helpers ───────────────────────────────────────────────\n", "def polarity(text: str | None) -> float:\n", "    \"\"\"Return TextBlob polarity in [-1,1]; empty text → 0.\"\"\"\n", "    if not text:\n", "        return 0.0\n", "    return TextBlob(text).sentiment.polarity\n", "\n", "\n", "def keyword_hits(text: str, keyword_dict: dict[str, list[str]]) -> dict[str, int]:\n", "    \"\"\"Count occurrences of keyword stems per key in *keyword_dict*.\"\"\"\n", "    counts = {k: 0 for k in keyword_dict}\n", "    if not text:\n", "        return counts\n", "    lower = text.lower()\n", "    for key, stems in keyword_dict.items():\n", "        counts[key] = sum(lower.count(stem) for stem in stems)\n", "    return counts"]}, {"cell_type": "code", "execution_count": null, "id": "eedb54ab", "metadata": {}, "outputs": [], "source": ["# ── feature extraction ────────────────────────────────────────\n", "# sentiment per product (use positive review if present, else fallback)\n", "amazon_df[\"sentiment\"] = amazon_df[\"positive_feedback\"].apply(polarity)\n", "\n", "# keyword‑based features\n", "amazon_df[\"hair_types\"] = amazon_df[\"positive_feedback\"].apply(\n", "    lambda txt: keyword_hits(txt, HAIR_TYPE_KW)\n", ")\n", "\n", "amazon_df[\"benefits\"] = amazon_df[\"positive_feedback\"].apply(\n", "    lambda txt: keyword_hits(txt, BENEFIT_KW)\n", ")\n", "\n", "# explode dict columns into individual numeric columns\n", "hair_df = pd.json_normalize(amazon_df[\"hair_types\"])\n", "hair_df.columns = [f\"hair_{c}\" for c in hair_df.columns]\n", "benefit_df = pd.json_normalize(amazon_df[\"benefits\"])\n", "benefit_df.columns = [f\"benefit_{c}\" for c in benefit_df.columns]\n", "\n", "amazon_df = pd.concat([amazon_df, hair_df, benefit_df], axis=1)\n", "amazon_df.drop(columns=[\"hair_types\", \"benefits\"], inplace=True)\n", "\n", "logging.info(\"Feature columns added.\")"]}, {"cell_type": "code", "execution_count": null, "id": "2c045512", "metadata": {}, "outputs": [], "source": ["# ── normalise features to 1‑10 scale ──────────────────────────\n", "def scale_1_to_10(series: pd.Series) -> pd.Series:\n", "    if series.max() == series.min():\n", "        return pd.Series([1] * len(series))\n", "    return ((series - series.min()) / (series.max() - series.min()) * 9 + 1).round(2)\n", "\n", "\n", "for col in hair_df.columns + benefit_df.columns:\n", "    amazon_df[col] = scale_1_to_10(amazon_df[col])\n", "\n", "# simple overall score (sentiment + mean of hair/benefit scores)\n", "score_cols = [\"sentiment\"] + list(hair_df.columns) + list(benefit_df.columns)\n", "amazon_df[\"overall_score\"] = amazon_df[score_cols].mean(axis=1).round(2)\n", "\n", "amazon_df.sort_values(\"overall_score\", ascending=False).head()"]}, {"cell_type": "code", "execution_count": null, "id": "58f8093e", "metadata": {}, "outputs": [], "source": ["# ── save ───────────────────────────────────────────────────────\n", "out_path = Path(\"hair_products_sentiment.npy\")\n", "np.save(out_path, amazon_df.to_numpy())\n", "print(f\"Saved NumPy array to {out_path.resolve()}\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}