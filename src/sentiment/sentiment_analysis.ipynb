{"cells": [{"cell_type": "code", "execution_count": null, "id": "00602119-3d44-4f49-b935-4c8c87f4846e", "metadata": {}, "outputs": [], "source": ["import random\n", "import re\n", "from datetime import datetime as dt\n", "from datetime import timedelta as td\n", "from datetime import timezone as tz\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import requests\n", "from bs4 import BeautifulSoup"]}, {"cell_type": "code", "execution_count": null, "id": "48f4d6bc-688f-4c7e-8602-29c1fcca2f4e", "metadata": {}, "outputs": [], "source": ["# ── amazon_scraper.py ──────────────────────────────────────────────\n", "# from __future__ import annotations\n", "# import random\n", "import concurrent.futures as fut\n", "import logging\n", "import time\n", "from dataclasses import asdict, dataclass\n", "from os import PathLike\n", "from typing import List, Optional, Sequence\n", "\n", "# import pandas as pd\n", "# import numpy as np\n", "# import requests\n", "# from bs4 import BeautifulSoup\n", "from requests.adapters import HTTPAdapter, Retry\n", "\n", "\n", "@dataclass\n", "class Product:  # ------------- data schema\n", "    title: str = \"\"\n", "    price: str = \"\"\n", "    rating: str = \"\"\n", "    review_count: str = \"\"\n", "    availability: str = \"\"\n", "    positive_feedback: str = \"\"\n", "    negative_feedback: str = \"\"\n", "\n", "\n", "class AmazonScraper:  # ------------- main façade\n", "    LIST_ITEM_SEL = (\"a\", \"a-link-normal s-no-outline\")\n", "    UA_POOL: Sequence[str] = (\n", "        \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) \"\n", "        \"AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0 Safari/537.36\",\n", "        \"Mozilla/5.0 (Macintosh; Intel Mac OS X 14_4) \"\n", "        \"AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17 Safari/605.1.15\",\n", "        \"Mozilla/5.0 (X11; Linux x86_64; rv:126.0) Gecko/20100101 Firefox/126.0\",\n", "    )\n", "\n", "    def __init__(\n", "        self,\n", "        search_url: str,\n", "        *,\n", "        proxies: Optional[Sequence[str]] = None,\n", "        delay: tuple[float, float] = (1, 3),\n", "        max_workers: int = 8,\n", "        loglevel: int = logging.WARNING,\n", "    ) -> None:\n", "        self.url, self.delay = search_url, delay\n", "        self.proxies, self.max_workers = proxies or (), max_workers\n", "        logging.basicConfig(level=loglevel, format=\"%(levelname)s ✦ %(message)s\")\n", "        self.log = logging.getLogger(\"AmazonScraper\")\n", "        self.session = self._make_session()\n", "        self._products: list[Product] = []\n", "\n", "    # ───────────────────────────────────────────────────────────────\n", "    # public helpers\n", "    def scrape(self) -> None:\n", "        self.log.info(\"Fetching search page …\")\n", "        links = self._extract_links(self._fetch(self.url))\n", "        if not links:\n", "            self.log.warning(\"No product links found.\")\n", "            return\n", "\n", "        self.log.info(f\"Found {len(links)} product pages.\")\n", "        with fut.ThreadPoolExecutor(max_workers=self.max_workers) as pool:\n", "            for prod in pool.map(self._parse_product, links):\n", "                if prod:\n", "                    self._products.append(prod)\n", "\n", "    def to_dataframe(self) -> pd.DataFrame:\n", "        return pd.DataFrame(asdict(p) for p in self._products)\n", "\n", "    def save_numpy(self, path: str | bytes | PathLike[str]) -> None:\n", "        np.save(path, self.to_dataframe().to_numpy())\n", "\n", "    # ───────────────────────────────────────────────────────────────\n", "    # internal — networking\n", "    def _make_session(self) -> requests.Session:\n", "        s = requests.Session()\n", "        retries = Retry(\n", "            total=3, backoff_factor=1.2, status_forcelist=[429, 500, 502, 503]\n", "        )\n", "        s.mount(\"https://\", HTTPAdapter(max_retries=retries))\n", "        return s\n", "\n", "    def _fetch(self, url: str) -> BeautifulSoup | None:\n", "        hdrs = {\n", "            \"User-Agent\": random.choice(self.UA_POOL),\n", "            \"Accept-Language\": \"en-US,en;q=0.9\",\n", "            \"Accept-Encoding\": \"gzip, deflate, br\",\n", "            \"Connection\": \"keep-alive\",\n", "        }\n", "        proxy = {\"http\": random.choice(self.proxies)} if self.proxies else None\n", "        try:\n", "            res = self.session.get(url, headers=hdrs, proxies=proxy, timeout=10)\n", "            res.raise_for_status()\n", "            return BeautifulSoup(res.content, \"html.parser\")\n", "        except requests.exceptions.RequestException as e:\n", "            self.log.debug(f\"Request failed: {e}\")\n", "            return None\n", "\n", "    # ───────────────────────────────────────────────────────────────\n", "    # internal — search result page\n", "    def _extract_links(self, soup: BeautifulSoup | None) -> List[str]:\n", "        if soup is None:\n", "            return []\n", "        tag, css = self.LIST_ITEM_SEL\n", "        return [\n", "            \"https://www.amazon.com\" + a[\"href\"] for a in soup.find_all(tag, class_=css)\n", "        ]\n", "\n", "    # ───────────────────────────────────────────────────────────────\n", "    # internal — product page\n", "    def _parse_product(self, url: str) -> Optional[Product]:\n", "        time.sleep(random.uniform(*self.delay))\n", "        sp = self._fetch(url)\n", "        if sp is None:\n", "            return None\n", "\n", "        def txt(sel: str) -> str:\n", "            \"\"\"Return stripped text of first match or ''.\"\"\"\n", "            n = sp.select_one(sel)\n", "            return n.text.strip() if n else \"\"\n", "\n", "        reviews = self._collect_reviews(sp)\n", "        positive, negative = self._best_and_worst(reviews)\n", "\n", "        return Product(\n", "            title=txt(\"#productTitle\"),\n", "            price=txt(\"span.a-offscreen\"),\n", "            rating=txt(\"span.a-icon-alt\"),\n", "            review_count=txt(\"#acrCustomerReviewText\"),\n", "            availability=txt(\"#availability span\"),\n", "            positive_feedback=positive,\n", "            negative_feedback=negative,\n", "        )\n", "\n", "    # reviews helpers\n", "    @staticmethod\n", "    def _collect_reviews(soup: BeautifulSoup) -> list[tuple[str, int]]:\n", "        reviews = []\n", "        for block in soup.select('div[data-hook=\"review\"]'):\n", "            text = block.select_one('span[data-hook=\"review-body\"]')\n", "            votes = block.select_one('span[data-hook=\"helpful-vote-statement\"]')\n", "            count = int(votes.text.split()[0].replace(\",\", \"\")) if votes else 0\n", "            reviews.append((text.text.strip() if text else \"\", count))\n", "        return reviews\n", "\n", "    @staticmethod\n", "    def _best_and_worst(revs: list[tuple[str, int]]) -> tuple[str, str]:\n", "        if not revs:\n", "            return \"\", \"\"\n", "        positive = max(revs, key=lambda t: t[1])[0]\n", "        negative = min(revs, key=lambda t: t[1])[0]\n", "        return positive, negative"]}, {"cell_type": "code", "execution_count": null, "id": "acd9c0fe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8ef7dd73", "metadata": {}, "outputs": [], "source": ["from amazon_scraper import AmazonScraper\n", "\n", "SEARCH = \"https://www.amazon.com/s?k=haircare+cream+shampoo+spray+products+for+women&ref=nb_sb_noss\"\n", "scraper = AmazonScraper(SEARCH, max_workers=12, loglevel=logging.INFO)\n", "scraper.scrape()\n", "\n", "df = scraper.to_dataframe()\n", "display(df.head())  # <PERSON><PERSON><PERSON> helper ‑‑ shows first rows\n", "scraper.save_numpy(\"hair_products.npy\")"]}, {"cell_type": "code", "execution_count": null, "id": "a35fcb78-a59d-4832-aa55-e39ae3b310e3", "metadata": {}, "outputs": [], "source": ["def get_title(soup):\n", "    try:\n", "        title = soup.find(\"span\", attrs={\"id\": \"productTitle\"}).get_text().strip()\n", "    except AttributeError:\n", "        title = \"\"\n", "    return title\n", "\n", "\n", "def get_price(soup):\n", "    try:\n", "        price = soup.find(\"span\", attrs={\"class\": \"a-offscreen\"}).get_text().strip()\n", "    except AttributeError:\n", "        price = \"\"\n", "    return price\n", "\n", "\n", "def get_rating(soup):\n", "    try:\n", "        rating = soup.find(\"span\", attrs={\"class\": \"a-icon-alt\"}).get_text().strip()\n", "    except AttributeError:\n", "        rating = \"\"\n", "    return rating\n", "\n", "\n", "def get_review_count(soup):\n", "    try:\n", "        review_count = (\n", "            soup.find(\"span\", attrs={\"id\": \"acrCustomerReviewText\"}).get_text().strip()\n", "        )\n", "    except AttributeError:\n", "        review_count = \"\"\n", "    return review_count\n", "\n", "\n", "def get_availability(soup):\n", "    try:\n", "        available = (\n", "            soup.find(\"div\", attrs={\"id\": \"availability\"})\n", "            .find(\"span\")\n", "            .get_text()\n", "            .strip()\n", "        )\n", "    except AttributeError:\n", "        available = \"Not Available\"\n", "    return available\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    # user agent\n", "    HEADERS = {\n", "        \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\n", "        \"Accept-Language\": \"en-US, en;q=0.5\",\n", "    }\n", "\n", "    # The webpage URL\n", "    URL = \"https://www.amazon.com/s?k=WOMEN+HAIR+SHAMPOO+CREAM+SPRAY&crid=1NO17BWUOR73Y&sprefix=women+hair+shampoo+cream+spray%2Caps%2C376&ref=nb_sb_noss\"\n", "\n", "    # HTTP Request\n", "    webpage = requests.get(URL, headers=HEADERS)\n", "\n", "    # Soup Object containing all data\n", "    soup = BeautifulSoup(webpage.content, \"html.parser\")\n", "\n", "    # Fetch links as List of Tag Objects\n", "    links = soup.find_all(\"a\", attrs={\"class\": \"a-link-normal s-no-outline\"})\n", "\n", "    # Store the links\n", "    links_list = []\n", "\n", "    # Loop for extracting links from Tag Objects\n", "    for link in links:\n", "        links_list.append(link.get(\"href\"))\n", "\n", "    d = {\"title\": [], \"price\": [], \"rating\": [], \"reviews\": [], \"availability\": []}\n", "\n", "    # Loop for extracting product details from each link\n", "    for link in links_list:\n", "        new_webpage = requests.get(\"https://www.amazon.com/\" + link, headers=HEADERS)\n", "        new_soup = BeautifulSoup(new_webpage.content, \"html.parser\")\n", "\n", "        # Function calls to display all necessary product information\n", "        d[\"title\"].append(get_title(new_soup))\n", "        d[\"price\"].append(get_price(new_soup))\n", "        d[\"rating\"].append(get_rating(new_soup))\n", "        d[\"reviews\"].append(get_review_count(new_soup))\n", "        d[\"availability\"].append(get_availability(new_soup))\n", "\n", "    amazon_df = pd.DataFrame.from_dict(d)\n", "    amazon_df[\"title\"] = amazon_df[\"title\"].replace(\"\", np.nan)\n", "    amazon_df = amazon_df.dropna(subset=[\"title\"])\n", "    amazon_df.to_csv(\"amazon_data.csv\", header=True, index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6fc590ac-9239-4d00-9e46-4d4978273528", "metadata": {}, "outputs": [], "source": ["amazon_df.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "8335af3f-3887-4630-a9a4-18380a5fb134", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import requests\n", "from bs4 import BeautifulSoup\n", "\n", "\n", "def get_title(soup):\n", "    try:\n", "        title = soup.find(\"span\", attrs={\"id\": \"productTitle\"}).get_text().strip()\n", "    except AttributeError:\n", "        title = \"\"\n", "    return title\n", "\n", "\n", "def get_price(soup):\n", "    try:\n", "        price = soup.find(\"span\", attrs={\"class\": \"a-offscreen\"}).get_text().strip()\n", "    except AttributeError:\n", "        price = \"\"\n", "    return price\n", "\n", "\n", "def get_rating(soup):\n", "    try:\n", "        rating = soup.find(\"span\", attrs={\"class\": \"a-icon-alt\"}).get_text().strip()\n", "    except AttributeError:\n", "        rating = \"\"\n", "    return rating\n", "\n", "\n", "def get_review_count(soup):\n", "    try:\n", "        review_count = (\n", "            soup.find(\"span\", attrs={\"id\": \"acrCustomerReviewText\"}).get_text().strip()\n", "        )\n", "    except AttributeError:\n", "        review_count = \"\"\n", "    return review_count\n", "\n", "\n", "def get_availability(soup):\n", "    try:\n", "        available = (\n", "            soup.find(\"div\", attrs={\"id\": \"availability\"})\n", "            .find(\"span\")\n", "            .get_text()\n", "            .strip()\n", "        )\n", "    except AttributeError:\n", "        available = \"Not Available\"\n", "    return available\n", "\n", "\n", "def get_reviews(soup):\n", "    reviews = []\n", "    review_blocks = soup.find_all(\n", "        \"div\",\n", "        attrs={\"class\": \"a-section review aok-relative\"},\n", "    )\n", "    for review in review_blocks:\n", "        try:\n", "            review_text = (\n", "                review.find(\n", "                    \"span\",\n", "                    attrs={\"class\": \"a-size-base review-text review-text-content\"},\n", "                )\n", "                .get_text()\n", "                .strip()\n", "            )\n", "            helpfulness = (\n", "                review.find(\"span\", attrs={\"class\": \"cr-vote\"}).get_text().strip()\n", "            )\n", "            reviews.append((review_text, helpfulness))\n", "        except AttributeError:\n", "            continue\n", "    return reviews\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    # user agent\n", "    HEADERS = {\n", "        \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\n", "        \"Accept-Language\": \"en-US, en;q=0.5\",\n", "    }\n", "\n", "    # The webpage URL\n", "    URL = \"https://www.amazon.com/s?k=haircare+cream+shampoo+spray+products+for+women&crid=2NQ2TTPBLNJ92&sprefix=haircare+cream+shampoo+sprayproducts+for+women%2Caps%2C518&ref=nb_sb_noss\"\n", "\n", "    # HTTP Request\n", "    webpage = requests.get(URL, headers=HEADERS)\n", "\n", "    # Soup Object containing all data\n", "    soup = BeautifulSoup(webpage.content, \"html.parser\")\n", "\n", "    # Fetch links as List of Tag Objects\n", "    links = soup.find_all(\"a\", attrs={\"class\": \"a-link-normal s-no-outline\"})\n", "\n", "    # Store the links\n", "    links_list = []\n", "\n", "    # Loop for extracting links from Tag Objects\n", "    for link in links:\n", "        links_list.append(link.get(\"href\"))\n", "\n", "    d = {\n", "        \"title\": [],\n", "        \"price\": [],\n", "        \"rating\": [],\n", "        \"reviews\": [],\n", "        \"availability\": [],\n", "        \"positive_feedback\": [],\n", "        \"negative_feedback\": [],\n", "    }\n", "\n", "    # Loop for extracting product details from each link\n", "    for link in links_list:\n", "        new_webpage = requests.get(\"https://www.amazon.com/\" + link, headers=HEADERS)\n", "        new_soup = BeautifulSoup(new_webpage.content, \"html.parser\")\n", "\n", "        # Function calls to display all necessary product information\n", "        d[\"title\"].append(get_title(new_soup))\n", "        d[\"price\"].append(get_price(new_soup))\n", "        d[\"rating\"].append(get_rating(new_soup))\n", "        d[\"reviews\"].append(get_review_count(new_soup))\n", "        d[\"availability\"].append(get_availability(new_soup))\n", "\n", "        reviews = get_reviews(new_soup)\n", "        if reviews:\n", "            positive_feedback = max(\n", "                reviews,\n", "                key=lambda x: int(x[1].split()[0]) if x[1].split()[0].isdigit() else 0,\n", "            )[0]\n", "            negative_feedback = min(\n", "                reviews,\n", "                key=lambda x: int(x[1].split()[0])\n", "                if x[1].split()[0].isdigit()\n", "                else float(\"inf\"),\n", "            )[0]\n", "            d[\"positive_feedback\"].append(positive_feedback)\n", "            d[\"negative_feedback\"].append(negative_feedback)\n", "        else:\n", "            d[\"positive_feedback\"].append(\"\")\n", "            d[\"negative_feedback\"].append(\"\")\n", "\n", "    amazon_df = pd.DataFrame.from_dict(d)\n", "    amazon_df[\"title\"] = amazon_df[\"title\"].replace(\"\", np.nan)\n", "    amazon_df = amazon_df.dropna(subset=[\"title\"])\n", "    amazon_df.to_csv(\"amazon_data.csv\", header=True, index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c50e6f4c-51d2-41c0-bf89-0e4aa2d64e53", "metadata": {}, "outputs": [], "source": ["amazon_df.head(4)"]}, {"cell_type": "code", "execution_count": null, "id": "c4228ef1-6f50-4924-abeb-564d917088ce", "metadata": {}, "outputs": [], "source": ["# def get_title(soup):\n", "#     try:\n", "#         title = soup.find(\"span\", attrs={\"id\": \"productTitle\"}).get_text().strip()\n", "#     except AttributeError:\n", "#         title = \"\"\n", "#     return title\n", "\n", "\n", "def get_price(soup):\n", "    try:\n", "        price = soup.find(\"span\", attrs={\"class\": \"a-offscreen\"}).get_text().strip()\n", "    except AttributeError:\n", "        price = \"\"\n", "    return price\n", "\n", "\n", "def get_rating(soup):\n", "    try:\n", "        rating = soup.find(\"span\", attrs={\"class\": \"a-icon-alt\"}).get_text().strip()\n", "    except AttributeError:\n", "        rating = \"\"\n", "    return rating\n", "\n", "\n", "def get_review_count(soup):\n", "    try:\n", "        review_count = (\n", "            soup.find(\"span\", attrs={\"id\": \"acrCustomerReviewText\"}).get_text().strip()\n", "        )\n", "    except AttributeError:\n", "        review_count = \"\"\n", "    return review_count\n", "\n", "\n", "def get_availability(soup):\n", "    try:\n", "        available = (\n", "            soup.find(\"div\", attrs={\"id\": \"availability\"})\n", "            .find(\"span\")\n", "            .get_text()\n", "            .strip()\n", "        )\n", "    except AttributeError:\n", "        available = \"Not Available\"\n", "    return available\n", "\n", "\n", "def get_reviews(soup):\n", "    reviews = []\n", "    review_blocks = soup.find_all(\n", "        \"div\",\n", "        attrs={\"class\": \"a-section review aok-relative\"},\n", "    )\n", "    for review in review_blocks:\n", "        try:\n", "            review_text = (\n", "                review.find(\n", "                    \"span\",\n", "                    attrs={\"class\": \"a-size-base review-text review-text-content\"},\n", "                )\n", "                .get_text()\n", "                .strip()\n", "            )\n", "            helpfulness = (\n", "                review.find(\"span\", attrs={\"class\": \"cr-vote-text\"}).get_text().strip()\n", "            )\n", "            reviews.append((review_text, helpfulness))\n", "        except AttributeError:\n", "            continue\n", "    return reviews\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    # user agent\n", "    HEADERS = {\n", "        \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\n", "        \"Accept-Language\": \"en-US, en;q=0.5\",\n", "    }\n", "\n", "    # The webpage URL for a specific product\n", "    URL = \"https://www.amazon.com/Cake-Beauty-Defining-Shampoo-Conditioner/dp/B09GW5JWCH/ref=sr_1_5?crid=2NQ2TTPBLNJ92&dib=eyJ2IjoiMSJ9.AF4mlhDSbrO0g_hHe59xKYL8ilI3jVjR2us4rCTpdiiUxTYHBA_EUZr9R_lJCSWD-hYpWk7mH8EGu9eiZCHOqVfgstcStc7DhwWAlMHK7o12O5lBZel-SKejuHVhut8-r_YAQR68NcLCrnLwZzHrYI7fP8-vZD-KtzNqmQSjMpFC4q87X70Lxis8p7DssNdcdvzDnxxluWzYy4HIaKpBegp-8UlKVgSOx86XBh3sptBKx6z1W10kcQ3nLDQ4OkZkO4h5GuYtlZGssO3HPA9k_kz_SATRwEr8EQLBl3YRtN4.QJepEJm-uX02hH9UUl8qIhtu8nBl24dw9YjLWh4-5C8&dib_tag=se&keywords=haircare%2Bcream%2Bshampoo%2Bspray%2Bproducts%2Bfor%2Bwomen&qid=1720278585&sprefix=haircare%2Bcream%2Bshampoo%2Bsprayproducts%2Bfor%2Bwomen%2Caps%2C518&sr=8-5&th=1\"\n", "\n", "    # HTTP Request\n", "    webpage = requests.get(URL, headers=HEADERS)\n", "\n", "    # Soup Object containing all data\n", "    soup = BeautifulSoup(webpage.content, \"html.parser\")\n", "\n", "    d = {\n", "        \"title\": [],\n", "        \"price\": [],\n", "        \"rating\": [],\n", "        \"reviews\": [],\n", "        \"availability\": [],\n", "        \"positive_feedback\": [],\n", "        \"negative_feedback\": [],\n", "    }\n", "\n", "    # Function calls to display all necessary product information\n", "    d[\"title\"].append(get_title(soup))\n", "    d[\"price\"].append(get_price(soup))\n", "    d[\"rating\"].append(get_rating(soup))\n", "    d[\"reviews\"].append(get_review_count(soup))\n", "    d[\"availability\"].append(get_availability(soup))\n", "\n", "    reviews = get_reviews(soup)\n", "    if reviews:\n", "        positive_feedback = max(\n", "            reviews,\n", "            key=lambda x: int(x[1].split()[0]) if x[1].split()[0].isdigit() else 0,\n", "        )[0]\n", "        negative_feedback = min(\n", "            reviews,\n", "            key=lambda x: int(x[1].split()[0])\n", "            if x[1].split()[0].isdigit()\n", "            else float(\"inf\"),\n", "        )[0]\n", "        d[\"positive_feedback\"].append(positive_feedback)\n", "        d[\"negative_feedback\"].append(negative_feedback)\n", "    else:\n", "        d[\"positive_feedback\"].append(\"\")\n", "        d[\"negative_feedback\"].append(\"\")\n", "\n", "    amazon_df = pd.DataFrame.from_dict(d)\n", "    amazon_df[\"title\"] = amazon_df[\"title\"].replace(\"\", np.nan)\n", "    amazon_df = amazon_df.dropna(subset=[\"title\"])\n", "    amazon_df.to_csv(\"amazon_data.csv\", header=True, index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "911aabf7-0cf1-46cf-87d9-9ed6f3d41414", "metadata": {}, "outputs": [], "source": ["amazon_df.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "5c613e4d-160e-4381-a271-472ebb66ac1e", "metadata": {}, "outputs": [], "source": ["# def get_title(soup):\n", "#     try:\n", "#         title = soup.find(\"span\", attrs={\"id\": \"productTitle\"}).get_text().strip()\n", "#     except AttributeError:\n", "#         title = \"\"\n", "#     return title\n", "\n", "\n", "def get_price(soup):\n", "    try:\n", "        price = soup.find(\"span\", attrs={\"class\": \"a-offscreen\"}).get_text().strip()\n", "    except AttributeError:\n", "        price = \"\"\n", "    return price\n", "\n", "\n", "def get_rating(soup):\n", "    try:\n", "        rating = soup.find(\"span\", attrs={\"class\": \"a-icon-alt\"}).get_text().strip()\n", "    except AttributeError:\n", "        rating = \"\"\n", "    return rating\n", "\n", "\n", "def get_review_count(soup):\n", "    try:\n", "        review_count = (\n", "            soup.find(\"span\", attrs={\"id\": \"acrCustomerReviewText\"}).get_text().strip()\n", "        )\n", "    except AttributeError:\n", "        review_count = \"\"\n", "    return review_count\n", "\n", "\n", "def get_availability(soup):\n", "    try:\n", "        available = (\n", "            soup.find(\"div\", attrs={\"id\": \"availability\"})\n", "            .find(\"span\")\n", "            .get_text()\n", "            .strip()\n", "        )\n", "    except AttributeError:\n", "        available = \"Not Available\"\n", "    return available\n", "\n", "\n", "def get_reviews(soup):\n", "    reviews = []\n", "    review_blocks = soup.find_all(\"div\", attrs={\"data-hook\": \"review\"})\n", "    for review in review_blocks:\n", "        try:\n", "            review_text = (\n", "                review.find(\"span\", attrs={\"data-hook\": \"review-body\"})\n", "                .get_text()\n", "                .strip()\n", "            )\n", "            helpfulness = review.find(\n", "                \"span\",\n", "                attrs={\"data-hook\": \"helpful-vote-statement\"},\n", "            )\n", "            helpfulness_text = (\n", "                helpfulness.get_text().strip()\n", "                if helpfulness\n", "                else \"0 found this helpful\"\n", "            )\n", "            reviews.append((review_text, helpfulness_text))\n", "        except AttributeError:\n", "            continue\n", "    return reviews\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    # user agent\n", "    HEADERS = {\n", "        \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\n", "        \"Accept-Language\": \"en-US, en;q=0.5\",\n", "    }\n", "\n", "    # The webpage URL for a specific product\n", "    URL = \"https://www.amazon.com/Cake-Beauty-Defining-Shampoo-Conditioner/dp/B09GW5JWCH/ref=sr_1_5?crid=2NQ2TTPBLNJ92&dib=eyJ2IjoiMSJ9.AF4mlhDSbrO0g_hHe59xKYL8ilI3jVjR2us4rCTpdiiUxTYHBA_EUZr9R_lJCSWD-hYpWk7mH8EGu9eiZCHOqVfgstcStc7DhwWAlMHK7o12O5lBZel-SKejuHVhut8-r_YAQR68NcLCrnLwZzHrYI7fP8-vZD-KtzNqmQSjMpFC4q87X70Lxis8p7DssNdcdvzDnxxluWzYy4HIaKpBegp-8UlKVgSOx86XBh3sptBKx6z1W10kcQ3nLDQ4OkZkO4h5GuYtlZGssO3HPA9k_kz_SATRwEr8EQLBl3YRtN4.QJepEJm-uX02hH9UUl8qIhtu8nBl24dw9YjLWh4-5C8&dib_tag=se&keywords=haircare%2Bcream%2Bshampoo%2Bspray%2Bproducts%2Bfor%2Bwomen&qid=1720278585&sprefix=haircare%2Bcream%2Bshampoo%2Bsprayproducts%2Bfor%2Bwomen%2Caps%2C518&sr=8-5&th=1\"\n", "\n", "    # HTTP Request\n", "    webpage = requests.get(URL, headers=HEADERS)\n", "\n", "    # Soup Object containing all data\n", "    soup = BeautifulSoup(webpage.content, \"html.parser\")\n", "\n", "    d = {\n", "        \"title\": [],\n", "        \"price\": [],\n", "        \"rating\": [],\n", "        \"reviews\": [],\n", "        \"availability\": [],\n", "        \"positive_feedback\": [],\n", "        \"negative_feedback\": [],\n", "    }\n", "\n", "    # Function calls to display all necessary product information\n", "    d[\"title\"].append(get_title(soup))\n", "    d[\"price\"].append(get_price(soup))\n", "    d[\"rating\"].append(get_rating(soup))\n", "    d[\"reviews\"].append(get_review_count(soup))\n", "    d[\"availability\"].append(get_availability(soup))\n", "\n", "    reviews = get_reviews(soup)\n", "    if reviews:\n", "        positive_feedback = max(\n", "            reviews,\n", "            key=lambda x: int(x[1].split()[0]) if x[1].split()[0].isdigit() else 0,\n", "        )[0]\n", "        negative_feedback = min(\n", "            reviews,\n", "            key=lambda x: int(x[1].split()[0])\n", "            if x[1].split()[0].isdigit()\n", "            else float(\"inf\"),\n", "        )[0]\n", "        d[\"positive_feedback\"].append(positive_feedback)\n", "        d[\"negative_feedback\"].append(negative_feedback)\n", "    else:\n", "        d[\"positive_feedback\"].append(\"\")\n", "        d[\"negative_feedback\"].append(\"\")\n", "\n", "    amazon_df = pd.DataFrame.from_dict(d)\n", "    amazon_df[\"title\"] = amazon_df[\"title\"].replace(\"\", np.nan)\n", "    amazon_df = amazon_df.dropna(subset=[\"title\"])\n", "    amazon_df.to_csv(\"amazon_data.csv\", header=True, index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "0064b80f-6579-4776-a1c1-f7e82d96c018", "metadata": {}, "outputs": [], "source": ["amazon_df.head(4)"]}, {"cell_type": "code", "execution_count": null, "id": "6e3b2b5b-4d4a-4381-b922-dd2d5453625a", "metadata": {}, "outputs": [], "source": ["# def get_title(soup):\n", "#     try:\n", "#         title = soup.find(\"span\", attrs={\"id\": \"productTitle\"}).get_text().strip()\n", "#     except AttributeError:\n", "#         title = \"\"\n", "#     return title\n", "\n", "\n", "def get_price(soup):\n", "    try:\n", "        price = soup.find(\"span\", attrs={\"class\": \"a-offscreen\"}).get_text().strip()\n", "    except AttributeError:\n", "        price = \"\"\n", "    return price\n", "\n", "\n", "def get_rating(soup):\n", "    try:\n", "        rating = soup.find(\"span\", attrs={\"class\": \"a-icon-alt\"}).get_text().strip()\n", "    except AttributeError:\n", "        rating = \"\"\n", "    return rating\n", "\n", "\n", "def get_review_count(soup):\n", "    try:\n", "        review_count = (\n", "            soup.find(\"span\", attrs={\"id\": \"acrCustomerReviewText\"}).get_text().strip()\n", "        )\n", "    except AttributeError:\n", "        review_count = \"\"\n", "    return review_count\n", "\n", "\n", "def get_availability(soup):\n", "    try:\n", "        available = (\n", "            soup.find(\"div\", attrs={\"id\": \"availability\"})\n", "            .find(\"span\")\n", "            .get_text()\n", "            .strip()\n", "        )\n", "    except AttributeError:\n", "        available = \"Not Available\"\n", "    return available\n", "\n", "\n", "def get_reviews(soup):\n", "    reviews = []\n", "    review_blocks = soup.find_all(\"div\", attrs={\"data-hook\": \"review\"})\n", "    for review in review_blocks:\n", "        try:\n", "            review_text = (\n", "                review.find(\"span\", attrs={\"data-hook\": \"review-body\"})\n", "                .get_text()\n", "                .strip()\n", "            )\n", "            helpfulness = review.find(\n", "                \"span\",\n", "                attrs={\"data-hook\": \"helpful-vote-statement\"},\n", "            )\n", "            helpfulness_text = (\n", "                helpfulness.get_text().strip()\n", "                if helpfulness\n", "                else \"0 found this helpful\"\n", "            )\n", "            reviews.append((review_text, helpfulness_text))\n", "        except AttributeError:\n", "            continue\n", "    return reviews\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    HEADERS = {\n", "        \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\n", "        \"Accept-Language\": \"en-US, en;q=0.5\",\n", "    }\n", "\n", "    # URL of the main search results page\n", "    search_url = \"https://www.amazon.com/s?k=haircare+cream+shampoo+spray+products+for+women&crid=2NQ2TTPBLNJ92&sprefix=haircare+cream+shampoo+sprayproducts+for+women%2Caps%2C518&ref=nb_sb_noss\"\n", "\n", "    # HTTP Request\n", "    search_page = requests.get(search_url, headers=HEADERS)\n", "\n", "    # Soup Object containing all data\n", "    search_soup = BeautifulSoup(search_page.content, \"html.parser\")\n", "\n", "    # Fetch product links from the search results page\n", "    product_links = []\n", "    links = search_soup.find_all(\"a\", attrs={\"class\": \"a-link-normal s-no-outline\"})\n", "\n", "    for link in links:\n", "        product_links.append(\"https://www.amazon.com\" + link.get(\"href\"))\n", "\n", "    d = {\n", "        \"title\": [],\n", "        \"price\": [],\n", "        \"rating\": [],\n", "        \"reviews\": [],\n", "        \"availability\": [],\n", "        \"positive_feedback\": [],\n", "        \"negative_feedback\": [],\n", "    }\n", "\n", "    for product_url in product_links:\n", "        # HTTP Request\n", "        product_page = requests.get(product_url, headers=HEADERS)\n", "\n", "        # Soup Object containing all data\n", "        product_soup = BeautifulSoup(product_page.content, \"html.parser\")\n", "\n", "        # Function calls to display all necessary product information\n", "        d[\"title\"].append(get_title(product_soup))\n", "        d[\"price\"].append(get_price(product_soup))\n", "        d[\"rating\"].append(get_rating(product_soup))\n", "        d[\"reviews\"].append(get_review_count(product_soup))\n", "        d[\"availability\"].append(get_availability(product_soup))\n", "\n", "        reviews = get_reviews(product_soup)\n", "        if reviews:\n", "            positive_feedback = max(\n", "                reviews,\n", "                key=lambda x: int(x[1].split()[0]) if x[1].split()[0].isdigit() else 0,\n", "            )[0]\n", "            negative_feedback = min(\n", "                reviews,\n", "                key=lambda x: int(x[1].split()[0])\n", "                if x[1].split()[0].isdigit()\n", "                else float(\"inf\"),\n", "            )[0]\n", "            d[\"positive_feedback\"].append(positive_feedback)\n", "            d[\"negative_feedback\"].append(negative_feedback)\n", "        else:\n", "            d[\"positive_feedback\"].append(\"\")\n", "            d[\"negative_feedback\"].append(\"\")\n", "\n", "    amazon_df = pd.DataFrame.from_dict(d)\n", "    amazon_df[\"title\"] = amazon_df[\"title\"].replace(\"\", np.nan)\n", "    amazon_df = amazon_df.dropna(subset=[\"title\"])\n", "    amazon_df.to_csv(\"amazon_data.csv\", header=True, index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "16c00199-b79e-4676-ac22-75f34f407c6c", "metadata": {}, "outputs": [], "source": ["amazon_df.head(4)"]}, {"cell_type": "code", "execution_count": null, "id": "013596c5-1db5-4d69-9eed-c2a5fe30d3f5", "metadata": {}, "outputs": [], "source": ["# def get_title(soup):\n", "#     try:\n", "#         title = soup.find(\"span\", attrs={\"id\": \"productTitle\"}).get_text().strip()\n", "#     except AttributeError:\n", "#         title = \"\"\n", "#     return title\n", "\n", "\n", "def get_price(soup):\n", "    try:\n", "        price = soup.find(\"span\", attrs={\"class\": \"a-offscreen\"}).get_text().strip()\n", "    except AttributeError:\n", "        price = \"\"\n", "    return price\n", "\n", "\n", "def get_rating(soup):\n", "    try:\n", "        rating = soup.find(\"span\", attrs={\"class\": \"a-icon-alt\"}).get_text().strip()\n", "    except AttributeError:\n", "        rating = \"\"\n", "    return rating\n", "\n", "\n", "def get_review_count(soup):\n", "    try:\n", "        review_count = (\n", "            soup.find(\"span\", attrs={\"id\": \"acrCustomerReviewText\"}).get_text().strip()\n", "        )\n", "    except AttributeError:\n", "        review_count = \"\"\n", "    return review_count\n", "\n", "\n", "def get_availability(soup):\n", "    try:\n", "        available = (\n", "            soup.find(\"div\", attrs={\"id\": \"availability\"})\n", "            .find(\"span\")\n", "            .get_text()\n", "            .strip()\n", "        )\n", "    except AttributeError:\n", "        available = \"Not Available\"\n", "    return available\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    HEADERS = {\n", "        \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\n", "        \"Accept-Language\": \"en-US, en;q=0.5\",\n", "    }\n", "\n", "    # URL of the main search results page\n", "    search_url = \"https://www.amazon.com/s?k=haircare+cream+shampoo+spray+products+for+women&crid=2NQ2TTPBLNJ92&sprefix=haircare+cream+shampoo+sprayproducts+for+women%2Caps%2C518&ref=nb_sb_noss\"\n", "\n", "    # HTTP Request\n", "    search_page = requests.get(search_url, headers=HEADERS)\n", "\n", "    # Soup Object containing all data\n", "    search_soup = BeautifulSoup(search_page.content, \"html.parser\")\n", "\n", "    # Fetch product links from the search results page\n", "    product_links = []\n", "    links = search_soup.find_all(\"a\", attrs={\"class\": \"a-link-normal s-no-outline\"})\n", "\n", "    for link in links:\n", "        product_links.append(\"https://www.amazon.com\" + link.get(\"href\"))\n", "\n", "    d = {\n", "        \"title\": [],\n", "        \"price\": [],\n", "        \"rating\": [],\n", "        \"reviews\": [],\n", "        \"availability\": [],\n", "        \"link\": [],\n", "    }\n", "\n", "    for product_url in product_links:\n", "        # Correcting URL format\n", "        product_url = product_url.split(\"https://www.amazon.com\")[-1]\n", "        product_url = \"https://www.amazon.com\" + product_url\n", "\n", "        # HTTP Request\n", "        product_page = requests.get(product_url, headers=HEADERS)\n", "\n", "        # Soup Object containing all data\n", "        product_soup = BeautifulSoup(product_page.content, \"html.parser\")\n", "\n", "        # Function calls to display all necessary product information\n", "        d[\"title\"].append(get_title(product_soup))\n", "        d[\"price\"].append(get_price(product_soup))\n", "        d[\"rating\"].append(get_rating(product_soup))\n", "        d[\"reviews\"].append(get_review_count(product_soup))\n", "        d[\"availability\"].append(get_availability(product_soup))\n", "        d[\"link\"].append(product_url)\n", "\n", "    amazon_df = pd.DataFrame.from_dict(d)\n", "    amazon_df[\"title\"] = amazon_df[\"title\"].replace(\"\", np.nan)\n", "    amazon_df = amazon_df.dropna(subset=[\"title\"])\n", "    amazon_df.to_csv(\"amazon_data.csv\", header=True, index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "ea680997-dccc-499d-b6f4-c35ad9049630", "metadata": {}, "outputs": [], "source": ["amazon_df.head(3)"]}, {"cell_type": "code", "execution_count": null, "id": "5dbd01d2-8d61-4ae2-98a2-e50161a097b5", "metadata": {}, "outputs": [], "source": ["# def get_title(soup):\n", "#     try:\n", "#         title = soup.find(\"span\", attrs={\"id\": \"productTitle\"}).get_text().strip()\n", "#     except AttributeError:\n", "#         title = \"\"\n", "#     return title\n", "\n", "\n", "def get_price(soup):\n", "    try:\n", "        price = soup.find(\"span\", attrs={\"class\": \"a-offscreen\"}).get_text().strip()\n", "    except AttributeError:\n", "        price = \"\"\n", "    return price\n", "\n", "\n", "def get_rating(soup):\n", "    try:\n", "        rating = soup.find(\"span\", attrs={\"class\": \"a-icon-alt\"}).get_text().strip()\n", "    except AttributeError:\n", "        rating = \"\"\n", "    return rating\n", "\n", "\n", "def get_review_count(soup):\n", "    try:\n", "        review_count = (\n", "            soup.find(\"span\", attrs={\"id\": \"acrCustomerReviewText\"}).get_text().strip()\n", "        )\n", "    except AttributeError:\n", "        review_count = \"\"\n", "    return review_count\n", "\n", "\n", "def get_availability(soup):\n", "    try:\n", "        available = (\n", "            soup.find(\"div\", attrs={\"id\": \"availability\"})\n", "            .find(\"span\")\n", "            .get_text()\n", "            .strip()\n", "        )\n", "    except AttributeError:\n", "        available = \"Not Available\"\n", "    return available\n", "\n", "\n", "def get_reviews(soup):\n", "    reviews = []\n", "    try:\n", "        review_divs = soup.find_all(\"div\", attrs={\"data-hook\": \"review\"})\n", "        for review in review_divs:\n", "            rating = (\n", "                review.find(\"i\", attrs={\"data-hook\": \"review-star-rating\"})\n", "                .get_text()\n", "                .strip()\n", "            )\n", "            review_text = (\n", "                review.find(\"span\", attrs={\"data-hook\": \"review-body\"})\n", "                .get_text()\n", "                .strip()\n", "            )\n", "            reviews.append({\"rating\": rating, \"text\": review_text})\n", "    except AttributeError:\n", "        reviews = []\n", "    return reviews\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    HEADERS = {\n", "        \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\n", "        \"Accept-Language\": \"en-US, en;q=0.5\",\n", "    }\n", "\n", "    search_url = \"https://www.amazon.com/s?k=haircare+cream+shampoo+spray+products+for+women&crid=2NQ2TTPBLNJ92&sprefix=haircare+cream+shampoo+sprayproducts+for+women%2Caps%2C518&ref=nb_sb_noss\"\n", "\n", "    search_page = requests.get(search_url, headers=HEADERS)\n", "    search_soup = BeautifulSoup(search_page.content, \"html.parser\")\n", "\n", "    product_links = []\n", "    links = search_soup.find_all(\"a\", attrs={\"class\": \"a-link-normal s-no-outline\"})\n", "    for link in links:\n", "        product_links.append(\"https://www.amazon.com\" + link.get(\"href\"))\n", "\n", "    d = {\n", "        \"title\": [],\n", "        \"price\": [],\n", "        \"rating\": [],\n", "        \"reviews\": [],\n", "        \"availability\": [],\n", "        \"link\": [],\n", "        \"most_positive_review\": [],\n", "        \"most_negative_review\": [],\n", "    }\n", "\n", "    for product_url in product_links:\n", "        product_url = product_url.split(\"https://www.amazon.com\")[-1]\n", "        product_url = \"https://www.amazon.com\" + product_url\n", "\n", "        product_page = requests.get(product_url, headers=HEADERS)\n", "        product_soup = BeautifulSoup(product_page.content, \"html.parser\")\n", "\n", "        d[\"title\"].append(get_title(product_soup))\n", "        d[\"price\"].append(get_price(product_soup))\n", "        d[\"rating\"].append(get_rating(product_soup))\n", "        d[\"reviews\"].append(get_review_count(product_soup))\n", "        d[\"availability\"].append(get_availability(product_soup))\n", "        d[\"link\"].append(product_url)\n", "\n", "        reviews = get_reviews(product_soup)\n", "        if reviews:\n", "            sorted_reviews = sorted(reviews, key=lambda x: x[\"rating\"], reverse=True)\n", "            most_positive_review = sorted_reviews[0][\"text\"]\n", "            most_negative_review = sorted_reviews[-1][\"text\"]\n", "        else:\n", "            most_positive_review = \"No reviews\"\n", "            most_negative_review = \"No reviews\"\n", "\n", "        d[\"most_positive_review\"].append(most_positive_review)\n", "        d[\"most_negative_review\"].append(most_negative_review)\n", "\n", "    amazon_df = pd.DataFrame.from_dict(d)\n", "    amazon_df[\"title\"] = amazon_df[\"title\"].replace(\"\", np.nan)\n", "    amazon_df = amazon_df.dropna(subset=[\"title\"])\n", "    amazon_df.to_csv(\"amazon_data_with_reviews.csv\", header=True, index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "a9736430-c58c-4128-a95d-c00156ca4442", "metadata": {}, "outputs": [], "source": ["amazon_df.head(4)"]}, {"cell_type": "code", "execution_count": null, "id": "f3187a35-6fc6-45a2-a6e2-93ccd4d35cb5", "metadata": {}, "outputs": [], "source": ["# import numpy as np\n", "# import pandas as pd\n", "from textblob import TextBlob\n", "\n", "# amazon_df['reviews'] contains all reviews as a list of strings\n", "\n", "\n", "def analyze_sentiment(review):\n", "    analysis = TextBlob(review)\n", "    return analysis.sentiment.polarity\n", "\n", "\n", "def analyze_effectiveness(review):\n", "    effectiveness_keywords = [\n", "        \"effective\",\n", "        \"works\",\n", "        \"result\",\n", "        \"solution\",\n", "        \"fix\",\n", "        \"OK\",\n", "        \"like\",\n", "    ]\n", "    blob = TextBlob(review)\n", "    word_list = blob.words\n", "    return sum(\n", "        [word_list.count(word) for word in effectiveness_keywords],\n", "    )\n", "\n", "\n", "# Create columns for sentiment and effectiveness scores\n", "amazon_df[\"sentiment_score\"] = amazon_df[\"reviews\"].apply(\n", "    lambda reviews: np.mean([analyze_sentiment(review) for review in reviews])\n", "    if reviews\n", "    else 0,\n", ")\n", "amazon_df[\"effectiveness_score\"] = amazon_df[\"reviews\"].apply(\n", "    lambda reviews: np.mean([analyze_effectiveness(review) for review in reviews])\n", "    if reviews\n", "    else 0,\n", ")\n", "\n", "# Normalize scores to a scale of 1-10\n", "amazon_df[\"likability_score\"] = (\n", "    amazon_df[\"sentiment_score\"] - amazon_df[\"sentiment_score\"].min()\n", ") / (amazon_df[\"sentiment_score\"].max() - amazon_df[\"sentiment_score\"].min()) * 9 + 1\n", "amazon_df[\"effectiveness_score_normalized\"] = (\n", "    amazon_df[\"effectiveness_score\"] - amazon_df[\"effectiveness_score\"].min()\n", ") / (\n", "    amazon_df[\"effectiveness_score\"].max() - amazon_df[\"effectiveness_score\"].min()\n", ") * 9 + 1\n", "\n", "# Drop unnecessary columns for clarity\n", "amazon_df = amazon_df.drop(columns=[\"sentiment_score\", \"effectiveness_score\"])\n", "\n", "# Save the results\n", "amazon_df.to_csv(\"amazon_product_scores.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "cafb43fd-b670-4936-a254-c17d0d3f0673", "metadata": {}, "outputs": [], "source": ["def analyze_sentiment(review):\n", "    analysis = TextBlob(review)\n", "    return analysis.sentiment.polarity\n", "\n", "\n", "def analyze_effectiveness(review):\n", "    effectiveness_keywords = [\n", "        \"effective\",\n", "        \"works\",\n", "        \"result\",\n", "        \"solution\",\n", "        \"fix\",\n", "        \"OK\",\n", "        \"great\",\n", "        \"like\",\n", "        \"best\",\n", "    ]\n", "    blob = TextBlob(review)\n", "    word_list = blob.words\n", "    return sum(\n", "        [word_list.count(word) for word in effectiveness_keywords],\n", "    )\n", "\n", "\n", "# Create columns for sentiment and effectiveness scores\n", "amazon_df[\"sentiment_score\"] = amazon_df[\"reviews\"].apply(\n", "    lambda reviews: np.mean([analyze_sentiment(review) for review in reviews])\n", "    if reviews\n", "    else 0,\n", ")\n", "amazon_df[\"effectiveness_score\"] = amazon_df[\"reviews\"].apply(\n", "    lambda reviews: np.mean([analyze_effectiveness(review) for review in reviews])\n", "    if reviews\n", "    else 0,\n", ")\n", "\n", "# Handle NaN values for scores\n", "amazon_df[\"sentiment_score\"] = amazon_df[\"sentiment_score\"].fillna(0)\n", "amazon_df[\"effectiveness_score\"] = amazon_df[\"effectiveness_score\"].fillna(0)\n", "\n", "# Avoid division by zero in normalization\n", "min_sentiment = amazon_df[\"sentiment_score\"].min()\n", "max_sentiment = amazon_df[\"sentiment_score\"].max()\n", "min_effectiveness = amazon_df[\"effectiveness_score\"].min()\n", "max_effectiveness = amazon_df[\"effectiveness_score\"].max()\n", "\n", "if max_sentiment - min_sentiment == 0:\n", "    amazon_df[\"likability_score\"] = 1  # or any default value\n", "else:\n", "    amazon_df[\"likability_score\"] = (amazon_df[\"sentiment_score\"] - min_sentiment) / (\n", "        max_sentiment - min_sentiment\n", "    ) * 9 + 1\n", "\n", "if max_effectiveness - min_effectiveness == 0:\n", "    amazon_df[\"effectiveness_score_normalized\"] = 1  # or any default value\n", "else:\n", "    amazon_df[\"effectiveness_score_normalized\"] = (\n", "        amazon_df[\"effectiveness_score\"] - min_effectiveness\n", "    ) / (max_effectiveness - min_effectiveness) * 9 + 1\n", "\n", "# Drop unnecessary columns for clarity\n", "amazon_df = amazon_df.drop(columns=[\"sentiment_score\", \"effectiveness_score\"])\n", "\n", "# Save the results\n", "amazon_df.to_csv(\"amazon_product_scores.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "563fb1bd-be83-4c0c-a62e-1baf53fb7ed0", "metadata": {}, "outputs": [], "source": ["def analyze_sentiment(review):\n", "    analysis = TextBlob(review)\n", "    return analysis.sentiment.polarity\n", "\n", "\n", "def analyze_effectiveness(review):\n", "    effectiveness_keywords = [\"effective\", \"works\", \"result\", \"solution\", \"fix\"]\n", "    blob = TextBlob(review)\n", "    word_list = blob.words\n", "    return sum(\n", "        [word_list.count(word) for word in effectiveness_keywords],\n", "    )\n", "\n", "\n", "# Create columns for sentiment and effectiveness scores\n", "amazon_df[\"sentiment_score\"] = amazon_df[\"reviews\"].apply(\n", "    lambda reviews: np.mean([analyze_sentiment(review) for review in reviews])\n", "    if reviews\n", "    else 0,\n", ")\n", "amazon_df[\"effectiveness_score\"] = amazon_df[\"reviews\"].apply(\n", "    lambda reviews: np.mean([analyze_effectiveness(review) for review in reviews])\n", "    if reviews\n", "    else 0,\n", ")\n", "\n", "# Handle NaN values for scores\n", "amazon_df[\"sentiment_score\"] = amazon_df[\"sentiment_score\"].fillna(0)\n", "amazon_df[\"effectiveness_score\"] = amazon_df[\"effectiveness_score\"].fillna(0)\n", "\n", "# Print raw sentiment and effectiveness scores for debugging\n", "\n", "# Avoid division by zero in normalization\n", "min_sentiment = amazon_df[\"sentiment_score\"].min()\n", "max_sentiment = amazon_df[\"sentiment_score\"].max()\n", "min_effectiveness = amazon_df[\"effectiveness_score\"].min()\n", "max_effectiveness = amazon_df[\"effectiveness_score\"].max()\n", "\n", "\n", "if max_sentiment - min_sentiment == 0:\n", "    amazon_df[\"likability_score\"] = 1  # or any default value\n", "else:\n", "    amazon_df[\"likability_score\"] = (amazon_df[\"sentiment_score\"] - min_sentiment) / (\n", "        max_sentiment - min_sentiment\n", "    ) * 9 + 1\n", "\n", "if max_effectiveness - min_effectiveness == 0:\n", "    amazon_df[\"effectiveness_score_normalized\"] = 1  # or any default value\n", "else:\n", "    amazon_df[\"effectiveness_score_normalized\"] = (\n", "        amazon_df[\"effectiveness_score\"] - min_effectiveness\n", "    ) / (max_effectiveness - min_effectiveness) * 9 + 1\n", "\n", "# Drop unnecessary columns for clarity\n", "amazon_df = amazon_df.drop(columns=[\"sentiment_score\", \"effectiveness_score\"])\n", "\n", "# Save the results\n", "amazon_df.to_csv(\"amazon_product_scores.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "8e382e89-571e-456d-b839-55cd4724db31", "metadata": {}, "outputs": [], "source": ["# Check the structure of amazon_df"]}, {"cell_type": "code", "execution_count": null, "id": "ece92bc3-cff0-4d66-894d-3a5419e0479b", "metadata": {}, "outputs": [], "source": ["amazon_df.head(4)"]}, {"cell_type": "code", "execution_count": null, "id": "a9f189e6-3035-4ed0-8bbf-accf6ba934b4", "metadata": {}, "outputs": [], "source": ["# Define keywords for hair types and characteristics\n", "hair_type_keywords = {\n", "    \"Type 1\": [\"straight\", \"smooth\"],\n", "    \"Type 2\": [\"wavy\", \"loose waves\", \"beachy waves\"],\n", "    \"Type 3\": [\"curly\", \"ringlets\"],\n", "    \"Type 4\": [\"coily\", \"kinky\", \"afro\"],\n", "}\n", "\n", "hair_characteristics_keywords = {\n", "    \"Porosity\": [\"low porosity\", \"high porosity\", \"medium porosity\"],\n", "    \"Texture\": [\"fine\", \"thick\", \"coarse\", \"soft\", \"brittle\"],\n", "    \"Density\": [\"thin\", \"thick\", \"sparse\", \"dense\"],\n", "}\n", "\n", "\n", "# Function to extract hair type mentions\n", "def extract_hair_types(review):\n", "    hair_types = {ht: 0 for ht in hair_type_keywords}\n", "    for ht, keywords in hair_type_keywords.items():\n", "        for keyword in keywords:\n", "            if re.search(r\"\\b\" + re.escape(keyword) + r\"\\b\", review, re.IGNORECASE):\n", "                hair_types[ht] += 1\n", "    return hair_types\n", "\n", "\n", "# Function to extract hair characteristics mentions\n", "def extract_hair_characteristics(review):\n", "    characteristics = {hc: 0 for hc in hair_characteristics_keywords}\n", "    for hc, keywords in hair_characteristics_keywords.items():\n", "        for keyword in keywords:\n", "            if re.search(r\"\\b\" + re.escape(keyword) + r\"\\b\", review, re.IGNORECASE):\n", "                characteristics[hc] += 1\n", "    return characteristics\n", "\n", "\n", "# Function to aggregate counts from multiple dictionaries\n", "def aggregate_counts(dicts):\n", "    if not dicts:\n", "        return {key: 0 for key in hair_type_keywords}\n", "    aggregated = {key: 0 for key in dicts[0]}\n", "    for d in dicts:\n", "        for key, value in d.items():\n", "            aggregated[key] += value\n", "    return aggregated"]}, {"cell_type": "code", "execution_count": null, "id": "185d8415-98da-435e-a1ab-d09eac8f70bd", "metadata": {}, "outputs": [], "source": ["# Apply functions to extract and aggregate hair type and characteristics\n", "amazon_df[\"hair_types\"] = amazon_df[\"reviews\"].apply(\n", "    lambda reviews: aggregate_counts([extract_hair_types(review) for review in reviews])\n", "    if reviews\n", "    else {key: 0 for key in hair_type_keywords},\n", ")\n", "amazon_df[\"hair_characteristics\"] = amazon_df[\"reviews\"].apply(\n", "    lambda reviews: aggregate_counts(\n", "        [extract_hair_characteristics(review) for review in reviews],\n", "    )\n", "    if reviews\n", "    else {key: 0 for key in hair_characteristics_keywords},\n", ")\n", "\n", "# Verify the columns are added correctly"]}, {"cell_type": "code", "execution_count": null, "id": "c444b35a-68df-4694-9d3d-9732ae7581e5", "metadata": {}, "outputs": [], "source": ["# Normalize hair type scores (example for Type 1)\n", "amazon_df[\"hair_type_1_score\"] = amazon_df[\"hair_types\"].apply(\n", "    lambda ht: ht.get(\"Type 1\", 0),\n", ")\n", "amazon_df[\"hair_type_1_score\"] = (\n", "    amazon_df[\"hair_type_1_score\"] - amazon_df[\"hair_type_1_score\"].min()\n", ") / (\n", "    amazon_df[\"hair_type_1_score\"].max() - amazon_df[\"hair_type_1_score\"].min()\n", ") * 9 + 1"]}, {"cell_type": "code", "execution_count": null, "id": "023366ca-6d79-40ea-a143-e9987d6acfcc", "metadata": {}, "outputs": [], "source": ["amazon_df"]}, {"cell_type": "code", "execution_count": null, "id": "621f1b68-363f-430c-9b2c-ea9677fe9a54", "metadata": {}, "outputs": [], "source": ["# Ensure that NaN is handled by providing a default zero count if no data is present\n", "def safe_aggregate_counts(dicts):\n", "    if not dicts:\n", "        return {key: 0 for key in hair_type_keywords}\n", "    aggregated = {key: 0 for key in dicts[0]}\n", "    for d in dicts:\n", "        for key, value in d.items():\n", "            aggregated[key] += value\n", "    return aggregated\n", "\n", "\n", "# Apply functions to extract and aggregate hair type and characteristics\n", "amazon_df[\"hair_types\"] = amazon_df[\"reviews\"].apply(\n", "    lambda reviews: safe_aggregate_counts(\n", "        [extract_hair_types(review) for review in reviews],\n", "    )\n", "    if reviews\n", "    else {key: 0 for key in hair_type_keywords},\n", ")\n", "amazon_df[\"hair_characteristics\"] = amazon_df[\"reviews\"].apply(\n", "    lambda reviews: safe_aggregate_counts(\n", "        [extract_hair_characteristics(review) for review in reviews],\n", "    )\n", "    if reviews\n", "    else {key: 0 for key in hair_characteristics_keywords},\n", ")\n", "\n", "\n", "# Normalize hair type scores (example for Type 1)\n", "def normalize_column(df, col_name):\n", "    col_min = df[col_name].min()\n", "    col_max = df[col_name].max()\n", "    if col_max == col_min:\n", "        df[col_name + \"_score\"] = 5  # Assign middle score if all values are the same\n", "    else:\n", "        df[col_name + \"_score\"] = (df[col_name] - col_min) / (col_max - col_min) * 9 + 1\n", "    return df\n", "\n", "\n", "# Extracting specific hair type scores\n", "amazon_df[\"hair_type_1_score\"] = amazon_df[\"hair_types\"].apply(\n", "    lambda ht: ht.get(\"Type 1\", 0),\n", ")\n", "amazon_df = normalize_column(amazon_df, \"hair_type_1_score\")\n", "\n", "# Repeat extraction and normalization for other hair types and characteristics as needed\n", "amazon_df[\"hair_type_2_score\"] = amazon_df[\"hair_types\"].apply(\n", "    lambda ht: ht.get(\"Type 2\", 0),\n", ")\n", "amazon_df = normalize_column(amazon_df, \"hair_type_2_score\")\n", "\n", "amazon_df[\"hair_type_3_score\"] = amazon_df[\"hair_types\"].apply(\n", "    lambda ht: ht.get(\"Type 3\", 0),\n", ")\n", "amazon_df = normalize_column(amazon_df, \"hair_type_3_score\")\n", "\n", "amazon_df[\"hair_type_4_score\"] = amazon_df[\"hair_types\"].apply(\n", "    lambda ht: ht.get(\"Type 4\", 0),\n", ")\n", "amazon_df = normalize_column(amazon_df, \"hair_type_4_score\")\n", "\n", "# Print the updated DataFrame to verify the results"]}, {"cell_type": "code", "execution_count": null, "id": "3377cd24-38d5-41ef-89af-366e3aabab76", "metadata": {}, "outputs": [], "source": ["amazon_df.head(4)"]}, {"cell_type": "code", "execution_count": null, "id": "0d547088-289b-402e-958f-0a4abcafa192", "metadata": {}, "outputs": [], "source": ["# benefit_keywords = [\n", "#     \"moisturizing\",\n", "#     \"volumizing\",\n", "#     \"softening\",\n", "#     \"strengthening\",\n", "#     \"shine\",\n", "#     \"smoothing\",\n", "# ]\n", "#\n", "#\n", "# def extract_benefits(review):\n", "#     benefits = {key: 0 for key in benefit_keywords}\n", "#     for word in review.lower().split():\n", "#         for key in benefit_keywords:\n", "#             if key in word:\n", "#                 benefits[key] += 1\n", "#     return benefits\n", "#\n", "#\n", "# def safe_aggregate_benefits(dicts):\n", "#     if not dicts:\n", "#         return {key: 0 for key in benefit_keywords}\n", "#     aggregated = {key: 0 for key in dicts[0]}\n", "#     for d in dicts:\n", "#         for key, value in d.items():\n", "#             aggregated[key] += value\n", "#     return aggregated\n", "#\n", "#\n", "# # Apply function to extract and aggregate benefits\n", "# amazon_df[\"benefits\"] = amazon_df[\"reviews\"].apply(\n", "#     lambda reviews: safe_aggregate_benefits(\n", "#         [extract_benefits(review) for review in reviews],\n", "#     )\n", "#     if reviews\n", "#     else {key: 0 for key in benefit_keywords},\n", "# )\n", "#\n", "# # Extracting specific benefit scores\n", "# for benefit in benefit_keywords:\n", "#     amazon_df[benefit + \"_score\"] = amazon_df[\"benefits\"].apply(lambda b: b[benefit])\n", "#\n", "#\n", "# # Normalize benefit scores\n", "# def normalize_benefit_scores(df, benefit_keywords):\n", "#     for benefit in benefit_keywords:\n", "#         col_name = benefit + \"_score\"\n", "#         df = normalize_column(df, col_name)\n", "#     return df\n", "#\n", "#\n", "# amazon_df = normalize_benefit_scores(amazon_df, benefit_keywords)\n", "#\n", "# # Print the updated DataFrame to verify the results"]}, {"cell_type": "code", "execution_count": null, "id": "07268a4c-cb15-4aa8-97cb-ed5735dd4959", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7339b427-1881-43ee-bfbf-a00f29b5ce15", "metadata": {}, "outputs": [], "source": ["# Define key benefits keywords (add more as needed)\n", "benefits_keywords = {\n", "    \"moisturizing\": [\"moisturiz\", \"hydrat\"],\n", "    \"smoothing\": [\"smooth\", \"silky\"],\n", "    \"repairing\": [\"repair\", \"restore\"],\n", "    \"strengthening\": [\"strength\", \"fortif\"],\n", "    \"volumizing\": [\"volume\", \"thicken\"],\n", "}\n", "\n", "\n", "# Function to extract and aggregate benefits from reviews\n", "def extract_main_benefits(review):\n", "    if not review:\n", "        return {key: 0 for key in benefits_keywords}\n", "    benefits_count = {key: 0 for key in benefits_keywords}\n", "    for benefit, keywords in benefits_keywords.items():\n", "        for keyword in keywords:\n", "            if keyword in review.lower():\n", "                benefits_count[benefit] += 1\n", "    return benefits_count\n", "\n", "\n", "# Apply function to extract benefits from reviews column\n", "amazon_df[\"main_benefits\"] = amazon_df[\"reviews\"].apply(extract_main_benefits)\n", "\n", "# Example: Print main benefits for the first few products\n", "for _index, row in amazon_df.head().iterrows():\n", "    for benefit, count in row[\"main_benefits\"].items():\n", "        if count > 0:\n", "            pass"]}, {"cell_type": "code", "execution_count": null, "id": "ff57668f-fced-4413-b7b6-0424978be9db", "metadata": {}, "outputs": [], "source": ["benefit_keywords = [\n", "    \"moisturizing\",\n", "    \"volumizing\",\n", "    \"softening\",\n", "    \"strengthening\",\n", "    \"shine\",\n", "    \"smoothing\",\n", "]\n", "\n", "\n", "def extract_benefits(review):\n", "    benefits = {key: 0 for key in benefit_keywords}\n", "    for word in review.lower().split():\n", "        for key in benefit_keywords:\n", "            if key in word:\n", "                benefits[key] += 1\n", "    return benefits\n", "\n", "\n", "def safe_aggregate_benefits(dicts):\n", "    if not dicts:\n", "        return {key: 0 for key in benefit_keywords}\n", "    aggregated = {key: 0 for key in dicts[0]}\n", "    for d in dicts:\n", "        for key, value in d.items():\n", "            aggregated[key] += value\n", "    return aggregated\n", "\n", "\n", "# Apply function to extract and aggregate benefits\n", "amazon_df[\"benefits\"] = amazon_df[\"reviews\"].apply(\n", "    lambda reviews: safe_aggregate_benefits(\n", "        [extract_benefits(review) for review in reviews],\n", "    )\n", "    if reviews\n", "    else {key: 0 for key in benefit_keywords},\n", ")\n", "\n", "# Extracting specific benefit scores\n", "for benefit in benefit_keywords:\n", "    amazon_df[benefit + \"_score\"] = amazon_df[\"benefits\"].apply(lambda b: b[benefit])\n", "\n", "\n", "# Normalize benefit scores (if needed, similar to hair type normalization)\n", "def normalize_benefit_scores(df, benefit_keywords):\n", "    for benefit in benefit_keywords:\n", "        col_name = benefit + \"_score\"\n", "        df = normalize_column(df, col_name)\n", "    return df\n", "\n", "\n", "amazon_df = normalize_benefit_scores(amazon_df, benefit_keywords)\n", "\n", "# Print the updated DataFrame to verify the results\n", "amazon_df.head()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}