"""
Refactored Products API using the new consolidated router patterns.

This demonstrates how to use the new BaseRouter and CRUDRouter classes
to reduce code duplication and standardize API patterns.
"""

from urllib.parse import unquote_plus

from django.contrib.postgres.search import SearchQuery, SearchVector
from django.db.models import Q, QuerySet
from loguru import logger
from ninja import Query

from helpers.api_router import BaseRouter, CRUDRouter, handle_api_error
from helpers.schemas import SuccessOut

from .models import Category, Product
from .schemas import (
    CategoryCreateSchema,
    CategorySchema,
    ProductInSchema,
    ProductOutSchema,
)

# ============================================================================
# Products Router using CRUDRouter
# ============================================================================


class ProductCRUDRouter(CRUDRouter):
    """
    Products CRUD router with custom search and filtering.
    """

    def __init__(self):
        super().__init__(
            model=Product,
            schema_out=ProductOutSchema,
            schema_in=ProductInSchema,
            tags=["Products"],
        )
        self._add_custom_endpoints()

    def get_queryset(self, request) -> QuerySet:
        """Get products queryset with optional filtering."""
        queryset = Product.objects.all()

        # Apply search if provided
        search = request.GET.get("search")
        if search:
            queryset = self._apply_search(queryset, search)

        # Apply category filter if provided
        category = request.GET.get("category")
        if category:
            queryset = queryset.filter(category__name__icontains=category)

        return queryset

    def _apply_search(self, queryset: QuerySet, search_term: str) -> QuerySet:
        """Apply full-text search to products."""
        try:
            # Use PostgreSQL full-text search if available
            search_vector = SearchVector("name", "ingredients", "insights")
            search_query = SearchQuery(search_term)
            return queryset.annotate(search=search_vector).filter(search=search_query)
        except Exception:
            # Fallback to simple icontains search
            return queryset.filter(
                Q(name__icontains=search_term)
                | Q(ingredients__icontains=search_term)
                | Q(insights__icontains=search_term)
            )

    def _add_custom_endpoints(self):
        """Add custom endpoints beyond basic CRUD."""

        @self.get_with_standard_responses("/search", list[ProductOutSchema])
        @handle_api_error
        def search_products(
            request,
            q: str = Query(..., min_length=2),
            page: int = Query(1, gt=0),
            page_size: int = Query(20, gt=0, le=100),
        ):
            """Advanced product search with full-text capabilities."""
            queryset = self._apply_search(Product.objects.all(), q)

            from django.core.paginator import Paginator

            paginator = Paginator(queryset, page_size)
            return paginator.get_page(page)

        @self.get_with_standard_responses(
            "/by-category/{category_name}", list[ProductOutSchema]
        )
        @handle_api_error
        def get_products_by_category(
            request,
            category_name: str,
            page: int = Query(1, gt=0),
            page_size: int = Query(20, gt=0, le=100),
        ):
            """Get products by category name."""
            decoded_category = unquote_plus(category_name)
            queryset = Product.objects.filter(
                category__name__icontains=decoded_category
            )

            from django.core.paginator import Paginator

            paginator = Paginator(queryset, page_size)
            return paginator.get_page(page)

        @self.get_with_standard_responses("/featured", list[ProductOutSchema])
        @handle_api_error
        def get_featured_products(request):
            """Get featured products (high rating or special flag)."""
            # Example: products with rating > 4.0 or featured flag
            queryset = Product.objects.filter(
                Q(rating__gte="4.0") | Q(name__icontains="featured")
            )[:10]  # Limit to 10 featured products
            return list(queryset)


# ============================================================================
# Categories Router using BaseRouter
# ============================================================================


class CategoryRouter(BaseRouter):
    """
    Categories router with custom functionality.
    """

    def __init__(self):
        super().__init__(tags=["Categories"])
        self._register_endpoints()

    def _register_endpoints(self):
        """Register category endpoints."""

        @self.get_with_standard_responses("/categories", list[CategorySchema])
        @handle_api_error
        def list_categories(request):
            """List all product categories."""
            return Category.objects.all()

        @self.get_with_standard_responses(
            "/categories/{int:category_id}", CategorySchema
        )
        @handle_api_error
        def get_category(request, category_id: int):
            """Get a specific category by ID."""
            from django.shortcuts import get_object_or_404

            return get_object_or_404(Category, pk=category_id)

        @self.post_with_standard_responses("/categories", CategorySchema)
        @handle_api_error
        def create_category(request, payload: CategoryCreateSchema):
            """Create a new category."""
            return Category.objects.create(**payload.dict())

        @self.get_with_standard_responses(
            "/categories/{int:category_id}/products", list[ProductOutSchema]
        )
        @handle_api_error
        def get_category_products(
            request,
            category_id: int,
            page: int = Query(1, gt=0),
            page_size: int = Query(20, gt=0, le=100),
        ):
            """Get all products in a specific category."""
            from django.core.paginator import Paginator
            from django.shortcuts import get_object_or_404

            category = get_object_or_404(Category, pk=category_id)
            products = Product.objects.filter(category=category)
            paginator = Paginator(products, page_size)
            return paginator.get_page(page)


# ============================================================================
# Combined Router
# ============================================================================

# Create the main router that combines both
router = BaseRouter(tags=["Products"])

# Create sub-routers
product_router = ProductCRUDRouter()
category_router = CategoryRouter()

# Add sub-router endpoints to main router
# Note: In a real implementation, you might want to use router.add_router()
# or include the endpoints directly. This is a simplified example.

# Copy endpoints from product router
for url_pattern in product_router.urls:
    router.urls.append(url_pattern)

# Copy endpoints from category router
for url_pattern in category_router.urls:
    router.urls.append(url_pattern)


# ============================================================================
# Additional Utility Endpoints
# ============================================================================


@router.get_with_standard_responses("/stats", SuccessOut)
@handle_api_error
def get_product_stats(request):
    """Get product statistics."""
    total_products = Product.objects.count()
    total_categories = Category.objects.count()

    # Get category distribution
    category_stats = {}
    for category in Category.objects.all():
        category_stats[category.name] = Product.objects.filter(
            category=category
        ).count()

    return {
        "success": True,
        "message": "Product statistics",
        "data": {
            "total_products": total_products,
            "total_categories": total_categories,
            "category_distribution": category_stats,
        },
    }


@router.get_with_standard_responses("/health", SuccessOut)
@handle_api_error
def products_health_check(request):
    """Health check for products API."""
    try:
        # Simple database connectivity check
        product_count = Product.objects.count()
        category_count = Category.objects.count()

        return {
            "success": True,
            "message": "Products API is healthy",
            "data": {
                "status": "operational",
                "product_count": product_count,
                "category_count": category_count,
            },
        }
    except Exception as e:
        logger.error(f"Products health check failed: {e}")
        return {
            "success": False,
            "message": "Products API health check failed",
            "data": {"status": "error", "error": str(e)},
        }


# ============================================================================
# Export the router
# ============================================================================

# The router is now ready to be imported and used in the main API
# Example usage in main API file:
# from products.api_refactored import router as products_router
# api.add_router("/products/", products_router, tags=["Products"])
