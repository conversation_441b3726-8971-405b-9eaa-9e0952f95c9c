# Generated by Django 5.2 on 2025-07-20 07:36

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0006_alter_product_category"),
    ]

    operations = [
        migrations.AddIndex(
            model_name="product",
            index=models.Index(
                fields=["category", "price"], name="products_pr_categor_47b724_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="product",
            index=models.Index(
                fields=["hairtype"], name="products_pr_hairtyp_39bd0c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="product",
            index=models.Index(
                fields=["porosity"], name="products_pr_porosit_575465_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="product",
            index=models.Index(
                fields=["texture"], name="products_pr_texture_7ee39e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="product",
            index=models.Index(fields=["name"], name="products_pr_name_9ff0a3_idx"),
        ),
    ]
