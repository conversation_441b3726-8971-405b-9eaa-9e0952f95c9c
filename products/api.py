from functools import reduce
from operator import or_
from typing import Annotated
from urllib.parse import unquote_plus

from django.conf import settings
from django.core.paginator import Paginator
from django.db.models import Q
from django.shortcuts import get_object_or_404
from loguru import logger
from ninja import Path, Query, Router

# Import PostgreSQL search features only if using PostgreSQL
try:
    from django.contrib.postgres.search import SearchQuery, SearchVector

    POSTGRES_SEARCH_AVAILABLE = True
except ImportError:
    POSTGRES_SEARCH_AVAILABLE = False

from helpers.error_handlers import handle_api_error

# Import shared schemas
from helpers.schemas import MessageOut

from .models import Category, Product
from .schemas import (
    CategoryCreateSchema,
    CategorySchema,
    ProductCreateSchema,
    ProductInSchema,
    ProductOutSchema,
    pathParams,
)

router = Router()


def is_postgres_db():
    """Check if the current database backend is PostgreSQL."""
    return "postgresql" in settings.DATABASES["default"]["ENGINE"]


def search_products_by_name(name: str):
    """
    Search products by name using appropriate method based on database backend.
    Uses PostgreSQL full-text search if available, otherwise falls back to icontains.
    """
    if is_postgres_db() and POSTGRES_SEARCH_AVAILABLE:
        # Use PostgreSQL full-text search
        return Product.objects.annotate(
            search=SearchVector("name"),
        ).filter(search=SearchQuery(name))
    else:
        # Use simple case-insensitive contains search for SQLite
        return Product.objects.filter(name__icontains=name)


def search_products_by_names(names: list[str]):
    """
    Search products by multiple names using appropriate method based on database backend.
    """
    if is_postgres_db() and POSTGRES_SEARCH_AVAILABLE:
        # Use PostgreSQL full-text search with OR queries
        search_query = reduce(or_, (SearchQuery(term) for term in names))
        return (
            Product.objects.annotate(search=SearchVector("name"))
            .filter(search=search_query)
            .order_by("id")
            .distinct()
        )
    else:
        # Use Q objects with icontains for SQLite
        query = Q()
        for name in names:
            query |= Q(name__icontains=name)
        return Product.objects.filter(query).order_by("id").distinct()


# Product Endpoints
@router.get(
    "/",
    response={200: list[ProductOutSchema], 404: MessageOut, 500: MessageOut},
)
@handle_api_error
def get_all_products(
    request, page: int = Query(1, gt=0), page_size: int = Query(20, gt=0, le=100)
):
    # No need for manual try-catch - @handle_api_error decorator handles all exceptions
    # Optimize query with select_related for category and prefetch for related data
    products = Product.objects.select_related("category").prefetch_related(
        "recommended_users"
    )
    paginator = Paginator(products, page_size)
    return paginator.get_page(page)


@router.get(
    "/id/{int:product_id}",
    response={200: ProductOutSchema, 404: MessageOut, 500: MessageOut},
)
@handle_api_error
def get_product_by_id(request, product_id: int = Path(..., gt=-1)):
    # No need for manual try-catch - @handle_api_error decorator handles all exceptions
    # get_object_or_404 raises ObjectDoesNotExist which is handled by the decorator
    product = get_object_or_404(Product, id=product_id)
    return 200, product


@router.get(
    "/name/{path:product_name}",
    response={200: list[ProductOutSchema], 404: MessageOut, 500: MessageOut},
)
def get_product_by_name(request, product_name: str):
    """
    Search for product by name.
    """
    if not product_name:
        logger.error("Product name is required")
        return 404, {"message": "Product name is required"}
    try:
        logger.info(f"Product name: {product_name}")
        product_name = pathParams.decode(product_name)
        product_qs = search_products_by_name(product_name)

        if not product_qs.exists():
            logger.error(f"Error getting product by name: {product_name}")
            return 404, {"message": "Product not found"}
        return 200, product_qs
    except Exception as e:
        logger.error(f"Error getting product by name: {product_name}")
        return 500, {"message": str(e)}


@router.get(
    "/names",
    response={
        200: list[ProductOutSchema],
        400: MessageOut,
        404: MessageOut,
        500: MessageOut,
    },
)
def get_products_by_names(
    request,
    name: Annotated[
        list[str],
        Query(..., description="Repeat the `name` parameter for each product you want"),
    ],
):
    """
    Bulk search for products by *fuzzy* name.

    Example:
        GET /api/products/names?name=Aloe&name=Conditiona%20shamps
    """
    if not name:
        return 400, {"message": "`name` query parameter is required at least once"}

    try:
        # Decode each name (handles '+' → space and percent-escapes)
        decoded_terms = [unquote_plus(n) for n in name]
        logger.info(f"Searching for products with names: {decoded_terms}")

        # Use the helper function that works with both PostgreSQL and SQLite
        products_qs = search_products_by_names(decoded_terms)

        if not products_qs.exists():
            logger.warning(f"No products found for names: {decoded_terms}")
            return 404, {"message": "No products matched any of the supplied names"}

        logger.info(f"Found {products_qs.count()} products matching the search terms")
        return 200, list(products_qs)

    except Exception as exc:
        logger.exception(f"Error searching for products by names: {decoded_terms}")
        return 500, {"message": str(exc)}


@router.get(
    "/recommendations",
    response={200: list[ProductOutSchema], 404: MessageOut, 500: MessageOut},
    tags=["Products", "Recommendations"],
)
def get_multi_products_by_list(request, product_ids: list[int] = Query(...)):
    try:
        return Product.objects.filter(id__in=product_ids)
    except Product.DoesNotExist:
        return 404, {"message": "Product not found"}
    except Exception as e:
        return 500, {"message": str(e)}


@router.post("/", response={201: ProductOutSchema, 500: MessageOut})
@handle_api_error
def create_product(request, payload: ProductCreateSchema):
    """TODO: Bug in crete product
    Create a new product.
    """
    category = None
    if payload.category_id:
        category = get_object_or_404(Category, id=payload.category_id)
    # No need for manual try-catch - @handle_api_error decorator handles all exceptions
    return 201, Product.objects.create(**payload.dict(), category=category)
    # return Product.objects.create(
    #     category=category,
    #     sku=payload.sku,
    #     name=payload.name,
    #     ingredients=payload.ingredients,
    #     insights=payload.insights,
    #     protein_moisture=payload.protein_moisture,
    # hairtype=payload.hairtype,
    # porosity=payload.porosity,
    # texture=payload.texture,
    # special_instruction=payload.special_instruction,
    # link=payload.link,
    # hair_goals=payload.hair_goals,
    # hair_issues=payload.hair_issues,
    # internal_insights=payload.internal_insights,
    # has_sizes=payload.has_sizes,
    # price=payload.price,
    # rating=payload.rating,
    # image_url=payload.image_url,
    # image=payload.image,
    # )
    # except Exception as e:
    #     return 500, {"message": str(e)}


@router.put("/{product_id}", response=ProductInSchema)
def update_product(request, product_id: int, payload: ProductCreateSchema):
    product = get_object_or_404(Product, id=product_id)
    if payload.category_id:
        product.category = get_object_or_404(Category, id=payload.category_id)
    for attr, value in payload.dict().items():
        if attr != "category_id":
            setattr(product, attr, value)
    product.save()
    return product


@router.delete("/{product_id}", response={204: None})
def delete_product(request, product_id: int) -> int:
    product = get_object_or_404(Product, id=product_id)
    product.delete()
    return 204


# Category Endpoints
@router.get(
    "/categories/",
    response={200: list[CategorySchema], 404: MessageOut, 500: MessageOut},
)
def get_all_product_categories(request):
    try:
        return 200, Category.objects.all()
    except Category.DoesNotExist:
        return 404, {"message": "Category not found"}
    except Exception as e:
        return 500, {"message": str(e)}


@router.get(
    "/categories/{path:category_name}",
    response={200: list[CategorySchema], 404: MessageOut, 500: MessageOut},
)
def get_category_by_name(request, category_name: str):
    try:
        return 200, Category.objects.filter(name=category_name)
    except Category.DoesNotExist:
        return 404, {"message": "Category not found"}
    except Exception as e:
        return 500, {"message": str(e)}


@router.get(
    "/categories/{category_id}",
    response={200: CategorySchema, 404: MessageOut, 500: MessageOut},
)
def get_category_by_id(request, category_id: int = Path(..., gt=0)):
    try:
        return 200, get_object_or_404(Category, id=category_id)
    except Category.DoesNotExist:
        return 404, {"message": "Category not found"}
    except Exception as e:
        return 500, {"message": str(e)}


@router.post("/categories/", response={204: CategorySchema, 500: MessageOut})
def create_category(request, payload: CategoryCreateSchema):
    try:
        Category.objects.create(**payload.dict())
        return 204, {"message": "Category created successfully"}
    except Exception as e:
        return 500, {"message": str(e)}


@router.put(
    "/categories/{category_id}",
    response={200: CategorySchema, 404: MessageOut, 500: MessageOut},
)
def update_category(request, category_id: int, payload: CategoryCreateSchema):
    try:
        category = get_object_or_404(Category, id=category_id)
        for attr, value in payload.dict().items():
            setattr(category, attr, value)
            category.save()
        return 200, category
    except Category.DoesNotExist:
        return 404, {"message": "Category not found"}
    except Exception as e:
        return 500, {"message": str(e)}


@router.delete(
    "/categories/{category_id}", response={204: None, 404: MessageOut, 500: MessageOut}
)
def delete_category(request, category_id: int) -> int:
    try:
        category = get_object_or_404(Category, id=category_id)
        category.delete()
        return 204, {"message": "Category deleted successfully"}
    except Category.DoesNotExist:
        return 404, {"message": "Category not found"}
    except Exception as e:
        return 500, {"message": str(e)}
