"""
Refactored, test‑friendly implementation of the legacy *ConditionScores*
algorithm.

The scoring logic has **zero external dependencies beyond *pandas* and
*numpy***, and no knowledge of Django or the data‑layer.  Feed it a plain
``dict`` of questionnaire answers and it returns a one‑row *DataFrame* that
mirrors the old interface.

All regex patterns are **case‑insensitive**.
"""

from __future__ import annotations

import re
from typing import Any

import numpy as np
import pandas as pd


# --------------------------------------------------------------------- #
# Utility helpers                                                       #
# --------------------------------------------------------------------- #
def _regex_score(
    value: str | None, mapping: dict[str, int], default=np.nan
) -> int | float:
    """
    Iterate through *mapping* (pattern → score) and return the first score
    whose pattern matches *value* (``re.I``).  If *value* is falsy or there
    is no match, return *default* (``numpy.nan`` by default).
    """
    if not value:
        return default
    for pattern, score in mapping.items():
        if re.search(pattern, str(value), flags=re.I):
            return score
    return default


# --------------------------------------------------------------------- #
# Mappings (collapsed from the original 800‑line implementation)        #
# --------------------------------------------------------------------- #

_POROSITY = {
    r"A\s*-repels": 0,
    r"B\s*- Its bouncy": 1,
    r"C- Easily absorbs": 2,
}

_ISSUE = {
    r"scalp dryness|breakage|split|thinning": 2,
    r"not me": 0,
}

_HAIR_OVERALL = {
    r"generally dry|flaky scalp": 3,
    r"oily roots": 2,
    r"fairly balanced": 0,
}

_DAILY_WATER = {
    r"quarter litre": 2,
    r"half a litre": 1,
    r"1 litre": 0,
    r"don.?t drink water": 3,
}

_HAIR_FEELS = {
    r"coarse and dry": 1,
    r"weighed down|limp|not responding|none of these": 0,
}

_BINARY_ONE = {
    r"yes|relaxers|perm|keratin|bleach|colour|natural extensions|synthetic|weave|blow dryer|straightening iron|curling iron": 1,
    r"no not me|no stylers": 0,
}

_HEAT_FREQ = {
    r"everyday": 3,
    r"once or twice a week": 2,
    r"once or twice a month": 1,
    r"rarely|i dont do this": 0,
}

_SCALP_FEELING = {
    r"a little bit tight": 1,
    r"tight & itchy|itchy & painful|itchy|sensitive": 2,
    r"none of the above": 0,
}

_THREE_SCALE = {
    r"rarely|1 - 2 days|within hours": 1,  # overridden below for specific Qs
    r"usually|3 - 4 days|1 - 2 days": 2,
    r"always|5": 3,
    r"no not me|my scalp gets dry|my scalp gets oily": 0,
}


# --------------------------------------------------------------------- #
# Main scorer                                                           #
# --------------------------------------------------------------------- #
class ConditionScorer:
    """
    Stateless scorer.  Instantiate once and call :meth:`score` with a
    *questionnaire* dict.
    """

    # Max values copied from the original spreadsheet‑driven rules
    _MAX = dict(
        Dry1=13,
        Dam=15,
        SEN=4.4,
        Oil=4.2,
        DSC=4.2,
        FL=4.1,
    )

    def score(self, answers: dict[str, Any]) -> pd.DataFrame:
        """
        Parameters
        ----------
        answers:
            Mapping of *identifier → raw string answer* as collected from
            the questionnaire.

        Returns
        -------
        pandas.DataFrame
            Single‑row DataFrame with the percentage columns expected by
            the legacy code‑base.
        """
        # Resolve every sub‑score -------------------------------------------------
        porosity_sc = _regex_score(answers.get("hair_behaviour"), _POROSITY)
        issue_sc = _regex_score(answers.get("issue"), _ISSUE)
        hair_overall_sc = _regex_score(answers.get("hair_overall"), _HAIR_OVERALL)
        daily_water_sc = _regex_score(answers.get("daily_water"), _DAILY_WATER)
        hair_feels_sc = _regex_score(answers.get("hair_feels"), _HAIR_FEELS)

        treatment_sc = _regex_score(answers.get("treatments"), _BINARY_ONE)
        enhancers_sc = _regex_score(answers.get("enhancers"), _BINARY_ONE)
        style_sc = _regex_score(answers.get("styling_hair"), _BINARY_ONE)
        split_ends_sc = _regex_score(answers.get("split_ends"), {r"yes": 1, r"no": 0})
        heat_freq_sc = _regex_score(answers.get("heat_freq"), _HEAT_FREQ)

        scalp_feeling_sc = _regex_score(answers.get("scalp_feeling"), _SCALP_FEELING)
        scalp_flaky_sc = _regex_score(answers.get("scalp_flaky"), _THREE_SCALE)
        scalp_oily_sc = _regex_score(
            answers.get("scalp_oily"),
            {r"1 - 2 days": 3, r"3 - 4 days": 2, r"5": 1, r"my scalp gets dry": 0},
        )
        scalp_dry_sc = _regex_score(
            answers.get("scalp_dry"),
            {
                r"within hours": 3,
                r"1 - 2 days": 2,
                r"3 - 5 days": 1,
                r"my scalp gets oily": 0,
            },
        )

        # Totals ------------------------------------------------------------------
        total1 = (
            issue_sc
            + hair_overall_sc
            + daily_water_sc
            + hair_feels_sc
            + scalp_flaky_sc
            + split_ends_sc
            + scalp_dry_sc
        )
        total2 = (
            treatment_sc
            + enhancers_sc
            + porosity_sc
            + style_sc
            + heat_freq_sc
            + split_ends_sc
        )
        total3 = scalp_feeling_sc
        total4 = scalp_oily_sc
        total5 = scalp_dry_sc
        total6 = scalp_flaky_sc

        # Percentages -------------------------------------------------------------
        pct_dry1 = round(total1 * 100 / self._MAX["Dry1"], 1)
        pct_dam = round(total2 * 100 / self._MAX["Dam"], 1)
        pct_sen = round(total3 * 100 / self._MAX["SEN"], 1)
        pct_oil = round(total4 * 100 / self._MAX["Oil"], 1)
        pct_dsc = round(total5 * 100 / self._MAX["DSC"], 1)
        pct_fl = round(total6 * 100 / self._MAX["FL"], 1)

        df = pd.DataFrame(
            [
                {
                    "customer_id": answers.get("customer_id"),
                    "Percentage_Dry1": pct_dry1,
                    "Percentage_Dam": pct_dam,
                    "Percentage_SEN": pct_sen,
                    "Percentage_Oil": pct_oil,
                    "Percentage_DSC": pct_dsc,
                    "Percentage_FL": pct_fl,
                }
            ]
        )
        return df
