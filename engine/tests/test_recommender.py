import pytest

from recommendation.recommender import Recommender


class StubScorer:
    """Fake ConditionScores that returns a deterministic DataFrame."""

    def __init__(self):
        import pandas as pd

        self._df = pd.DataFrame(
            [
                {
                    "Percentage_Dry1": 0.1,
                    "Percentage_Dam": 0.2,
                    "Percentage_SEN": 0.3,  # highest
                    "Percentage_Oil": 0.05,
                }
            ]
        )

    def get_condition_scores(self, user):
        return self._df


@pytest.fixture
def dummy_workbook(monkeypatch, tmp_path):
    """
    Create a fake Excel workbook on disk and monkeypatch *openpyxl* so that
    :pyfunc:`~recommendation_engine.data_access.load_workbook_readonly`
    returns it.  We do **not** require a real XLSX file because our data‑
    access layer only cares about the sheet.values iterator.
    """
    from recommendation_engine import data_access as _da

    class DummySheet:
        def __init__(self, rows):
            self.values = rows

    class DummyWorkbook(dict):
        def __getitem__(self, item):
            return super().__getitem__(item)

    wb = DummyWorkbook(
        {
            "Shampoo": DummySheet(
                [("Product",), ("Percentage_SEN Shampoo 1",), ("Other",)]
            ),
            "Conditioners": DummySheet(
                [("Product",), ("Percentage_SEN Conditioner",), ("Other",)]
            ),
        }
    )

    def fake_load_workbook_readonly(path):
        return wb

    monkeypatch.setattr(_da, "load_workbook_readonly", fake_load_workbook_readonly)
    return wb


def test_recommender_picks_matching_products(dummy_workbook):
    reco = Recommender(
        product_file="dummy.xlsx",
        dict_file="dummy2.xlsx",
        condition_scores_cls=StubScorer,
    )
    shampoos, conditioners = reco.recommend(user="irrelevant")
    assert shampoos == ["Percentage_SEN Shampoo 1", "Other"]
    assert conditioners == ["Percentage_SEN Conditioner", "Other"]
