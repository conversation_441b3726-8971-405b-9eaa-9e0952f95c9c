# Users API Unit Testing Documentation

## 🎯 Overview

This document describes the comprehensive unit testing suite for the Users API, designed to achieve 100% code coverage using pytest and pytest-cov.

## 📁 Test Structure

```
tests/users/
├── __init__.py                 # Test package initialization
├── conftest.py                 # Pytest fixtures and configuration
├── test_models.py              # Model tests (User, UserProfile, UserImage)
├── test_api_endpoints.py       # API endpoint tests
├── test_schemas.py             # Pydantic schema tests
├── test_backends.py            # Authentication backend tests
└── test_utils.py               # Utility functions and edge cases
```

## 🧪 Test Categories

### 1. Model Tests (`test_models.py`)
- **UserManager**: Custom user manager functionality
- **User Model**: User creation, validation, and properties
- **UserProfile Model**: Profile management and relationships
- **UserImage Model**: Image handling and file uploads
- **Model Signals**: Signal handlers and auto-creation
- **Database Constraints**: Unique constraints and cascading deletes

### 2. API Endpoint Tests (`test_api_endpoints.py`)
- **Authentication Endpoints**: Login, logout, signup, register
- **Password Reset**: Token validation and password updates
- **User CRUD**: Create, read, update, delete operations
- **Profile Management**: Profile creation and management
- **Recommendations**: User recommendation handling
- **Error Handling**: Invalid requests and edge cases

### 3. Schema Tests (`test_schemas.py`)
- **Pydantic Schemas**: Data validation and serialization
- **Input Validation**: Required fields and data types
- **Output Formatting**: Response schema validation
- **Edge Cases**: Invalid data and boundary conditions

### 4. Backend Tests (`test_backends.py`)
- **EmailBackend**: Email-based authentication
- **User Retrieval**: User lookup by ID and email
- **Password Validation**: Authentication logic
- **Error Handling**: Database errors and edge cases

### 5. Utility Tests (`test_utils.py`)
- **API Utilities**: Helper functions and decorators
- **Data Validation**: Input sanitization and validation
- **Concurrency**: Race conditions and concurrent operations
- **Performance**: Memory usage and large data handling

## 🔧 Test Configuration

### Pytest Configuration (`pytest.ini`)
```ini
[tool:pytest]
DJANGO_SETTINGS_MODULE = cosmetrics_ai.settings.testing
python_files = tests.py test_*.py *_tests.py
python_classes = Test*
python_functions = test_*
testpaths = tests
addopts =
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --cov=users
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=100
    --cov-branch
    --reuse-db
    --nomigrations
```

### Test Fixtures (`conftest.py`)
- **Database Fixtures**: User, UserProfile, UserImage instances
- **API Client**: Authenticated and unauthenticated clients
- **Mock Objects**: External dependencies and services
- **Test Data**: Sample data for various test scenarios

## 🚀 Running Tests

### Local Development

#### Using the Test Runner Script
```bash
# Run all tests with coverage
python scripts/run_users_tests.py

# Run specific test categories
python scripts/run_users_tests.py --models
python scripts/run_users_tests.py --api
python scripts/run_users_tests.py --schemas

# Generate HTML coverage report
python scripts/run_users_tests.py --html-coverage --open-coverage

# Run tests in parallel
python scripts/run_users_tests.py --parallel

# Run only fast tests (exclude slow)
python scripts/run_users_tests.py --fast
```

#### Direct Pytest Commands
```bash
# Run all users tests
pytest tests/users/ --cov=users --cov-report=html

# Run specific test file
pytest tests/users/test_models.py -v

# Run with specific markers
pytest tests/users/ -m "models"
pytest tests/users/ -m "api"
pytest tests/users/ -m "not slow"

# Generate coverage reports
pytest tests/users/ --cov=users --cov-report=html:htmlcov --cov-report=xml
```

### GitHub Actions

The workflow automatically runs on:
- Push to `main` or `develop` branches
- Pull requests to `main` or `develop` branches
- Manual workflow dispatch

#### Test Matrix
- **Python Versions**: 3.9, 3.10, 3.11
- **Django Versions**: 4.2, 5.0
- **Databases**: PostgreSQL 15
- **Cache**: Redis 7

## 📊 Coverage Requirements

### Target: 100% Code Coverage
- **Line Coverage**: 100%
- **Branch Coverage**: 100%
- **Function Coverage**: 100%
- **Class Coverage**: 100%

### Coverage Reports
- **HTML Report**: `htmlcov/index.html`
- **XML Report**: `coverage.xml`
- **Terminal Report**: Real-time coverage display
- **Badge**: Auto-generated coverage badge

## 🧩 Test Fixtures and Factories

### User Fixtures
```python
@pytest.fixture
def user(user_data):
    """Create a test user."""
    return User.objects.create_user(**user_data)

@pytest.fixture
def superuser():
    """Create a test superuser."""
    return User.objects.create_superuser(
        email="<EMAIL>",
        password="adminpass123"
    )
```

### Profile Fixtures
```python
@pytest.fixture
def user_profile(user, user_profile_data):
    """Create a test user profile."""
    return UserProfile.objects.create(user=user, **user_profile_data)
```

### API Client Fixtures
```python
@pytest.fixture
def authenticated_client(api_client, user):
    """Provide an authenticated client."""
    api_client.force_login(user)
    return api_client
```

## 🔍 Test Markers

### Available Markers
- `@pytest.mark.models` - Model-related tests
- `@pytest.mark.api` - API endpoint tests
- `@pytest.mark.schemas` - Schema validation tests
- `@pytest.mark.backends` - Authentication backend tests
- `@pytest.mark.utils` - Utility function tests
- `@pytest.mark.slow` - Slow-running tests
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.unit` - Unit tests

### Usage Examples
```python
@pytest.mark.models
@pytest.mark.django_db
class TestUserModel:
    def test_user_creation(self):
        # Test implementation
        pass

@pytest.mark.api
@pytest.mark.django_db
class TestUserAPI:
    def test_user_registration(self):
        # Test implementation
        pass
```

## 🛡️ Security Testing

### Security Checks Included
- **Bandit**: Static security analysis
- **Safety**: Dependency vulnerability scanning
- **Input Validation**: SQL injection and XSS prevention
- **Authentication**: Secure password handling
- **Authorization**: Access control validation

## 📈 Performance Testing

### Performance Benchmarks
- **API Response Times**: < 100ms for simple operations
- **Database Queries**: Optimized query counts
- **Memory Usage**: Memory leak detection
- **Concurrent Operations**: Race condition testing

## 🔄 Continuous Integration

### GitHub Actions Workflow
1. **Setup**: Python, PostgreSQL, Redis
2. **Dependencies**: Install requirements
3. **Database**: Create and migrate test database
4. **Tests**: Run full test suite with coverage
5. **Reports**: Generate and upload coverage reports
6. **Security**: Run security scans
7. **Performance**: Benchmark critical operations
8. **Notifications**: Report results

### Quality Gates
- ✅ **100% Test Coverage** - All code must be tested
- ✅ **All Tests Pass** - No failing tests allowed
- ✅ **Security Scan** - No high-severity vulnerabilities
- ✅ **Performance** - Response times within limits
- ✅ **Code Quality** - Linting and formatting checks

## 📝 Test Data Management

### Test Database
- **Isolation**: Each test runs in a transaction
- **Cleanup**: Automatic rollback after each test
- **Fixtures**: Reusable test data
- **Factories**: Dynamic test data generation

### Mock Objects
- **External APIs**: Mock third-party services
- **Email Backend**: Mock email sending
- **File Storage**: Mock file operations
- **Time**: Mock datetime for consistent testing

## 🐛 Debugging Tests

### Common Issues and Solutions

#### Test Database Issues
```bash
# Reset test database
python manage.py migrate --run-syncdb

# Check migration status
python manage.py showmigrations
```

#### Coverage Issues
```bash
# Run with coverage debug
pytest --cov=users --cov-report=term-missing --cov-debug=trace

# Check uncovered lines
coverage report --show-missing
```

#### Fixture Issues
```bash
# List available fixtures
pytest --fixtures tests/users/

# Debug fixture scope
pytest --setup-show tests/users/test_models.py
```

## 📚 Best Practices

### Test Writing Guidelines
1. **Descriptive Names**: Use clear, descriptive test names
2. **Single Responsibility**: One assertion per test when possible
3. **Arrange-Act-Assert**: Follow AAA pattern
4. **Independent Tests**: Tests should not depend on each other
5. **Edge Cases**: Test boundary conditions and error cases

### Coverage Guidelines
1. **Line Coverage**: Test every line of code
2. **Branch Coverage**: Test all conditional branches
3. **Exception Handling**: Test error conditions
4. **Edge Cases**: Test boundary values and invalid inputs
5. **Integration Points**: Test interactions between components

### Performance Guidelines
1. **Fast Tests**: Keep unit tests under 100ms
2. **Database Efficiency**: Minimize database queries
3. **Memory Management**: Avoid memory leaks in tests
4. **Parallel Execution**: Design tests for parallel running
5. **Resource Cleanup**: Properly clean up test resources

## 🎯 Success Metrics

### Test Quality Metrics
- **Coverage**: 100% line and branch coverage
- **Speed**: < 30 seconds for full test suite
- **Reliability**: 0% flaky tests
- **Maintainability**: Clear, readable test code
- **Documentation**: Comprehensive test documentation

### CI/CD Metrics
- **Build Success Rate**: > 99%
- **Test Execution Time**: < 5 minutes
- **Feedback Time**: < 10 minutes from commit to result
- **Security Scan**: 0 high-severity vulnerabilities
- **Performance**: All benchmarks within limits

---

**The Users API testing suite provides comprehensive coverage and ensures high-quality, reliable code through automated testing and continuous integration!** 🧪✨
