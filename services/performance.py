"""
Performance optimization service for Cosmetrics AI.

This service provides utilities for optimizing database queries, caching,
and monitoring performance metrics.
"""

import functools
import time
from typing import Any, Dict, List, Optional, Type

from django.conf import settings
from django.core.cache import cache
from django.db import models
from django.db.models import Prefetch, QuerySet
from loguru import logger


class QueryOptimizer:
    """
    Utility class for optimizing database queries.
    """

    @staticmethod
    def optimize_user_queries(queryset: QuerySet) -> QuerySet:
        """Optimize User model queries with proper select_related and prefetch_related."""
        return queryset.select_related().prefetch_related(
            "userprofile", "userprofile__recommendations", "userimage_set"
        )

    @staticmethod
    def optimize_product_queries(queryset: QuerySet) -> QuerySet:
        """Optimize Product model queries with proper select_related and prefetch_related."""
        return queryset.select_related("category").prefetch_related(
            "recommended_users", "recommended_users__userprofile"
        )

    @staticmethod
    def optimize_recommendation_queries(queryset: QuerySet) -> QuerySet:
        """Optimize Recommendation model queries."""
        return queryset.select_related("user", "user__userprofile").prefetch_related(
            "products", "products__category"
        )

    @staticmethod
    def optimize_userprofile_queries(queryset: QuerySet) -> QuerySet:
        """Optimize UserProfile model queries."""
        return queryset.select_related("user").prefetch_related(
            "recommendations", "recommendations__category"
        )


class CacheManager:
    """
    Centralized cache management for the application.
    """

    # Cache key prefixes
    USER_PREFIX = "user"
    PRODUCT_PREFIX = "product"
    RECOMMENDATION_PREFIX = "recommendation"
    API_PREFIX = "api"

    # Cache timeouts (in seconds)
    SHORT_TIMEOUT = 300  # 5 minutes
    MEDIUM_TIMEOUT = 1800  # 30 minutes
    LONG_TIMEOUT = 3600  # 1 hour
    VERY_LONG_TIMEOUT = 86400  # 24 hours

    @classmethod
    def get_cache_key(cls, prefix: str, identifier: str, *args) -> str:
        """Generate a standardized cache key."""
        parts = [prefix, str(identifier)] + [str(arg) for arg in args]
        return ":".join(parts)

    @classmethod
    def cache_user_data(
        cls, user_id: int, data: Any, timeout: int = MEDIUM_TIMEOUT
    ) -> None:
        """Cache user-related data."""
        key = cls.get_cache_key(cls.USER_PREFIX, user_id)
        cache.set(key, data, timeout)
        logger.debug(f"Cached user data for user {user_id}")

    @classmethod
    def get_user_data(cls, user_id: int) -> Optional[Any]:
        """Retrieve cached user data."""
        key = cls.get_cache_key(cls.USER_PREFIX, user_id)
        data = cache.get(key)
        if data:
            logger.debug(f"Cache hit for user {user_id}")
        return data

    @classmethod
    def cache_product_list(
        cls, filters: Dict[str, Any], data: Any, timeout: int = LONG_TIMEOUT
    ) -> None:
        """Cache product list with filters."""
        filter_key = "_".join(f"{k}_{v}" for k, v in sorted(filters.items()))
        key = cls.get_cache_key(cls.PRODUCT_PREFIX, "list", filter_key)
        cache.set(key, data, timeout)
        logger.debug(f"Cached product list with filters: {filters}")

    @classmethod
    def get_product_list(cls, filters: Dict[str, Any]) -> Optional[Any]:
        """Retrieve cached product list."""
        filter_key = "_".join(f"{k}_{v}" for k, v in sorted(filters.items()))
        key = cls.get_cache_key(cls.PRODUCT_PREFIX, "list", filter_key)
        data = cache.get(key)
        if data:
            logger.debug(f"Cache hit for product list with filters: {filters}")
        return data

    @classmethod
    def cache_recommendations(
        cls, user_id: int, data: Any, timeout: int = MEDIUM_TIMEOUT
    ) -> None:
        """Cache user recommendations."""
        key = cls.get_cache_key(cls.RECOMMENDATION_PREFIX, user_id)
        cache.set(key, data, timeout)
        logger.debug(f"Cached recommendations for user {user_id}")

    @classmethod
    def get_recommendations(cls, user_id: int) -> Optional[Any]:
        """Retrieve cached recommendations."""
        key = cls.get_cache_key(cls.RECOMMENDATION_PREFIX, user_id)
        data = cache.get(key)
        if data:
            logger.debug(f"Cache hit for recommendations for user {user_id}")
        return data

    @classmethod
    def invalidate_user_cache(cls, user_id: int) -> None:
        """Invalidate all cache entries for a user."""
        patterns = [
            cls.get_cache_key(cls.USER_PREFIX, user_id),
            cls.get_cache_key(cls.RECOMMENDATION_PREFIX, user_id),
        ]
        for pattern in patterns:
            cache.delete(pattern)
        logger.debug(f"Invalidated cache for user {user_id}")


def cache_result(timeout: int = CacheManager.MEDIUM_TIMEOUT, key_prefix: str = "func"):
    """
    Decorator to cache function results.

    Args:
        timeout: Cache timeout in seconds
        key_prefix: Prefix for cache key
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key from function name and arguments
            cache_key = CacheManager.get_cache_key(
                key_prefix,
                func.__name__,
                str(hash(str(args) + str(sorted(kwargs.items())))),
            )

            # Try to get from cache
            result = cache.get(cache_key)
            if result is not None:
                logger.debug(f"Cache hit for {func.__name__}")
                return result

            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            logger.debug(f"Cached result for {func.__name__}")

            return result

        return wrapper

    return decorator


def monitor_performance(func):
    """
    Decorator to monitor function performance.
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()

        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time

            # Log performance
            logger.debug(f"Function {func.__name__} executed in {execution_time:.3f}s")

            # Log warning for slow functions
            if execution_time > 1.0:
                logger.warning(
                    f"Slow function: {func.__name__} took {execution_time:.3f}s"
                )

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(
                f"Function {func.__name__} failed after {execution_time:.3f}s: {e}"
            )
            raise

    return wrapper


class PerformanceMetrics:
    """
    Utility class for collecting and analyzing performance metrics.
    """

    @staticmethod
    def get_endpoint_metrics(endpoint: str) -> Dict[str, Any]:
        """Get performance metrics for a specific endpoint."""
        cache_key = f"perf:GET:{endpoint.replace('/', '_')}"
        metrics = cache.get(cache_key, {})

        if metrics:
            # Calculate averages
            total_requests = metrics.get("total_requests", 0)
            if total_requests > 0:
                metrics["avg_time"] = metrics.get("total_time", 0) / total_requests
                metrics["avg_queries"] = (
                    metrics.get("total_queries", 0) / total_requests
                )
                metrics["error_rate"] = (
                    metrics.get("error_count", 0) / total_requests * 100
                )

        return metrics

    @staticmethod
    def get_all_metrics() -> Dict[str, Dict[str, Any]]:
        """Get all performance metrics."""
        # This is a simplified version - in production you'd want to use
        # a more sophisticated approach to get all cache keys
        endpoints = [
            "/api/users/",
            "/api/products/",
            "/api/recommendations/",
            "/api/quiz/",
        ]

        metrics = {}
        for endpoint in endpoints:
            endpoint_metrics = PerformanceMetrics.get_endpoint_metrics(endpoint)
            if endpoint_metrics:
                metrics[endpoint] = endpoint_metrics

        return metrics

    @staticmethod
    def clear_metrics() -> None:
        """Clear all performance metrics."""
        # In a real implementation, you'd want to be more selective
        # This is a simplified version
        logger.info("Performance metrics cleared")


# Optimized query functions for common use cases
@cache_result(timeout=CacheManager.LONG_TIMEOUT, key_prefix="products")
@monitor_performance
def get_optimized_products(
    filters: Optional[Dict[str, Any]] = None,
) -> List[Dict[str, Any]]:
    """Get optimized product list with caching."""
    from products.models import Product

    queryset = Product.objects.all()

    # Apply filters if provided
    if filters:
        if "category" in filters:
            queryset = queryset.filter(category_id=filters["category"])
        if "hairtype" in filters:
            queryset = queryset.filter(hairtype=filters["hairtype"])
        if "price_min" in filters:
            queryset = queryset.filter(price__gte=filters["price_min"])
        if "price_max" in filters:
            queryset = queryset.filter(price__lte=filters["price_max"])

    # Optimize the query
    queryset = QueryOptimizer.optimize_product_queries(queryset)

    # Convert to list of dicts for serialization
    products = []
    for product in queryset:
        products.append(
            {
                "id": product.id,
                "name": product.name,
                "price": float(product.price),
                "category": product.category.name if product.category else None,
                "hairtype": product.hairtype,
                "porosity": product.porosity,
                "texture": product.texture,
            }
        )

    return products


@cache_result(timeout=CacheManager.MEDIUM_TIMEOUT, key_prefix="user_recommendations")
@monitor_performance
def get_user_recommendations_optimized(user_id: int) -> List[Dict[str, Any]]:
    """Get optimized user recommendations with caching."""
    from recommendation.models import Recommendation

    queryset = Recommendation.objects.filter(user_id=user_id)
    queryset = QueryOptimizer.optimize_recommendation_queries(queryset)

    recommendations = []
    for rec in queryset:
        recommendations.append(
            {
                "id": rec.id,
                "session_guid": rec.session_guid,
                "products": [
                    {
                        "id": product.id,
                        "name": product.name,
                        "price": float(product.price),
                        "category": product.category.name if product.category else None,
                    }
                    for product in rec.products.all()
                ],
            }
        )

    return recommendations
