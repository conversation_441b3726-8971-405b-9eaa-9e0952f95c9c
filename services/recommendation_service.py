"""
Recommendation service for Cosmetrics AI.

This service handles all recommendation-related business logic,
providing a clean interface for generating and managing product recommendations.
"""

from typing import Any

from django.contrib.auth.models import User
from django.db.models import QuerySet

from helpers.performance import QueryOptimizer
from products.models import Product
from questionaire.models import Reply
from recommendation.models import Recommendation

from .base import ComprehensiveService, ServiceResult, service_operation
from .events import EventTypes


class RecommendationService(ComprehensiveService):
    """
    Service for managing product recommendations.

    Handles recommendation generation, caching, and related business logic.
    """

    @service_operation("generate_recommendations")
    def generate_recommendations(
        self, user_id: int, session_guid: str
    ) -> ServiceResult[dict[str, Any]]:
        """
        Generate product recommendations for a user session.

        Args:
            user_id: The user ID
            session_guid: The session GUID

        Returns:
            ServiceResult containing recommendation data
        """
        # Check cache first
        cache_key = self.get_cache_key("recommendations", user_id, session_guid)
        cached_result = self.get_cached_result(cache_key)

        if cached_result:
            self.logger.info(f"Returning cached recommendations for user {user_id}")
            return ServiceResult.success(data=cached_result)

        # Validate inputs
        validation_result = self.validate_input(
            {"user_id": user_id, "session_guid": session_guid},
            ["user_id", "session_guid"],
        )
        if validation_result.is_error:
            return validation_result

        try:
            # Get user and validate existence
            user = User.objects.get(id=user_id)

            # Get user's questionnaire responses
            replies = self._get_user_replies(user_id, session_guid)
            if not replies:
                return ServiceResult.error(
                    "No questionnaire responses found for this session"
                )

            # Generate recommendations based on replies
            recommendations = self._generate_product_recommendations(replies)

            # Save recommendations to database
            recommendation_obj = self._save_recommendations(
                user, session_guid, recommendations
            )

            # Cache the result
            self.cache_result(cache_key, recommendations, timeout=600)  # 10 minutes

            # Emit event
            self.emit_event(
                EventTypes.RECOMMENDATION_GENERATED,
                data=recommendations,
                entity_id=str(user_id),
            )

            return ServiceResult.success(
                data=recommendations,
                message="Recommendations generated successfully",
                metadata={
                    "recommendation_id": recommendation_obj.id,
                    "session_guid": session_guid,
                    "total_products": len(recommendations.get("shampoos", []))
                    + len(recommendations.get("conditioners", [])),
                },
            )

        except User.DoesNotExist:
            return ServiceResult.not_found("User not found")
        except Exception as e:
            return self.handle_exception(e, "generate_recommendations")

    @service_operation("get_recommendations")
    def get_recommendations(
        self, user_id: int, session_guid: str
    ) -> ServiceResult[dict[str, Any]]:
        """
        Get existing recommendations for a user session.

        Args:
            user_id: The user ID
            session_guid: The session GUID

        Returns:
            ServiceResult containing recommendation data
        """
        try:
            recommendation = Recommendation.objects.select_related("user").get(
                user_id=user_id, session_guid=session_guid
            )

            recommendations_data = {
                "shampoos": recommendation.shampoos,
                "conditioners": recommendation.conditioners,
                "created_at": recommendation.created_at.isoformat(),
                "updated_at": recommendation.updated_at.isoformat(),
            }

            return ServiceResult.success(
                data=recommendations_data,
                message="Recommendations retrieved successfully",
            )

        except Recommendation.DoesNotExist:
            return ServiceResult.not_found("No recommendations found for this session")
        except Exception as e:
            return self.handle_exception(e, "get_recommendations")

    @service_operation("get_user_recommendation_history")
    def get_user_recommendation_history(
        self, user_id: int, limit: int = 10
    ) -> ServiceResult[list[dict[str, Any]]]:
        """
        Get recommendation history for a user.

        Args:
            user_id: The user ID
            limit: Maximum number of recommendations to return

        Returns:
            ServiceResult containing list of recommendations
        """
        try:
            recommendations = QueryOptimizer.optimize_recommendation_queries(
                Recommendation.objects.filter(user_id=user_id)
            ).order_by("-created_at")[:limit]

            history_data = []
            for rec in recommendations:
                history_data.append(
                    {
                        "id": rec.id,
                        "session_guid": rec.session_guid,
                        "shampoos": rec.shampoos,
                        "conditioners": rec.conditioners,
                        "created_at": rec.created_at.isoformat(),
                        "product_count": len(rec.shampoos) + len(rec.conditioners),
                    }
                )

            return ServiceResult.success(
                data=history_data,
                message=f"Retrieved {len(history_data)} recommendations",
            )

        except Exception as e:
            return self.handle_exception(e, "get_user_recommendation_history")

    def _get_user_replies(self, user_id: int, session_guid: str) -> QuerySet[Reply]:
        """Get optimized user replies for a session."""
        return QueryOptimizer.optimize_reply_queries(
            Reply.objects.filter(user_id=user_id, session_guid=session_guid)
        )

    def _generate_product_recommendations(
        self, replies: QuerySet[Reply]
    ) -> dict[str, list[str]]:
        """
        Generate product recommendations based on user replies.

        This is a simplified version. In production, this would use
        a more sophisticated ML algorithm.
        """
        # Calculate user profile scores
        scores = self._calculate_user_scores(replies)

        # Get matching products
        shampoos = self._get_matching_products("shampoo", scores)
        conditioners = self._get_matching_products("conditioner", scores)

        return {
            "shampoos": [product.name for product in shampoos[:5]],  # Top 5
            "conditioners": [product.name for product in conditioners[:5]],  # Top 5
            "scores": scores,
        }

    def _calculate_user_scores(self, replies: QuerySet[Reply]) -> dict[str, float]:
        """Calculate user profile scores from questionnaire replies."""
        scores = {
            "dry_score": 0.0,
            "damage_score": 0.0,
            "sensitivity_score": 0.0,
            "oily_score": 0.0,
        }

        total_replies = replies.count()
        if total_replies == 0:
            return scores

        for reply in replies:
            if reply.answer and reply.answer.score:
                # Simplified scoring logic
                question_title = reply.question.title.lower()
                score_value = float(reply.answer.score)

                if "dry" in question_title:
                    scores["dry_score"] += score_value
                elif "damage" in question_title:
                    scores["damage_score"] += score_value
                elif "sensitive" in question_title:
                    scores["sensitivity_score"] += score_value
                elif "oily" in question_title:
                    scores["oily_score"] += score_value

        # Normalize scores
        for key in scores:
            scores[key] = scores[key] / total_replies if total_replies > 0 else 0.0

        return scores

    def _get_matching_products(
        self, product_type: str, scores: dict[str, float]
    ) -> QuerySet[Product]:
        """Get products matching the user's profile scores."""
        # This is a simplified matching algorithm
        # In production, this would use more sophisticated ML/AI

        products = Product.objects.all()

        # Filter by product type if we had that field
        # For now, we'll use a simple scoring mechanism

        # Return top products (simplified)
        return products[:10]  # Return top 10 for now

    def _save_recommendations(
        self, user: User, session_guid: str, recommendations: dict[str, Any]
    ) -> Recommendation:
        """Save recommendations to database."""
        recommendation, created = Recommendation.objects.update_or_create(
            user=user,
            session_guid=session_guid,
            defaults={
                "shampoos": recommendations.get("shampoos", []),
                "conditioners": recommendations.get("conditioners", []),
            },
        )

        if created:
            self.logger.info(f"Created new recommendation for user {user.id}")
        else:
            self.logger.info(f"Updated existing recommendation for user {user.id}")

        return recommendation

    @service_operation("invalidate_user_recommendations")
    def invalidate_user_recommendations(self, user_id: int) -> ServiceResult[None]:
        """
        Invalidate cached recommendations for a user.

        Args:
            user_id: The user ID

        Returns:
            ServiceResult indicating success or failure
        """
        try:
            # Invalidate cache
            self.invalidate_cache(f"*recommendations*{user_id}*")

            self.logger.info(f"Invalidated recommendations cache for user {user_id}")

            return ServiceResult.success(
                message="Recommendations cache invalidated successfully"
            )

        except Exception as e:
            return self.handle_exception(e, "invalidate_user_recommendations")

    @service_operation("get_recommendation_analytics")
    def get_recommendation_analytics(
        self, days: int = 30
    ) -> ServiceResult[dict[str, Any]]:
        """
        Get analytics data for recommendations.

        Args:
            days: Number of days to analyze

        Returns:
            ServiceResult containing analytics data
        """
        try:
            from datetime import datetime, timedelta

            from django.db.models import Count

            start_date = datetime.now() - timedelta(days=days)

            # Get recommendation statistics
            total_recommendations = Recommendation.objects.filter(
                created_at__gte=start_date
            ).count()

            unique_users = (
                Recommendation.objects.filter(created_at__gte=start_date)
                .values("user")
                .distinct()
                .count()
            )

            # Get daily breakdown
            daily_stats = (
                Recommendation.objects.filter(created_at__gte=start_date)
                .extra(select={"day": "date(created_at)"})
                .values("day")
                .annotate(count=Count("id"))
                .order_by("day")
            )

            analytics_data = {
                "total_recommendations": total_recommendations,
                "unique_users": unique_users,
                "average_per_user": total_recommendations / unique_users
                if unique_users > 0
                else 0,
                "daily_breakdown": list(daily_stats),
                "period_days": days,
            }

            return ServiceResult.success(
                data=analytics_data, message="Analytics data retrieved successfully"
            )

        except Exception as e:
            return self.handle_exception(e, "get_recommendation_analytics")
