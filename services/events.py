"""
Event-driven architecture implementation for Cosmetrics AI.

This module provides event bus functionality, enabling loose coupling
between services and preparing for microservices architecture.
"""

import asyncio
import uuid
from collections.abc import Callable
from dataclasses import asdict, dataclass
from datetime import datetime
from enum import Enum
from typing import Any

from django.conf import settings
from django.core.cache import cache
from loguru import logger


class EventPriority(Enum):
    """Event priority levels."""

    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class Event:
    """
    Event data structure for the event bus.

    Represents a domain event that occurred in the system.
    """

    event_type: str
    data: dict[str, Any]
    event_id: str = None
    timestamp: datetime = None
    source_service: str = None
    entity_id: str | None = None
    priority: EventPriority = EventPriority.NORMAL
    correlation_id: str | None = None
    metadata: dict[str, Any] | None = None

    def __post_init__(self):
        if self.event_id is None:
            self.event_id = str(uuid.uuid4())
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
        if self.metadata is None:
            self.metadata = {}

    def to_dict(self) -> dict[str, Any]:
        """Convert event to dictionary for serialization."""
        data = asdict(self)
        data["timestamp"] = self.timestamp.isoformat()
        data["priority"] = self.priority.value
        return data

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "Event":
        """Create event from dictionary."""
        data = data.copy()
        data["timestamp"] = datetime.fromisoformat(data["timestamp"])
        data["priority"] = EventPriority(data["priority"])
        return cls(**data)


class EventHandler:
    """
    Base class for event handlers.

    Event handlers process specific types of events.
    """

    def __init__(self, event_types: list[str], handler_func: Callable[[Event], None]):
        self.event_types = set(event_types)
        self.handler_func = handler_func
        self.handler_id = str(uuid.uuid4())

    def can_handle(self, event: Event) -> bool:
        """Check if this handler can process the given event."""
        return event.event_type in self.event_types

    async def handle(self, event: Event) -> None:
        """Handle the event asynchronously."""
        try:
            if asyncio.iscoroutinefunction(self.handler_func):
                await self.handler_func(event)
            else:
                self.handler_func(event)
        except Exception as e:
            logger.error(f"Error in event handler {self.handler_id}: {e}")


class EventBus:
    """
    Event bus implementation for publish-subscribe pattern.

    Enables loose coupling between services through events.
    """

    def __init__(self):
        self._handlers: list[EventHandler] = []
        self._event_store: list[Event] = []
        self._max_stored_events = getattr(settings, "MAX_STORED_EVENTS", 1000)

    def subscribe(
        self, event_types: list[str], handler_func: Callable[[Event], None]
    ) -> str:
        """
        Subscribe to specific event types.

        Returns the handler ID for unsubscribing.
        """
        handler = EventHandler(event_types, handler_func)
        self._handlers.append(handler)
        logger.info(f"Subscribed handler {handler.handler_id} to events: {event_types}")
        return handler.handler_id

    def unsubscribe(self, handler_id: str) -> bool:
        """Unsubscribe a handler by ID."""
        for i, handler in enumerate(self._handlers):
            if handler.handler_id == handler_id:
                del self._handlers[i]
                logger.info(f"Unsubscribed handler {handler_id}")
                return True
        return False

    def emit(self, event: Event) -> None:
        """
        Emit an event to all subscribed handlers.

        Processes handlers synchronously for now, but can be made async.
        """
        logger.info(f"Emitting event: {event.event_type} (ID: {event.event_id})")

        # Store event for replay/debugging
        self._store_event(event)

        # Process handlers
        matching_handlers = [h for h in self._handlers if h.can_handle(event)]

        if not matching_handlers:
            logger.warning(f"No handlers found for event type: {event.event_type}")
            return

        for handler in matching_handlers:
            try:
                # For now, run synchronously. In production, use async or Celery
                if asyncio.iscoroutinefunction(handler.handler_func):
                    # Run async handler in sync context
                    asyncio.create_task(handler.handle(event))
                else:
                    handler.handler_func(event)

                logger.debug(
                    f"Event {event.event_id} processed by handler {handler.handler_id}"
                )
            except Exception as e:
                logger.error(
                    f"Handler {handler.handler_id} failed to process event {event.event_id}: {e}"
                )

    def _store_event(self, event: Event) -> None:
        """Store event for replay and debugging."""
        self._event_store.append(event)

        # Keep only the most recent events
        if len(self._event_store) > self._max_stored_events:
            self._event_store = self._event_store[-self._max_stored_events :]

    def get_events(self, event_type: str = None, limit: int = 100) -> list[Event]:
        """Get stored events, optionally filtered by type."""
        events = self._event_store

        if event_type:
            events = [e for e in events if e.event_type == event_type]

        return events[-limit:]

    def replay_events(
        self, event_type: str = None, from_timestamp: datetime = None
    ) -> None:
        """Replay stored events to current handlers."""
        events = self._event_store

        if event_type:
            events = [e for e in events if e.event_type == event_type]

        if from_timestamp:
            events = [e for e in events if e.timestamp >= from_timestamp]

        logger.info(f"Replaying {len(events)} events")

        for event in events:
            self.emit(event)


# Global event bus instance
_event_bus = EventBus()


def get_event_bus() -> EventBus:
    """Get the global event bus instance."""
    return _event_bus


# Event type constants
class EventTypes:
    """Constants for common event types."""

    # User events
    USER_REGISTERED = "user.registered"
    USER_PROFILE_UPDATED = "user.profile_updated"
    USER_DELETED = "user.deleted"

    # Questionnaire events
    QUESTIONNAIRE_STARTED = "questionnaire.started"
    QUESTIONNAIRE_COMPLETED = "questionnaire.completed"
    QUESTION_ANSWERED = "question.answered"

    # Recommendation events
    RECOMMENDATION_GENERATED = "recommendation.generated"
    RECOMMENDATION_VIEWED = "recommendation.viewed"
    RECOMMENDATION_SHARED = "recommendation.shared"

    # Report events
    REPORT_GENERATED = "report.generated"
    REPORT_EMAILED = "report.emailed"
    REPORT_DOWNLOADED = "report.downloaded"

    # System events
    SYSTEM_ERROR = "system.error"
    PERFORMANCE_ALERT = "system.performance_alert"
    SECURITY_ALERT = "system.security_alert"


# Convenience functions for common events
def emit_user_registered(user_id: str, user_data: dict[str, Any]) -> None:
    """Emit user registration event."""
    event = Event(
        event_type=EventTypes.USER_REGISTERED, data=user_data, entity_id=user_id
    )
    get_event_bus().emit(event)


def emit_questionnaire_completed(
    user_id: str, session_guid: str, questionnaire_data: dict[str, Any]
) -> None:
    """Emit questionnaire completion event."""
    event = Event(
        event_type=EventTypes.QUESTIONNAIRE_COMPLETED,
        data=questionnaire_data,
        entity_id=user_id,
        metadata={"session_guid": session_guid},
    )
    get_event_bus().emit(event)


def emit_recommendation_generated(
    user_id: str, session_guid: str, recommendations: dict[str, Any]
) -> None:
    """Emit recommendation generation event."""
    event = Event(
        event_type=EventTypes.RECOMMENDATION_GENERATED,
        data=recommendations,
        entity_id=user_id,
        metadata={"session_guid": session_guid},
    )
    get_event_bus().emit(event)


def emit_report_generated(
    user_id: str, session_guid: str, report_data: dict[str, Any]
) -> None:
    """Emit report generation event."""
    event = Event(
        event_type=EventTypes.REPORT_GENERATED,
        data=report_data,
        entity_id=user_id,
        metadata={"session_guid": session_guid},
    )
    get_event_bus().emit(event)


# Event handlers for common patterns
def log_all_events(event: Event) -> None:
    """Log all events for debugging."""
    logger.info(f"Event logged: {event.event_type} - {event.event_id}")


def cache_user_events(event: Event) -> None:
    """Cache user-related events for analytics."""
    if event.entity_id and event.event_type.startswith("user."):
        cache_key = f"user_events:{event.entity_id}"

        # Get existing events
        existing_events = cache.get(cache_key, [])
        existing_events.append(event.to_dict())

        # Keep only last 50 events per user
        if len(existing_events) > 50:
            existing_events = existing_events[-50:]

        # Cache for 24 hours
        cache.set(cache_key, existing_events, 86400)


# Initialize default event handlers
def setup_default_handlers():
    """Set up default event handlers."""
    event_bus = get_event_bus()

    # Log all events in debug mode
    if settings.DEBUG:
        event_bus.subscribe(["*"], log_all_events)

    # Cache user events for analytics
    user_event_types = [
        EventTypes.USER_REGISTERED,
        EventTypes.USER_PROFILE_UPDATED,
        EventTypes.QUESTIONNAIRE_COMPLETED,
        EventTypes.RECOMMENDATION_GENERATED,
        EventTypes.REPORT_GENERATED,
    ]
    event_bus.subscribe(user_event_types, cache_user_events)


# Call setup when module is imported
setup_default_handlers()
