"""
Base service classes for service-oriented architecture.

This module provides the foundation for all business services in the application,
enabling clean separation of concerns and preparing for microservices architecture.
"""

import abc
import uuid
from dataclasses import dataclass
from enum import Enum
from typing import Any, Generic, TypeVar

from django.core.exceptions import ValidationError
from django.db import transaction
from loguru import logger

from helpers.performance import monitor_performance

T = TypeVar("T")


class ServiceStatus(Enum):
    """Status codes for service operations."""

    SUCCESS = "success"
    ERROR = "error"
    VALIDATION_ERROR = "validation_error"
    NOT_FOUND = "not_found"
    PERMISSION_DENIED = "permission_denied"
    RATE_LIMITED = "rate_limited"


@dataclass
class ServiceResult(Generic[T]):
    """
    Standard result object for service operations.

    Provides consistent return format across all services.
    """

    status: ServiceStatus
    data: T | None = None
    message: str = ""
    errors: dict[str, Any] | None = None
    metadata: dict[str, Any] | None = None

    @property
    def is_success(self) -> bool:
        """Check if the operation was successful."""
        return self.status == ServiceStatus.SUCCESS

    @property
    def is_error(self) -> bool:
        """Check if the operation resulted in an error."""
        return self.status != ServiceStatus.SUCCESS

    @classmethod
    def success(
        cls, data: T = None, message: str = "", metadata: dict[str, Any] = None
    ) -> "ServiceResult[T]":
        """Create a successful result."""
        return cls(
            status=ServiceStatus.SUCCESS,
            data=data,
            message=message,
            metadata=metadata or {},
        )

    @classmethod
    def error(
        cls,
        message: str,
        errors: dict[str, Any] = None,
        status: ServiceStatus = ServiceStatus.ERROR,
    ) -> "ServiceResult[T]":
        """Create an error result."""
        return cls(status=status, message=message, errors=errors or {})

    @classmethod
    def validation_error(
        cls, errors: dict[str, Any], message: str = "Validation failed"
    ) -> "ServiceResult[T]":
        """Create a validation error result."""
        return cls(
            status=ServiceStatus.VALIDATION_ERROR, message=message, errors=errors
        )

    @classmethod
    def not_found(cls, message: str = "Resource not found") -> "ServiceResult[T]":
        """Create a not found result."""
        return cls(status=ServiceStatus.NOT_FOUND, message=message)

    @classmethod
    def permission_denied(
        cls, message: str = "Permission denied"
    ) -> "ServiceResult[T]":
        """Create a permission denied result."""
        return cls(status=ServiceStatus.PERMISSION_DENIED, message=message)


class BaseService(abc.ABC):
    """
    Abstract base class for all business services.

    Provides common functionality and patterns for service implementations.
    """

    def __init__(self):
        self.service_id = str(uuid.uuid4())
        self.logger = logger.bind(
            service=self.__class__.__name__, service_id=self.service_id
        )

    @monitor_performance
    def execute_with_monitoring(
        self, operation_name: str, func, *args, **kwargs
    ) -> Any:
        """Execute a function with performance monitoring."""
        self.logger.info(f"Starting operation: {operation_name}")
        try:
            result = func(*args, **kwargs)
            self.logger.info(f"Completed operation: {operation_name}")
            return result
        except Exception as e:
            self.logger.error(f"Failed operation: {operation_name} - {e}")
            raise

    def validate_input(
        self, data: dict[str, Any], required_fields: list[str]
    ) -> ServiceResult[None]:
        """Validate input data for required fields."""
        missing_fields = [
            field
            for field in required_fields
            if field not in data or data[field] is None
        ]

        if missing_fields:
            return ServiceResult.validation_error(
                errors={"missing_fields": missing_fields},
                message=f"Missing required fields: {', '.join(missing_fields)}",
            )

        return ServiceResult.success()

    def handle_exception(self, e: Exception, operation: str) -> ServiceResult[Any]:
        """Handle exceptions and convert to ServiceResult."""
        self.logger.error(f"Exception in {operation}: {e}")

        if isinstance(e, ValidationError):
            return ServiceResult.validation_error(
                errors={"validation": str(e)}, message="Validation failed"
            )
        elif isinstance(e, PermissionError):
            return ServiceResult.permission_denied(message="Permission denied")
        else:
            return ServiceResult.error(
                message=f"An error occurred during {operation}",
                errors={"exception": str(e)},
            )


class TransactionalService(BaseService):
    """
    Base service class with database transaction support.

    Automatically wraps operations in database transactions.
    """

    def execute_in_transaction(self, func, *args, **kwargs) -> Any:
        """Execute a function within a database transaction."""
        with transaction.atomic():
            return func(*args, **kwargs)

    def safe_execute(
        self, operation_name: str, func, *args, **kwargs
    ) -> ServiceResult[Any]:
        """
        Safely execute an operation with transaction and error handling.

        Combines transaction management, performance monitoring, and error handling.
        """
        try:
            result = self.execute_with_monitoring(
                operation_name,
                lambda: self.execute_in_transaction(func, *args, **kwargs),
            )
            return ServiceResult.success(data=result)
        except Exception as e:
            return self.handle_exception(e, operation_name)


class CacheableService(BaseService):
    """
    Base service class with caching support.

    Provides caching patterns for service operations.
    """

    def __init__(self):
        super().__init__()
        from django.core.cache import cache

        self.cache = cache

    def get_cache_key(self, prefix: str, *args) -> str:
        """Generate a cache key for the service."""
        service_name = self.__class__.__name__.lower()
        key_parts = [service_name, prefix] + [str(arg) for arg in args]
        return ":".join(key_parts)

    def cache_result(self, key: str, data: Any, timeout: int = 300) -> None:
        """Cache operation result."""
        try:
            self.cache.set(key, data, timeout)
            self.logger.debug(f"Cached result with key: {key}")
        except Exception as e:
            self.logger.warning(f"Failed to cache result: {e}")

    def get_cached_result(self, key: str) -> Any | None:
        """Get cached operation result."""
        try:
            result = self.cache.get(key)
            if result is not None:
                self.logger.debug(f"Cache hit for key: {key}")
            return result
        except Exception as e:
            self.logger.warning(f"Failed to get cached result: {e}")
            return None

    def invalidate_cache(self, pattern: str) -> None:
        """Invalidate cache entries matching a pattern."""
        try:
            # Simple implementation - in production, use Redis with pattern matching
            self.logger.info(f"Cache invalidation requested for pattern: {pattern}")
        except Exception as e:
            self.logger.warning(f"Failed to invalidate cache: {e}")


class EventEmittingService(BaseService):
    """
    Base service class with event emission support.

    Enables event-driven architecture patterns.
    """

    def __init__(self):
        super().__init__()
        from .events import EventBus

        self.event_bus = EventBus()

    def emit_event(
        self, event_type: str, data: dict[str, Any], entity_id: str | None = None
    ) -> None:
        """Emit an event to the event bus."""
        try:
            from .events import Event

            event = Event(
                event_type=event_type,
                data=data,
                source_service=self.__class__.__name__,
                entity_id=entity_id,
            )
            self.event_bus.emit(event)
            self.logger.info(f"Emitted event: {event_type}")
        except Exception as e:
            self.logger.error(f"Failed to emit event {event_type}: {e}")


class ComprehensiveService(
    TransactionalService, CacheableService, EventEmittingService
):
    """
    Comprehensive service base class.

    Combines all service capabilities: transactions, caching, and events.
    """

    def __init__(self):
        # Initialize all parent classes
        TransactionalService.__init__(self)
        CacheableService.__init__(self)
        EventEmittingService.__init__(self)


# Service registry for dependency injection
class ServiceRegistry:
    """
    Service registry for dependency injection and service discovery.

    Enables loose coupling between services and prepares for microservices.
    """

    _services: dict[str, BaseService] = {}

    @classmethod
    def register(cls, service_name: str, service_instance: BaseService) -> None:
        """Register a service instance."""
        cls._services[service_name] = service_instance
        logger.info(f"Registered service: {service_name}")

    @classmethod
    def get(cls, service_name: str) -> BaseService | None:
        """Get a registered service instance."""
        return cls._services.get(service_name)

    @classmethod
    def get_all(cls) -> dict[str, BaseService]:
        """Get all registered services."""
        return cls._services.copy()

    @classmethod
    def unregister(cls, service_name: str) -> None:
        """Unregister a service."""
        if service_name in cls._services:
            del cls._services[service_name]
            logger.info(f"Unregistered service: {service_name}")


# Decorator for service methods
def service_operation(operation_name: str = None):
    """
    Decorator for service operations.

    Provides automatic logging and error handling for service methods.
    """

    def decorator(func):
        def wrapper(self, *args, **kwargs):
            op_name = operation_name or f"{self.__class__.__name__}.{func.__name__}"

            try:
                self.logger.info(f"Starting operation: {op_name}")
                result = func(self, *args, **kwargs)
                self.logger.info(f"Completed operation: {op_name}")
                return result
            except Exception as e:
                self.logger.error(f"Failed operation: {op_name} - {e}")
                return self.handle_exception(e, op_name)

        return wrapper

    return decorator
