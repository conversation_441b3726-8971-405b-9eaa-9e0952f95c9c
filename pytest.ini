[tool:pytest]
# Pytest configuration for Cosmetrics AI

# Django settings
DJANGO_SETTINGS_MODULE = cosmetrics_ai.settings.testing
python_files = tests.py test_*.py *_tests.py
python_classes = Test* *Tests
python_functions = test_*

# Test discovery
testpaths = tests
addopts =
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --cov=.
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=85
    --cov-branch
    --reuse-db
    --nomigrations

# Markers
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    api: marks tests as API tests
    performance: marks tests as performance tests
    security: marks tests as security tests
    smoke: marks tests as smoke tests for basic functionality
    models: marks tests as model tests
    schemas: marks tests as schema tests
    backends: marks tests as backend tests
    utils: marks tests as utility tests

# Filtering
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*django.utils.translation.*:DeprecationWarning

# Minimum version
minversion = 6.0
