import api from "../../src/api";
import { uploadQuestionaireReply } from "../hooks/user.actions";

/**
 * Registers a new user by sending their details to the backend API.
 */
export const signup = async (
  username: string,
  email: string,
  password: string
) => {
  try {
    const response = await api.post(
      `/users/register`,
      { username, email, password }
    );
    console.log("Signup successful:", response.data);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};

/**
 * Logs in a user by sending their credentials to the backend API.
 */
export const login = async (email: string, password: string) => {
  try {
    const response = await api.post(
      `/users/login`,
      { email, password }
    );
    console.log("Login successful:", response.data);
    return response;
  } catch (error) {
    console.error("Login error:", error);
    throw error;
  }
};

/**
 * Uploads all questionnaire replies for a user.
 */
export const uploadQuestionaire = async (
  user_id: string,
  questionaire: Record<string, string>,
  csrfToken: string,
  sessionGuid: string
): Promise<void> => {
  try {
    const replies = Object.entries(questionaire).map(([question_id, response]) =>
      uploadQuestionaireReply(user_id, question_id, response, csrfToken, sessionGuid)
    );

    await Promise.all(replies);
    console.log("Questionnaire upload completed.");
  } catch (error) {
    console.error("Error uploading questionnaire:", error);
    throw error;
  }
};

/**
 * Generates recommendations for a user after quiz completion.
 */
export const generateRecommendations = async (
  user_id: string,
  sessionGuid: string
): Promise<any> => {
  try {
    console.log(`Generating recommendations for user ${user_id}, session ${sessionGuid}`);

    const response = await api.post(
      `/recommendations/generate/user/${user_id}?session_guid=${sessionGuid}`,
      {},
      {
        // No CSRF token needed for JWT-based authentication
      }
    );

    console.log("Recommendations generated successfully:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error generating recommendations:", error);
    throw error;
  }
};

/**
 * Deletes a quiz session and all its replies.
 */
export const deleteQuizSession = async (sessionGuid: string): Promise<void> => {
  try {
    console.log(`Deleting quiz session: ${sessionGuid}`);

    const response = await api.delete(`/quiz/replies/sessions/session/${sessionGuid}`);

    console.log("Quiz session deleted successfully:", sessionGuid);
    return response.data;
  } catch (error) {
    console.error("Error deleting quiz session:", error);
    throw error;
  }
};
