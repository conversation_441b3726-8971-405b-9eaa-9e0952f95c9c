/**
 * Utility functions for handling errors in a type-safe way
 */

/**
 * Safely extracts error message from unknown error type
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (error && typeof error === 'object' && 'message' in error) {
    return String((error as any).message);
  }
  
  return 'An unknown error occurred';
}

/**
 * Checks if error is an instance of Error
 */
export function isError(error: unknown): error is Error {
  return error instanceof Error;
}

/**
 * Checks if error message contains specific text
 */
export function errorContains(error: unknown, searchText: string): boolean {
  const message = getErrorMessage(error).toLowerCase();
  return message.includes(searchText.toLowerCase());
}

/**
 * Safely extracts error stack trace
 */
export function getErrorStack(error: unknown): string | undefined {
  if (error instanceof Error) {
    return error.stack;
  }
  return undefined;
}

/**
 * Creates a standardized error object for logging
 */
export function createErrorLog(error: unknown, context?: string) {
  return {
    message: getErrorMessage(error),
    stack: getErrorStack(error),
    context,
    timestamp: new Date().toISOString(),
    type: error instanceof Error ? error.constructor.name : typeof error
  };
}

/**
 * Error types for quiz completion flow
 */
export enum QuizErrorType {
  UPLOAD_ERROR = 'upload',
  RECOMMENDATION_ERROR = 'recommendation',
  NETWORK_ERROR = 'network',
  VALIDATION_ERROR = 'validation',
  UNKNOWN_ERROR = 'unknown'
}

/**
 * Categorizes quiz-related errors
 */
export function categorizeQuizError(error: unknown): QuizErrorType {
  const message = getErrorMessage(error).toLowerCase();
  
  if (message.includes('recommendation')) {
    return QuizErrorType.RECOMMENDATION_ERROR;
  }
  
  if (message.includes('upload') || message.includes('submit')) {
    return QuizErrorType.UPLOAD_ERROR;
  }
  
  if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
    return QuizErrorType.NETWORK_ERROR;
  }
  
  if (message.includes('validation') || message.includes('invalid')) {
    return QuizErrorType.VALIDATION_ERROR;
  }
  
  return QuizErrorType.UNKNOWN_ERROR;
}

/**
 * Gets user-friendly error message based on error type
 */
export function getUserFriendlyErrorMessage(error: unknown, context?: string): string {
  const errorType = categorizeQuizError(error);
  
  switch (errorType) {
    case QuizErrorType.RECOMMENDATION_ERROR:
      return 'Quiz completed, but we couldn\'t generate recommendations right now. Please check your dashboard later.';
    
    case QuizErrorType.UPLOAD_ERROR:
      return 'Failed to submit quiz. Please try again.';
    
    case QuizErrorType.NETWORK_ERROR:
      return 'Network connection issue. Please check your internet connection and try again.';
    
    case QuizErrorType.VALIDATION_ERROR:
      return 'Some quiz responses are invalid. Please review your answers and try again.';
    
    case QuizErrorType.UNKNOWN_ERROR:
    default:
      return context 
        ? `An error occurred during ${context}. Please try again.`
        : 'An unexpected error occurred. Please try again.';
  }
}

/**
 * Logs error with context for debugging
 */
export function logError(error: unknown, context?: string, additionalData?: Record<string, any>) {
  const errorLog = createErrorLog(error, context);
  
  console.error(`[${context || 'Error'}]:`, {
    ...errorLog,
    ...additionalData
  });
  
  // In production, you might want to send this to an error tracking service
  // Example: Sentry.captureException(error, { extra: { context, additionalData } });
}

/**
 * Handles quiz completion errors with appropriate user feedback
 */
export function handleQuizCompletionError(
  error: unknown, 
  onQuizSuccess: () => void,
  onShowError: (message: string) => void,
  onShowWarning: (message: string) => void
) {
  const errorType = categorizeQuizError(error);
  const userMessage = getUserFriendlyErrorMessage(error, 'quiz completion');
  
  logError(error, 'quiz_completion', { errorType });
  
  switch (errorType) {
    case QuizErrorType.RECOMMENDATION_ERROR:
      // Quiz was successful, but recommendations failed
      onQuizSuccess();
      onShowWarning(userMessage);
      break;
    
    case QuizErrorType.UPLOAD_ERROR:
    case QuizErrorType.NETWORK_ERROR:
    case QuizErrorType.VALIDATION_ERROR:
    case QuizErrorType.UNKNOWN_ERROR:
    default:
      // Quiz submission failed
      onShowError(userMessage);
      break;
  }
}

/**
 * Type guard for API errors with status codes
 */
export function isApiError(error: unknown): error is { status: number; message: string } {
  return (
    error !== null &&
    typeof error === 'object' &&
    'status' in error &&
    'message' in error &&
    typeof (error as any).status === 'number' &&
    typeof (error as any).message === 'string'
  );
}

/**
 * Handles API errors with status code specific logic
 */
export function handleApiError(error: unknown): string {
  if (isApiError(error)) {
    switch (error.status) {
      case 400:
        return 'Invalid request. Please check your input and try again.';
      case 401:
        return 'Authentication required. Please log in and try again.';
      case 403:
        return 'You don\'t have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return `Request failed with status ${error.status}: ${error.message}`;
    }
  }
  
  return getUserFriendlyErrorMessage(error);
}
