import React from 'react';
import { Droplet, Star, ExclamationTriangle, <PERSON><PERSON>ure, Wind, Snow3, Hourglass } from 'react-bootstrap-icons';


// INTERFACES

export interface UserSessionDetails {
  userId: string;
  sessionGuid: string;
}

export interface ScoreItem {
  value: number;
  description: string;
}

export interface StatusCardProps {
  title: string;
  scoreMap: Record<string, ScoreItem>;
  keys: string[];
}


// COMMON FUNCTIONS
export const getHighestScoreItem = (scores: Record<string, ScoreItem>): [string, ScoreItem] => {
  const entries = Object.entries(scores).filter(([_, v]) => v && typeof v.value === 'number');
  if (entries.length === 0) return ['', { value: 0, description: 'No data available' }];

  console.log(`Getting highest score for grading: ${entries}`);
  return entries.reduce(
    (max, entry) => (entry[1].value > max[1].value ? entry : max),
    ['', { value: -Infinity, description: '' }]
  );
};

export const getHealthStatus = (value: number): { text: string; variant: string; } => {
  if (value <= 25) return { text: 'Lower is Better', variant: 'success' };
  if (value <= 50) return { text: 'Can be Better', variant: 'primary' };
  if (value <= 75) return { text: 'Need Care', variant: 'secondary' };
  return { text: 'Attention Needed', variant: 'danger' };
};


export const getScoreIcon = (scoreType: string) => {
  switch (scoreType) {
    case 'dry_score_percentage':
      return React.createElement(Wind, { size: 24 });
    case 'damage_score_percentage':
      return React.createElement(ExclamationTriangle, { size: 24 });
    case 'sensitivity_percentage':
      return React.createElement(Star, { size: 24 });
    case 'sebum_oily_percentage':
      return React.createElement(Droplet, { size: 24 });
    case 'sebum_dry_percentage':
      return React.createElement(Moisture, { size: 24 });
    case 'flake_score_percentage':
      return React.createElement(Snow3, { size: 24 });
    default:
      return React.createElement(Hourglass, { size: 24 });
  }
};


export const getHighestScoreCategory = (scores: Record<string, ScoreItem>): string => {
  let highestScore = 0;
  let highestCategory = '';

  Object.entries(scores).forEach(([_, data]) => {
    if (data.value > highestScore) {
      highestScore = data.value;
      highestCategory = data.description;
    }
  });

  return highestCategory;
};
