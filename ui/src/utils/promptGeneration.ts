/**
 * Utility functions for generating grammatically correct LLM prompts
 * from user hair analysis data
 */

import { DataItem } from '../constants';
import { ScoreItem } from './dashboard';

export interface PromptGenerationOptions {
  includeInstructions?: boolean;
  promptStyle?: 'detailed' | 'concise' | 'clinical';
  targetAudience?: 'general' | 'professional' | 'technical';
}

/**
 * Formats text for consistent prompt generation
 */
export const formatPromptText = (text: string): string => {
  if (!text) return '';
  return text.toLowerCase().trim().replace(/\s+/g, ' ');
};

/**
 * Creates grammatically correct sentence parts based on question type
 */
export const createHairProfileSentence = (label: string, value: string): string => {
  const formattedValue = formatPromptText(value);
  if (!formattedValue) return '';
  
  // Grammar rules for different hair analysis categories
  const grammarRules: Record<string, (value: string) => string> = {
    'hair goal': (v) => `seeking ${v}`,
    'chosen issue': (v) => `experiencing ${v}`,
    'hair type': (v) => `with ${v} hair`,
    'hair density': (v) => `having ${v} hair density`,
    'hair texture': (v) => `with ${v} hair texture`,
    'hair porosity': (v) => `with ${v} porosity`,
  };

  const formatter = grammarRules[label];
  return formatter ? formatter(formattedValue) : `with ${label} being ${formattedValue}`;
};

/**
 * Generates priority issue text with proper grammar
 */
export const generatePriorityText = (topIssueItem: ScoreItem | null): string => {
  if (!topIssueItem?.description) return '';
  
  const formattedIssue = formatPromptText(topIssueItem.description);
  const severity = getSeverityLevel(topIssueItem.value);
  
  return ` The ${severity} concern identified is ${formattedIssue}.`;
};

/**
 * Determines severity level based on score value
 */
export const getSeverityLevel = (value: number): string => {
  if (value >= 80) return 'critical';
  if (value >= 60) return 'primary';
  if (value >= 40) return 'moderate';
  return 'minor';
};

/**
 * Generates porosity analysis text
 */
export const generatePorosityText = (porosityValue: string | null): string => {
  if (!porosityValue) return '';
  
  const formattedPorosity = formatPromptText(porosityValue);
  return ` Hair porosity analysis indicates ${formattedPorosity} porosity levels.`;
};

/**
 * Creates instruction text based on prompt style
 */
export const generateInstructions = (options: PromptGenerationOptions = {}): string => {
  const { promptStyle = 'detailed', targetAudience = 'general' } = options;
  
  const baseInstructions = 'Based on this hair analysis, please provide';
  
  const styleInstructions: Record<string, string> = {
    detailed: 'specific, actionable recommendations for optimal hair care, including product suggestions, routine adjustments, and lifestyle considerations',
    concise: 'key recommendations for hair care improvement',
    clinical: 'professional assessment and treatment recommendations based on the hair condition analysis'
  };

  const audienceModifiers: Record<string, string> = {
    general: 'that are easy to understand and implement',
    professional: 'suitable for hair care professionals',
    technical: 'with detailed scientific rationale'
  };

  return `${baseInstructions} ${styleInstructions[promptStyle]} ${audienceModifiers[targetAudience]}.`;
};

/**
 * Validates prompt quality and suggests improvements
 */
export const validatePrompt = (prompt: string): {
  isValid: boolean;
  issues: string[];
  suggestions: string[];
} => {
  const issues: string[] = [];
  const suggestions: string[] = [];

  // Check minimum length
  if (prompt.length < 50) {
    issues.push('Prompt is too short');
    suggestions.push('Add more context about hair condition');
  }

  // Check for proper sentence structure
  if (!prompt.includes('.')) {
    issues.push('Missing proper sentence structure');
    suggestions.push('Ensure sentences end with periods');
  }

  // Check for redundant words
  const redundantPatterns = [/\s+/g, /\.\./g, /,,/g];
  redundantPatterns.forEach(pattern => {
    if (pattern.test(prompt)) {
      issues.push('Contains redundant punctuation or spacing');
      suggestions.push('Clean up spacing and punctuation');
    }
  });

  // Check for incomplete sentences
  if (prompt.includes('is .') || prompt.includes('are .')) {
    issues.push('Contains incomplete sentence fragments');
    suggestions.push('Complete all sentence fragments');
  }

  return {
    isValid: issues.length === 0,
    issues,
    suggestions
  };
};

/**
 * Main function to generate a complete, grammatically correct LLM prompt
 */
export const generateLLMPrompt = (
  replies: DataItem[],
  questionLabels: Record<number, string>,
  topIssueItem: ScoreItem | null = null,
  porosityValue: string | null = null,
  options: PromptGenerationOptions = {}
): string => {
  const { includeInstructions = true } = options;

  // Filter out exception questions (photos, etc.)
  const exceptionsList = [8, 37]; // These are handled separately
  const filteredReplies = replies.filter(reply => !exceptionsList.includes(reply.question_id));

  // Generate profile parts
  const profileParts = filteredReplies
    .map(reply => {
      const label = questionLabels[reply.question_id];
      if (label && reply.text) {
        return createHairProfileSentence(label, reply.text);
      }
      return null;
    })
    .filter(Boolean);

  // Build the prompt sections
  const sections: string[] = [];

  // User profile section
  if (profileParts.length > 0) {
    sections.push(`User profile: Individual ${profileParts.join(', ')}.`);
  } else {
    sections.push('User profile: Limited response data available.');
  }

  // Priority issue section
  const priorityText = generatePriorityText(topIssueItem);
  if (priorityText) {
    sections.push(priorityText.trim());
  }

  // Porosity section
  const porosityText = generatePorosityText(porosityValue);
  if (porosityText) {
    sections.push(porosityText.trim());
  }

  // Instructions section
  if (includeInstructions) {
    sections.push(generateInstructions(options));
  }

  // Combine all sections
  const prompt = sections.join(' ');

  // Validate and clean up the prompt
  const validation = validatePrompt(prompt);
  if (!validation.isValid) {
    console.warn('Prompt validation issues:', validation.issues);
    console.warn('Suggestions:', validation.suggestions);
  }

  return prompt;
};

/**
 * Generates a prompt specifically optimized for different LLM providers
 */
export const generateProviderOptimizedPrompt = (
  basePrompt: string,
  provider: 'openai' | 'anthropic' | 'google' | 'generic' = 'generic'
): string => {
  const providerPrefixes: Record<string, string> = {
    openai: 'You are a professional hair care specialist. ',
    anthropic: 'I need expert hair care advice. ',
    google: 'As a hair care professional, ',
    generic: ''
  };

  const providerSuffixes: Record<string, string> = {
    openai: ' Please format your response with clear sections and bullet points.',
    anthropic: ' Please provide a thoughtful, detailed response.',
    google: ' Structure your advice clearly and professionally.',
    generic: ''
  };

  return `${providerPrefixes[provider]}${basePrompt}${providerSuffixes[provider]}`;
};
