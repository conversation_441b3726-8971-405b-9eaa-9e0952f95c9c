/**
 * Tests for error handling utilities
 */

import {
  getErrorMessage,
  isError,
  errorContains,
  getErrorStack,
  createErrorLog,
  categorizeQuizError,
  getUserFriendlyErrorMessage,
  handleQuizCompletionError,
  isApiError,
  handleApiError,
  QuizErrorType
} from '../errorHandling';

describe('Error Handling Utilities', () => {
  describe('getErrorMessage', () => {
    it('should extract message from Error instance', () => {
      const error = new Error('Test error message');
      expect(getErrorMessage(error)).toBe('Test error message');
    });

    it('should handle string errors', () => {
      expect(getErrorMessage('String error')).toBe('String error');
    });

    it('should handle objects with message property', () => {
      const error = { message: 'Object error message' };
      expect(getErrorMessage(error)).toBe('Object error message');
    });

    it('should handle unknown error types', () => {
      expect(getErrorMessage(null)).toBe('An unknown error occurred');
      expect(getErrorMessage(undefined)).toBe('An unknown error occurred');
      expect(getErrorMessage(123)).toBe('An unknown error occurred');
    });
  });

  describe('isError', () => {
    it('should identify Error instances', () => {
      expect(isError(new Error('test'))).toBe(true);
      expect(isError(new TypeError('test'))).toBe(true);
    });

    it('should reject non-Error types', () => {
      expect(isError('string')).toBe(false);
      expect(isError({ message: 'test' })).toBe(false);
      expect(isError(null)).toBe(false);
    });
  });

  describe('errorContains', () => {
    it('should find text in error messages', () => {
      const error = new Error('Recommendation generation failed');
      expect(errorContains(error, 'recommendation')).toBe(true);
      expect(errorContains(error, 'RECOMMENDATION')).toBe(true);
      expect(errorContains(error, 'upload')).toBe(false);
    });

    it('should handle non-Error types', () => {
      expect(errorContains('Upload failed', 'upload')).toBe(true);
      expect(errorContains({ message: 'Network error' }, 'network')).toBe(true);
    });
  });

  describe('createErrorLog', () => {
    it('should create structured error log', () => {
      const error = new Error('Test error');
      const log = createErrorLog(error, 'test_context');

      expect(log.message).toBe('Test error');
      expect(log.context).toBe('test_context');
      expect(log.type).toBe('Error');
      expect(log.timestamp).toBeDefined();
      expect(log.stack).toBeDefined();
    });
  });

  describe('categorizeQuizError', () => {
    it('should categorize recommendation errors', () => {
      const error = new Error('Recommendation generation failed');
      expect(categorizeQuizError(error)).toBe(QuizErrorType.RECOMMENDATION_ERROR);
    });

    it('should categorize upload errors', () => {
      const error = new Error('Failed to upload quiz responses');
      expect(categorizeQuizError(error)).toBe(QuizErrorType.UPLOAD_ERROR);
    });

    it('should categorize network errors', () => {
      const error = new Error('Network connection failed');
      expect(categorizeQuizError(error)).toBe(QuizErrorType.NETWORK_ERROR);
    });

    it('should categorize validation errors', () => {
      const error = new Error('Invalid quiz data');
      expect(categorizeQuizError(error)).toBe(QuizErrorType.VALIDATION_ERROR);
    });

    it('should default to unknown error', () => {
      const error = new Error('Something went wrong');
      expect(categorizeQuizError(error)).toBe(QuizErrorType.UNKNOWN_ERROR);
    });
  });

  describe('getUserFriendlyErrorMessage', () => {
    it('should provide user-friendly messages for different error types', () => {
      const recommendationError = new Error('Recommendation failed');
      const uploadError = new Error('Upload failed');
      const networkError = new Error('Network error');
      const unknownError = new Error('Random error');

      expect(getUserFriendlyErrorMessage(recommendationError)).toContain('recommendations');
      expect(getUserFriendlyErrorMessage(uploadError)).toContain('submit quiz');
      expect(getUserFriendlyErrorMessage(networkError)).toContain('Network connection');
      expect(getUserFriendlyErrorMessage(unknownError)).toContain('unexpected error');
    });

    it('should include context in unknown error messages', () => {
      const error = new Error('Random error');
      const message = getUserFriendlyErrorMessage(error, 'quiz completion');
      expect(message).toContain('quiz completion');
    });
  });

  describe('handleQuizCompletionError', () => {
    it('should handle recommendation errors correctly', () => {
      const onQuizSuccess = jest.fn();
      const onShowError = jest.fn();
      const onShowWarning = jest.fn();

      const recommendationError = new Error('Recommendation generation failed');
      
      handleQuizCompletionError(
        recommendationError,
        onQuizSuccess,
        onShowError,
        onShowWarning
      );

      expect(onQuizSuccess).toHaveBeenCalled();
      expect(onShowWarning).toHaveBeenCalled();
      expect(onShowError).not.toHaveBeenCalled();
    });

    it('should handle upload errors correctly', () => {
      const onQuizSuccess = jest.fn();
      const onShowError = jest.fn();
      const onShowWarning = jest.fn();

      const uploadError = new Error('Failed to upload quiz');
      
      handleQuizCompletionError(
        uploadError,
        onQuizSuccess,
        onShowError,
        onShowWarning
      );

      expect(onQuizSuccess).not.toHaveBeenCalled();
      expect(onShowError).toHaveBeenCalled();
      expect(onShowWarning).not.toHaveBeenCalled();
    });
  });

  describe('isApiError', () => {
    it('should identify API errors', () => {
      const apiError = { status: 404, message: 'Not found' };
      expect(isApiError(apiError)).toBe(true);
    });

    it('should reject non-API errors', () => {
      expect(isApiError(new Error('test'))).toBe(false);
      expect(isApiError({ message: 'test' })).toBe(false);
      expect(isApiError({ status: 'error' })).toBe(false);
    });
  });

  describe('handleApiError', () => {
    it('should handle different HTTP status codes', () => {
      expect(handleApiError({ status: 400, message: 'Bad request' })).toContain('Invalid request');
      expect(handleApiError({ status: 401, message: 'Unauthorized' })).toContain('Authentication required');
      expect(handleApiError({ status: 403, message: 'Forbidden' })).toContain('permission');
      expect(handleApiError({ status: 404, message: 'Not found' })).toContain('not found');
      expect(handleApiError({ status: 500, message: 'Server error' })).toContain('Server error');
    });

    it('should handle non-API errors', () => {
      const error = new Error('Generic error');
      const message = handleApiError(error);
      expect(message).toBe('An unexpected error occurred. Please try again.');
    });
  });
});

// Integration test example
describe('Error Handling Integration', () => {
  it('should handle quiz completion flow errors end-to-end', () => {
    const mockDispatch = jest.fn();
    const mockToast = {
      error: jest.fn(),
      warning: jest.fn(),
      success: jest.fn()
    };

    // Simulate recommendation error
    const recommendationError = new Error('Failed to generate recommendations');
    
    handleQuizCompletionError(
      recommendationError,
      () => mockDispatch({ type: 'MARK_QUIZ_COMPLETED' }),
      (message) => mockToast.error(message),
      (message) => mockToast.warning(message)
    );

    expect(mockDispatch).toHaveBeenCalledWith({ type: 'MARK_QUIZ_COMPLETED' });
    expect(mockToast.warning).toHaveBeenCalled();
    expect(mockToast.error).not.toHaveBeenCalled();
  });
});
