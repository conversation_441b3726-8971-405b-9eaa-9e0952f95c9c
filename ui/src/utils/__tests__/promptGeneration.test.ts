/**
 * Tests for prompt generation utilities
 */

import {
  formatPromptText,
  createHairProfileSentence,
  generatePriorityText,
  generatePorosityText,
  generateLLMPrompt,
  validatePrompt,
  generateProviderOptimizedPrompt
} from '../promptGeneration';
import { DataItem } from '../../constants';
import { ScoreItem } from '../dashboard';

describe('Prompt Generation Utilities', () => {
  describe('formatPromptText', () => {
    it('should format text correctly', () => {
      expect(formatPromptText('  CURLY HAIR  ')).toBe('curly hair');
      expect(formatPromptText('Multiple   Spaces')).toBe('multiple spaces');
      expect(formatPromptText('')).toBe('');
    });
  });

  describe('createHairProfileSentence', () => {
    it('should create correct sentences for different hair attributes', () => {
      expect(createHairProfileSentence('hair goal', 'Growth')).toBe('seeking growth');
      expect(createHairProfileSentence('chosen issue', 'Split ends')).toBe('experiencing split ends');
      expect(createHairProfileSentence('hair type', 'Curly')).toBe('with curly hair');
      expect(createHairProfileSentence('hair density', 'Thick')).toBe('having thick hair density');
      expect(createHairProfileSentence('hair texture', 'Coarse')).toBe('with coarse hair texture');
    });

    it('should handle empty values', () => {
      expect(createHairProfileSentence('hair goal', '')).toBe('');
      expect(createHairProfileSentence('hair goal', '   ')).toBe('');
    });
  });

  describe('generatePriorityText', () => {
    it('should generate priority text with severity levels', () => {
      const highPriorityIssue: ScoreItem = { value: 85, description: 'Severe Dryness' };
      expect(generatePriorityText(highPriorityIssue)).toBe(' The critical concern identified is severe dryness.');

      const moderatePriorityIssue: ScoreItem = { value: 45, description: 'Mild Damage' };
      expect(generatePriorityText(moderatePriorityIssue)).toBe(' The moderate concern identified is mild damage.');
    });

    it('should handle null priority item', () => {
      expect(generatePriorityText(null)).toBe('');
    });
  });

  describe('generatePorosityText', () => {
    it('should generate porosity text correctly', () => {
      expect(generatePorosityText('High')).toBe(' Hair porosity analysis indicates high porosity levels.');
      expect(generatePorosityText('Low')).toBe(' Hair porosity analysis indicates low porosity levels.');
    });

    it('should handle null porosity value', () => {
      expect(generatePorosityText(null)).toBe('');
    });
  });

  describe('validatePrompt', () => {
    it('should validate correct prompts', () => {
      const goodPrompt = 'User profile: Individual seeking growth, with curly hair. The primary concern identified is dryness. Based on this analysis, please provide recommendations.';
      const validation = validatePrompt(goodPrompt);
      expect(validation.isValid).toBe(true);
      expect(validation.issues).toHaveLength(0);
    });

    it('should identify issues in bad prompts', () => {
      const badPrompt = 'short';
      const validation = validatePrompt(badPrompt);
      expect(validation.isValid).toBe(false);
      expect(validation.issues.length).toBeGreaterThan(0);
    });
  });

  describe('generateLLMPrompt', () => {
    const mockReplies: DataItem[] = [
      {
        user_id: 1,
        session_guid: 'test-session',
        question_id: 1,
        text: 'Growth',
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      },
      {
        user_id: 1,
        session_guid: 'test-session',
        question_id: 6,
        text: 'Curly',
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      }
    ];

    const mockQuestionLabels = {
      1: 'hair goal',
      6: 'hair type'
    };

    const mockTopIssue: ScoreItem = {
      value: 75,
      description: 'Dryness'
    };

    it('should generate a complete LLM prompt', () => {
      const prompt = generateLLMPrompt(
        mockReplies,
        mockQuestionLabels,
        mockTopIssue,
        'High',
        { promptStyle: 'detailed' }
      );

      expect(prompt).toContain('User profile: Individual seeking growth, with curly hair.');
      expect(prompt).toContain('concern identified is dryness');
      expect(prompt).toContain('porosity analysis indicates high porosity');
      expect(prompt).toContain('please provide specific, actionable recommendations');
    });

    it('should handle empty replies gracefully', () => {
      const prompt = generateLLMPrompt(
        [],
        mockQuestionLabels,
        null,
        null,
        { promptStyle: 'concise' }
      );

      expect(prompt).toContain('Limited response data available');
      expect(prompt).toContain('key recommendations');
    });
  });

  describe('generateProviderOptimizedPrompt', () => {
    const basePrompt = 'User profile: Individual seeking growth.';

    it('should optimize for different providers', () => {
      const openaiPrompt = generateProviderOptimizedPrompt(basePrompt, 'openai');
      expect(openaiPrompt).toContain('professional hair care specialist');
      expect(openaiPrompt).toContain('bullet points');

      const anthropicPrompt = generateProviderOptimizedPrompt(basePrompt, 'anthropic');
      expect(anthropicPrompt).toContain('expert hair care advice');
      expect(anthropicPrompt).toContain('thoughtful, detailed response');
    });

    it('should handle generic provider', () => {
      const genericPrompt = generateProviderOptimizedPrompt(basePrompt, 'generic');
      expect(genericPrompt).toBe(basePrompt);
    });
  });
});

// Example of expected output for documentation
describe('Example Outputs', () => {
  it('should generate expected prompt format', () => {
    const mockData: DataItem[] = [
      { user_id: 1, session_guid: 'test', question_id: 1, text: 'Growth', created_at: '', updated_at: '' },
      { user_id: 1, session_guid: 'test', question_id: 2, text: 'Split ends', created_at: '', updated_at: '' },
      { user_id: 1, session_guid: 'test', question_id: 6, text: 'Curly', created_at: '', updated_at: '' }
    ];

    const labels = { 1: 'hair goal', 2: 'chosen issue', 6: 'hair type' };
    const topIssue: ScoreItem = { value: 80, description: 'Severe Dryness' };

    const result = generateLLMPrompt(mockData, labels, topIssue, 'High');

    // Expected format:
    // "User profile: Individual seeking growth, experiencing split ends, with curly hair. 
    //  The critical concern identified is severe dryness. 
    //  Hair porosity analysis indicates high porosity levels. 
    //  Based on this hair analysis, please provide specific, actionable recommendations..."

    expect(result).toMatch(/^User profile: Individual .+\./);
    expect(result).toContain('seeking growth');
    expect(result).toContain('experiencing split ends');
    expect(result).toContain('with curly hair');
    expect(result).toContain('critical concern identified is severe dryness');
    expect(result).toContain('high porosity levels');
    expect(result).toContain('please provide specific, actionable recommendations');
  });
});
