/**
 * Tests for recommendation generation flow
 */

import { generateRecommendations } from '../../apis';
import api from '../../api';

// Mock the api module
jest.mock('../../api');
const mockedApi = api as jest.Mocked<typeof api>;

describe('Recommendation Generation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.log = jest.fn(); // Mock console.log
    console.error = jest.fn(); // Mock console.error
  });

  describe('generateRecommendations', () => {
    it('should call the correct API endpoint with proper parameters', async () => {
      const mockResponse = {
        data: {
          id: 1,
          user_id: 123,
          session_guid: 'test-session-guid',
          recommendations: ['Product A', 'Product B'],
          created_at: '2024-01-01T00:00:00Z'
        }
      };

      mockedApi.post.mockResolvedValueOnce(mockResponse);

      const userId = '123';
      const sessionGuid = 'test-session-guid';

      const result = await generateRecommendations(userId, sessionGuid);

      // Verify API call
      expect(mockedApi.post).toHaveBeenCalledWith(
        `/recommendations/generate/user/${userId}?session_guid=${sessionGuid}`,
        {},
        {
          // No CSRF token needed for JWT-based authentication
        }
      );

      // Verify result
      expect(result).toEqual(mockResponse.data);

      // Verify logging
      expect(console.log).toHaveBeenCalledWith(
        `Generating recommendations for user ${userId}, session ${sessionGuid}`
      );
      expect(console.log).toHaveBeenCalledWith(
        'Recommendations generated successfully:',
        mockResponse.data
      );
    });

    it('should handle API errors gracefully', async () => {
      const mockError = new Error('API Error');
      mockedApi.post.mockRejectedValueOnce(mockError);

      const userId = '123';
      const sessionGuid = 'test-session-guid';

      await expect(generateRecommendations(userId, sessionGuid)).rejects.toThrow('API Error');

      // Verify error logging
      expect(console.error).toHaveBeenCalledWith(
        'Error generating recommendations:',
        mockError
      );
    });

    it('should handle network errors', async () => {
      const networkError = new Error('Network Error');
      mockedApi.post.mockRejectedValueOnce(networkError);

      const userId = '456';
      const sessionGuid = 'another-session-guid';

      await expect(generateRecommendations(userId, sessionGuid)).rejects.toThrow('Network Error');
    });

    it('should handle empty session guid', async () => {
      const mockResponse = { data: { id: 1, recommendations: [] } };
      mockedApi.post.mockResolvedValueOnce(mockResponse);

      const userId = '123';
      const sessionGuid = '';

      await generateRecommendations(userId, sessionGuid);

      expect(mockedApi.post).toHaveBeenCalledWith(
        `/recommendations/generate/user/${userId}?session_guid=`,
        {},
        {}
      );
    });
  });

  describe('Integration scenarios', () => {
    it('should handle successful quiz completion flow', async () => {
      const mockRecommendationResponse = {
        data: {
          id: 1,
          user_id: 123,
          session_guid: 'quiz-session-123',
          recommendations: ['Shampoo A', 'Conditioner B', 'Treatment C'],
          created_at: '2024-01-01T00:00:00Z'
        }
      };

      mockedApi.post.mockResolvedValueOnce(mockRecommendationResponse);

      const userId = '123';
      const sessionGuid = 'quiz-session-123';

      // Simulate quiz completion and recommendation generation
      const result = await generateRecommendations(userId, sessionGuid);

      expect(result).toEqual(mockRecommendationResponse.data);
      expect(result.recommendations).toHaveLength(3);
      expect(result.user_id).toBe(123);
      expect(result.session_guid).toBe(sessionGuid);
    });

    it('should handle session attribution flow', async () => {
      const mockAttributionResponse = { data: { success: true } };
      const mockRecommendationResponse = {
        data: {
          id: 2,
          user_id: 456,
          session_guid: 'attributed-session-456',
          recommendations: ['Product X', 'Product Y'],
          created_at: '2024-01-01T00:00:00Z'
        }
      };

      // Mock both attribution and recommendation calls
      mockedApi.post
        .mockResolvedValueOnce(mockAttributionResponse) // Attribution call
        .mockResolvedValueOnce(mockRecommendationResponse); // Recommendation call

      const userId = '456';
      const sessionGuid = 'attributed-session-456';

      // Simulate session attribution
      await api.post('/api/quiz/replies/attribute-session', {
        session_guid: sessionGuid,
        user_id: parseInt(userId)
      });

      // Then generate recommendations
      const result = await generateRecommendations(userId, sessionGuid);

      expect(mockedApi.post).toHaveBeenCalledTimes(2);
      expect(result).toEqual(mockRecommendationResponse.data);
    });

    it('should handle recommendation generation failure gracefully in quiz flow', async () => {
      const recommendationError = new Error('Recommendation service unavailable');
      mockedApi.post.mockRejectedValueOnce(recommendationError);

      const userId = '789';
      const sessionGuid = 'failing-session-789';

      // This should throw, but in the actual implementation, 
      // the quiz completion should still succeed
      await expect(generateRecommendations(userId, sessionGuid)).rejects.toThrow(
        'Recommendation service unavailable'
      );

      expect(console.error).toHaveBeenCalledWith(
        'Error generating recommendations:',
        recommendationError
      );
    });
  });

  describe('API endpoint validation', () => {
    it('should construct the correct URL format', async () => {
      const mockResponse = { data: { id: 1 } };
      mockedApi.post.mockResolvedValueOnce(mockResponse);

      const userId = '123';
      const sessionGuid = 'test-session-guid-with-special-chars-123';

      await generateRecommendations(userId, sessionGuid);

      const expectedUrl = `/recommendations/generate/user/${userId}?session_guid=${sessionGuid}`;
      expect(mockedApi.post).toHaveBeenCalledWith(expectedUrl, {}, {});
    });

    it('should handle special characters in session guid', async () => {
      const mockResponse = { data: { id: 1 } };
      mockedApi.post.mockResolvedValueOnce(mockResponse);

      const userId = '123';
      const sessionGuid = 'session-with-dashes-and-numbers-123-456';

      await generateRecommendations(userId, sessionGuid);

      expect(mockedApi.post).toHaveBeenCalledWith(
        `/recommendations/generate/user/${userId}?session_guid=${sessionGuid}`,
        {},
        {}
      );
    });
  });
});

// Example test data for documentation
export const mockRecommendationData = {
  successful_response: {
    id: 1,
    user_id: 123,
    session_guid: 'example-session-guid',
    recommendations: [
      'Moisturizing Shampoo for Dry Hair',
      'Deep Conditioning Treatment',
      'Leave-in Protein Treatment',
      'Heat Protection Spray'
    ],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  error_response: {
    error: 'Recommendation generation failed',
    details: 'Insufficient user data for recommendation generation'
  }
};
