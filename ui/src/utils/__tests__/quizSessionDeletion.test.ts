/**
 * Tests for quiz session deletion functionality
 */

import { deleteQuizSession } from '../../apis';
import api from '../../api';

// Mock the api module
jest.mock('../../api');
const mockedApi = api as jest.Mocked<typeof api>;

describe('Quiz Session Deletion', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.log = jest.fn();
    console.error = jest.fn();
  });

  describe('deleteQuizSession', () => {
    it('should call the correct API endpoint with session GUID', async () => {
      const mockResponse = { data: { success: true } };
      mockedApi.delete.mockResolvedValueOnce(mockResponse);

      const sessionGuid = 'test-session-guid-123';

      const result = await deleteQuizSession(sessionGuid);

      // Verify API call
      expect(mockedApi.delete).toHaveBeenCalledWith(
        `/quiz/replies/sessions/session/${sessionGuid}`
      );

      // Verify result
      expect(result).toEqual(mockResponse.data);

      // Verify logging
      expect(console.log).toHaveBeenCalledWith(
        `Deleting quiz session: ${sessionGuid}`
      );
      expect(console.log).toHaveBeenCalledWith(
        'Quiz session deleted successfully:',
        sessionGuid
      );
    });

    it('should handle API errors gracefully', async () => {
      const mockError = new Error('Session not found');
      mockedApi.delete.mockRejectedValueOnce(mockError);

      const sessionGuid = 'non-existent-session';

      await expect(deleteQuizSession(sessionGuid)).rejects.toThrow('Session not found');

      // Verify error logging
      expect(console.error).toHaveBeenCalledWith(
        'Error deleting quiz session:',
        mockError
      );
    });

    it('should handle 404 errors for non-existent sessions', async () => {
      const mockError = {
        response: {
          status: 404,
          data: { message: 'Session not found' }
        },
        message: 'Request failed with status code 404'
      };
      mockedApi.delete.mockRejectedValueOnce(mockError);

      const sessionGuid = 'non-existent-session-404';

      await expect(deleteQuizSession(sessionGuid)).rejects.toEqual(mockError);

      expect(console.error).toHaveBeenCalledWith(
        'Error deleting quiz session:',
        mockError
      );
    });

    it('should handle network errors', async () => {
      const networkError = new Error('Network Error');
      mockedApi.delete.mockRejectedValueOnce(networkError);

      const sessionGuid = 'session-network-error';

      await expect(deleteQuizSession(sessionGuid)).rejects.toThrow('Network Error');
    });

    it('should handle empty session GUID', async () => {
      const mockResponse = { data: { success: false, message: 'Invalid session GUID' } };
      mockedApi.delete.mockResolvedValueOnce(mockResponse);

      const sessionGuid = '';

      await deleteQuizSession(sessionGuid);

      expect(mockedApi.delete).toHaveBeenCalledWith(
        '/quiz/replies/sessions/session/'
      );
    });
  });

  describe('Integration scenarios', () => {
    it('should handle successful deletion flow', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Session deleted successfully',
          deleted_session_guid: 'test-session-123'
        }
      };

      mockedApi.delete.mockResolvedValueOnce(mockResponse);

      const sessionGuid = 'test-session-123';

      const result = await deleteQuizSession(sessionGuid);

      expect(result).toEqual(mockResponse.data);
      expect(result.success).toBe(true);
      expect(result.deleted_session_guid).toBe(sessionGuid);
    });

    it('should handle deletion of incomplete session with replies', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Session and 5 replies deleted successfully',
          deleted_replies_count: 5
        }
      };

      mockedApi.delete.mockResolvedValueOnce(mockResponse);

      const sessionGuid = 'incomplete-session-with-replies';

      const result = await deleteQuizSession(sessionGuid);

      expect(result.success).toBe(true);
      expect(result.deleted_replies_count).toBe(5);
    });

    it('should handle deletion of session with no replies', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Session deleted successfully',
          deleted_replies_count: 0
        }
      };

      mockedApi.delete.mockResolvedValueOnce(mockResponse);

      const sessionGuid = 'empty-session';

      const result = await deleteQuizSession(sessionGuid);

      expect(result.success).toBe(true);
      expect(result.deleted_replies_count).toBe(0);
    });
  });

  describe('Error handling edge cases', () => {
    it('should handle server errors (500)', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { message: 'Internal server error' }
        },
        message: 'Request failed with status code 500'
      };

      mockedApi.delete.mockRejectedValueOnce(serverError);

      const sessionGuid = 'server-error-session';

      await expect(deleteQuizSession(sessionGuid)).rejects.toEqual(serverError);
    });

    it('should handle unauthorized errors (401)', async () => {
      const authError = {
        response: {
          status: 401,
          data: { message: 'Unauthorized' }
        },
        message: 'Request failed with status code 401'
      };

      mockedApi.delete.mockRejectedValueOnce(authError);

      const sessionGuid = 'unauthorized-session';

      await expect(deleteQuizSession(sessionGuid)).rejects.toEqual(authError);
    });

    it('should handle forbidden errors (403)', async () => {
      const forbiddenError = {
        response: {
          status: 403,
          data: { message: 'Forbidden - cannot delete completed session' }
        },
        message: 'Request failed with status code 403'
      };

      mockedApi.delete.mockRejectedValueOnce(forbiddenError);

      const sessionGuid = 'completed-session';

      await expect(deleteQuizSession(sessionGuid)).rejects.toEqual(forbiddenError);
    });
  });
});

// Example test data for documentation
export const mockSessionDeletionData = {
  successful_deletion: {
    success: true,
    message: 'Session deleted successfully',
    deleted_session_guid: 'example-session-guid',
    deleted_replies_count: 3
  },
  error_responses: {
    not_found: {
      status: 404,
      message: 'Session not found'
    },
    forbidden: {
      status: 403,
      message: 'Cannot delete completed session'
    },
    server_error: {
      status: 500,
      message: 'Internal server error'
    }
  }
};
