import React from "react";
import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import Home from "./screens/Home";
import "./App.css";
import Quiz from "./screens/Quiz";
import Signup from "./screens/Signup";
import NotFound from "./screens/NotFound";
import Recommendations from "./screens/Dashboard/Recommendations";
import ReportHistory from "./screens/Dashboard/ReportHistory";
import Account from "./screens/Dashboard/Account";
import { Provider } from "react-redux";
import { store } from "./redux/store";
import { ToastContainer } from "react-toastify";
import ForgotPassword from "./screens/Forgot";
import ResetPassword from "./screens/ResetPassword";
// Login uses the same component as Signup, differentiated by URL path
import HealthDashboard from "./screens/Play";
import UserDashboard from "./screens/Boots";
import Dashboard from "./screens/Dashboard/Home";
import Feedback from "./screens/Dashboard/Feedback";
import Showcase from "./screens/Showcase";
import Products from "./screens/Products";
import CameraPage from "./screens/Camera";
import CameraPageVoice from "./screens/CameraPage";
import Analysis from "./screens/Analysis";
import { QueryClientProvider } from "@tanstack/react-query";
import ErrorBoundary from "./components/ErrorBoundary";
import { useErrorReporter } from "./components/ErrorReporter";
import { setGlobalErrorReporter } from "./services/errorHandler";
import { ErrorProvider } from "./contexts/ErrorContext";
import ErrorNotifications from "./components/ErrorNotifications";
import ModernLandingPreview from "./screens/Home/ModernLandingPreview";
import ABTestLanding from "./screens/Home/ABTestLanding";
import ABTestDashboard from "./screens/ABTestDashboard";
import ABTestControls from "./components/ABTestControls";

// Modern Components (Lazy Loaded)
import { LazyWrapper } from "./components/ui/LazyComponents";
import { PageLoader } from "./components/ui/LoadingStates";
import { queryClient as modernQueryClient } from "./services/modernAPI";

// Lazy load modern components for better performance
// const ModernDashboard = React.lazy(() => import("./screens/ModernDashboard"));
const ModernHome = React.lazy(() => import("./screens/ModernHome"));
// const ModernLogin = React.lazy(() => import("./components/auth/ModernAuth").then(module => ({ default: module.ModernLogin })));
// const ModernRegister = React.lazy(() => import("./components/auth/ModernAuth").then(module => ({ default: module.ModernRegister })));

// Use the modern query client for better integration
const queryClient = modernQueryClient;

function App() {
  const { addError, ErrorReporterComponent } = useErrorReporter();

  // Set up global error reporter for development
  React.useEffect(() => {
    setGlobalErrorReporter(addError);
  }, [addError]);

  // Navigation handler for modern components
  const handleNavigation = (path: string) => {
    window.location.href = path;
  };

  return (
    <ErrorBoundary>
      <ErrorProvider>
        <Provider store={store}>
          <QueryClientProvider client={queryClient}>
            <ToastContainer />
            <ErrorNotifications />
            <ABTestControls />
            <Router>
              <Routes>
                <Route path="" element={<ABTestLanding />} />
                <Route path="/home-original" element={<Home />} />
                <Route path="/modern" element={
                  <LazyWrapper fallback={<PageLoader text="Loading home page..." />}>
                    <ModernHome onNavigate={handleNavigation} />
                  </LazyWrapper>
                } />
                <Route path="/landing-preview" element={<ModernLandingPreview />} />
                <Route path="/ab-test-dashboard" element={<ABTestDashboard />} />
                <Route path="/quiz" element={<Quiz />} />
                <Route path="/signup" element={<Signup />} />
                <Route path="/login" element={<Signup />} />
                <Route path="/forgot" element={<ForgotPassword />} />
                <Route path="/reset/:token/:uid" element={<ResetPassword />} />
                <Route path="/dashboard" element={<Dashboard />} />
                {/* <Route path="/moderndashboard" element={
                  <LazyWrapper fallback={<PageLoader text="Loading dashboard..." />}>
                    <ModernDashboard />
                  </LazyWrapper>
                } /> */}
                {/* <Route path="/modern-login" element={
                  <LazyWrapper fallback={<PageLoader text="Loading login..." />}>
                    <ModernLogin onSuccess={() => handleNavigation('/moderndashboard')} />
                  </LazyWrapper>
                } /> */}
                {/* <Route path="/modern-register" element={
                  <LazyWrapper fallback={<PageLoader text="Loading registration..." />}>
                    <ModernRegister onSuccess={() => handleNavigation('/modern-login')} />
                  </LazyWrapper>
                } /> */}
                <Route path="/showcase" element={<Showcase />} />
                <Route path="/products" element={<Products />} />
                <Route path="/capture" element={<CameraPage />} />
                <Route path="/cameras" element={<CameraPageVoice />} />
                <Route path="/analysis" element={<Analysis />} />
                <Route path="/play" element={<HealthDashboard />} />
                <Route
                  path="/dashboard/recommendations"
                  element={<Recommendations />}
                />
                <Route path="/dashboard/reporthistory" element={<ReportHistory />} />
                <Route path="/dashboard/account" element={<Account />} />
                <Route path="/feedback" element={<Feedback />} />
                <Route path="/flex" element={<HealthDashboard />} />
                <Route path="/analytics" element={<UserDashboard />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </Router>
          </QueryClientProvider>
        </Provider>
        {ErrorReporterComponent}
      </ErrorProvider>
    </ErrorBoundary>
  );
}

export default App;
