/**
 * Modern API client for Cosmetrics AI
 * 
 * Integrates with the optimized backend APIs and provides
 * type-safe, performant data fetching with caching.
 */

import { QueryClient } from '@tanstack/react-query';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_BASE || '/api';
const API_TIMEOUT = 10000; // 10 seconds

// Types
export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  is_active: boolean;
  date_joined: string;
}

export interface UserProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  completed_questions: number;
  questionaire_completed: boolean;
  hair_type?: string;
  hair_image?: string;
}

export interface Product {
  id: number;
  name: string;
  price: number;
  category?: string;
  hairtype?: string;
  porosity?: string;
  texture?: string;
  image_url?: string;
  rating?: string;
}

export interface Recommendation {
  id: number;
  session_guid: string;
  created_at: string;
  shampoos_recs: string[];
  conditioners_recs: string[];
  products?: Product[];
}

export interface HairScoreReport {
  id: number;
  user_id: number;
  session_guid: string;
  data: {
    overall_score?: number;
    damage_score_percentage?: number;
    dry_score_percentage?: number;
    sensitivity_percentage?: number;
  };
  created_at: string;
}

export interface APIResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: Record<string, string[]>;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  access: string;
  refresh: string;
  user: User;
}

export interface RegisterRequest {
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
}

// API Client Class
export class ModernAPIClient {
  private baseURL: string;
  private timeout: number;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  constructor(baseURL: string = API_BASE_URL, timeout: number = API_TIMEOUT) {
    this.baseURL = baseURL;
    this.timeout = timeout;
    this.loadTokensFromStorage();
  }

  // Method to clear all tokens (useful for debugging)
  clearAllTokens() {
    this.clearTokens();
    // Also clear any other token storage formats
    localStorage.removeItem('auth');
  }

  // Token Management
  private loadTokensFromStorage() {
    this.accessToken = localStorage.getItem('access_token');
    this.refreshToken = localStorage.getItem('refresh_token');
  }

  private saveTokensToStorage(access: string, refresh: string) {
    this.accessToken = access;
    this.refreshToken = refresh;
    localStorage.setItem('access_token', access);
    localStorage.setItem('refresh_token', refresh);
  }

  private clearTokens() {
    this.accessToken = null;
    this.refreshToken = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  // Core Request Method
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    // Convert HeadersInit to Record<string, string>
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Safely merge headers from options
    if (options.headers) {
      if (options.headers instanceof Headers) {
        // Convert Headers object to Record<string, string>
        options.headers.forEach((value, key) => {
          headers[key] = value;
        });
      } else if (Array.isArray(options.headers)) {
        // Convert string[][] to Record<string, string>
        options.headers.forEach(([key, value]) => {
          headers[key] = value;
        });
      } else {
        // It's already a Record<string, string>
        Object.assign(headers, options.headers);
      }
    }

    // Add authorization header if token exists
    if (this.accessToken && !headers['Authorization']) {
      headers['Authorization'] = `Bearer ${this.accessToken}`;
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Handle 401 - try to refresh token
      if (response.status === 401 && this.refreshToken) {
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
          // Retry the original request
          headers['Authorization'] = `Bearer ${this.accessToken}`;
          const retryResponse = await fetch(url, {
            ...options,
            headers,
          });

          if (!retryResponse.ok) {
            throw new Error(`HTTP ${retryResponse.status}: ${retryResponse.statusText}`);
          }

          return await retryResponse.json();
        } else {
          this.clearTokens();
          throw new Error('Authentication failed');
        }
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      throw error;
    }
  }

  // Authentication Methods
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.request<LoginResponse>('/users/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
      headers: { 'Authorization': '' }, // Don't send any existing token for login
    });

    if (response.success && response.access && response.refresh) {
      this.saveTokensToStorage(response.access, response.refresh);
    }

    return response;
  }

  async register(userData: RegisterRequest): Promise<APIResponse<User>> {
    return this.request<APIResponse<User>>('/users/register', {
      method: 'POST',
      body: JSON.stringify(userData),
      headers: { 'Authorization': '' }, // Don't send any existing token for registration
    });
  }

  async logout(): Promise<void> {
    try {
      await this.request('/users/logout', { method: 'POST' });
    } finally {
      this.clearTokens();
    }
  }

  private async refreshAccessToken(): Promise<boolean> {
    if (!this.refreshToken) return false;

    try {
      const response = await this.request<{ access: string; }>('/users/refresh', {
        method: 'POST',
        body: JSON.stringify({ refresh: this.refreshToken }),
        headers: { 'Authorization': '' }, // Don't send access token for refresh
      });

      if (response.access) {
        this.accessToken = response.access;
        localStorage.setItem('access_token', response.access);
        return true;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }

    return false;
  }

  // User Methods
  async getCurrentUser(): Promise<User> {
    return this.request<User>('/users/me');
  }

  async getUserProfile(userId: number): Promise<UserProfile> {
    return this.request<UserProfile>(`/users/${userId}/profile`);
  }

  async updateUserProfile(userId: number, data: Partial<UserProfile>): Promise<UserProfile> {
    return this.request<UserProfile>(`/users/${userId}/profile`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  // Product Methods
  async getProducts(filters?: {
    category?: string;
    hairtype?: string;
    porosity?: string;
    price_min?: number;
    price_max?: number;
    page?: number;
    page_size?: number;
  }): Promise<Product[]> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });
    }

    const endpoint = `/products/${params.toString() ? `?${params.toString()}` : ''}`;
    return this.request<Product[]>(endpoint);
  }

  async getProduct(productId: number): Promise<Product> {
    return this.request<Product>(`/products/${productId}`);
  }

  // Recommendation Methods
  async getRecommendations(userId?: number): Promise<Recommendation[]> {
    const endpoint = userId ? `/recommendations/?user_id=${userId}` : '/recommendations/';
    return this.request<Recommendation[]>(endpoint);
  }

  async generateRecommendations(userId: number, preferences?: any): Promise<Recommendation> {
    return this.request<Recommendation>('/recommendations/generate', {
      method: 'POST',
      body: JSON.stringify({ user_id: userId, preferences }),
    });
  }

  // Hair Score Methods
  async getHairScores(userId: number, sessionGuid?: string): Promise<HairScoreReport[]> {
    const params = new URLSearchParams({ user_id: userId.toString() });
    if (sessionGuid) {
      params.append('session_guid', sessionGuid);
    }

    return this.request<HairScoreReport[]>(`/reports/?${params.toString()}`);
  }

  // Quiz Methods
  async getQuestionnaires(): Promise<any[]> {
    return this.request<any[]>('/quiz/');
  }

  async submitQuizResponse(data: any): Promise<any> {
    return this.request<any>('/quiz/submit', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Recommendation Methods
  async generateRecommendations(userId: number, sessionGuid: string): Promise<any> {
    return this.request<any>(`/recommendations/generate/user/${userId}?session_guid=${sessionGuid}`, {
      method: 'POST',
    });
  }

  async getUserRecommendations(userId: number): Promise<any[]> {
    return this.request<any[]>(`/recommendations/user/${userId}`);
  }

  async getRecommendationBySession(userId: number, sessionGuid: string): Promise<any> {
    return this.request<any>(`/recommendations/user/${userId}/session/${sessionGuid}`);
  }

  // Health Check
  async healthCheck(): Promise<{ status: string; timestamp: string; }> {
    return this.request<{ status: string; timestamp: string; }>('/health');
  }

  // Utility Methods
  isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  getAccessToken(): string | null {
    return this.accessToken;
  }
}

// Create singleton instance
export const apiClient = new ModernAPIClient();

// Query Client Configuration for React Query
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime)
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors except 401
        if (error instanceof Error && error.message.includes('HTTP 4')) {
          return error.message.includes('401') ? failureCount < 1 : false;
        }
        return failureCount < 3;
      },
    },
    mutations: {
      retry: 1,
    },
  },
});

export default apiClient;
