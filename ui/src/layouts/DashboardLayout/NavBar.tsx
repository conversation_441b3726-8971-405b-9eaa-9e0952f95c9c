import React, { useCallback } from "react";
import { NavLink, useNavigate } from "react-router-dom";
import styled, { css } from "styled-components";
import {
  FastArrowLeft,
  HomeSimple,
  QuoteMessage,
  Reports,
  Star,
  SubmitDocument,
  User,
  LogOut,
  Refresh,
  ShoppingBag,
} from "iconoir-react";

import CosmetricsLogo from "../../assets/images/cosmetrics-logo.svg";
import { useUserActions } from "../../hooks/user.actions";

/**
 * Breakpoints – tweak per design system.
 */
const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
};

/**
 * Modern Drawer (sidebar) – blends with dashboard design
 */
const Drawer = styled.aside<{ $open: boolean; }>`
  position: fixed;
  inset: 0 auto 0 0;
  height: 100dvh;
  width: clamp(280px, 48vw, 320px);
  background: #FFFFFF;
  color: #1F2937;
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  z-index: 10001;
  transition: all 0.3s ease;
  border-radius: 0 32px 32px 0;
  overflow: hidden;
  transform: translateX(${({ $open }) => ($open ? "0" : "-100%")});
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid #E2E8F0;

  /* Subtle gradient overlay */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(67, 0, 255, 0.02) 0%, rgba(255, 67, 0, 0.02) 100%);
    pointer-events: none;
  }

  /* desktop – always visible */
  @media (min-width: ${BREAKPOINTS.tablet}px) {
    transform: translateX(0);
    position: sticky;
    border-radius: 0 24px 24px 0;
    margin: 16px 0 16px 16px;
    height: calc(100vh - 32px);
  }
`;

const Logo = styled.img`
  margin: 0 auto 3rem auto;
  width: 85%;
  max-width: 240px;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
  position: relative;
  z-index: 1;
`;

const CloseBtn = styled.button`
  align-self: flex-end;
  margin-bottom: 1.5rem;
  background: #F8FAFC;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  color: #4B5563;
  cursor: pointer;
  padding: 8px;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;

  svg {
    width: 20px;
    height: 20px;
  }

  &:hover {
    background: #4300FF;
    color: white;
    transform: translateX(-2px);
    border-color: #4300FF;
  }

  @media (min-width: ${BREAKPOINTS.tablet}px) {
    display: none; /* hide on desktop */
  }
`;

/**
 * Modern navigation item styles.
 */
const itemStyles = css`
  position: relative;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.25rem;
  font-size: 0.9375rem; /* 15px */
  font-weight: 500;
  letter-spacing: 0.2px;
  border-radius: 16px;
  text-decoration: none;
  color: #4B5563;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.5rem;
  backdrop-filter: blur(10px);
  z-index: 1;

  & svg {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    color: #6B7280;
    transition: all 0.2s ease;
  }

  &:hover {
    background: linear-gradient(135deg, #4300FF 0%, #ff4300 100%);
    color: #ffffff;
    transform: translateX(4px);
    box-shadow: 0 8px 25px rgba(67, 0, 255, 0.3);

    & svg {
      color: #ffffff;
      transform: scale(1.05);
    }
  }
`;

/**
 * Modern active indicator.
 */
const activeIndicator = css`
  content: "";
  position: absolute;
  left: -1.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: linear-gradient(180deg, #4300FF 0%, #ff4300 100%);
  border-radius: 0 4px 4px 0;
  box-shadow: 0 2px 8px rgba(67, 0, 255, 0.3);
`;

const NavItem = styled(NavLink)`
  ${itemStyles}

  &.active {
    background: linear-gradient(135deg, #4300FF 0%, #ff4300 100%);
    color: #ffffff;
    font-weight: 600;
    transform: translateX(4px);
    box-shadow: 0 8px 32px rgba(67, 0, 255, 0.3);
    border: 1px solid rgba(67, 0, 255, 0.2);

    & svg {
      color: #ffffff;
      transform: scale(1.1);
    }
  }

  &.active::before {
    ${activeIndicator}
  }
`;

const ActionItem = styled.button`
  ${itemStyles}
  background: none;
  border: none;
  text-align: left;

  &:hover {
    background: #EF4444;
    color: #ffffff;
    transform: translateX(4px);

    & svg {
      opacity: 1;
      transform: scale(1.05);
    }
  }
`;

const NavSection = styled.nav`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
  position: relative;
  z-index: 1;
`;

interface LinkItem {
  path: string;
  label: string;
  Icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const primaryLinks: LinkItem[] = [
  { path: "/dashboard", label: "Hair Analysis", Icon: Reports },
  { path: "/dashboard/recommendations", label: "Recommendations", Icon: Star },
  { path: "/dashboard/reporthistory", label: "Report History", Icon: SubmitDocument },
  { path: "/dashboard/account", label: "Profile", Icon: User },
  { path: "/quiz", label: "Take Quiz", Icon: Refresh },
  { path: "/products", label: "products", Icon: ShoppingBag },
];

const secondaryLinks: LinkItem[] = [
  { path: "/", label: "Cosmetrics Home", Icon: HomeSimple },
  { path: "/feedback", label: "Give Feedback", Icon: QuoteMessage },
];

interface Props {
  /**
   * Controls if the drawer is visible on touch devices.
   */
  open?: boolean;
  onToggle?: () => void;
}

const NavBar: React.FC<Props> = ({ open = true, onToggle }) => {
  const navigate = useNavigate();
  const { logout } = useUserActions(navigate);

  const handleLogout = useCallback(async () => {
    await logout();
  }, [logout]);

  return (
    <Drawer $open={open} className="rounded-2 py-4 my-4">
      {/* Collapse only on touch devices */}
      <CloseBtn aria-label="Close navigation" onClick={onToggle}>
        <FastArrowLeft />
      </CloseBtn>

      <Logo src={CosmetricsLogo} alt="Cosmetrics logo" />

      <NavSection aria-label="Primary navigation">
        {primaryLinks.map(({ path, label, Icon }) => (
          <NavItem key={path} to={path} end>
            <Icon />
            {label}
          </NavItem>
        ))}
      </NavSection>

      <NavSection
        aria-label="Secondary navigation"
        style={{
          borderTop: "1px solid #E2E8F0",
          paddingTop: "1.5rem",
          marginTop: "1rem"
        }}
      >
        {secondaryLinks.map(({ path, label, Icon }) => (
          <NavItem key={path} to={path} end>
            <Icon />
            {label}
          </NavItem>
        ))}
        <ActionItem onClick={handleLogout} type="button">
          <LogOut />
          Logout
        </ActionItem>
      </NavSection>
    </Drawer>
  );
};

export default React.memo(NavBar);
