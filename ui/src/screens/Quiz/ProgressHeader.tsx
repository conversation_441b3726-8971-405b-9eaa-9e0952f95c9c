import React, { useMemo } from 'react';
import styled from 'styled-components';
import { CheckCircle, Circle } from 'lucide-react';
import Column from '../../components/Column';
import colors from '../../styles/colors';
import Row from '../../components/Row';

const Root = styled.div`
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 2rem;
    background: #FFFFFF;
    border-bottom: 1px solid #E2E8F0;

    .progress-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 2rem;
    }

    .step-info {
        flex: 1;
    }

    h1 {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1F2937;
        margin: 0 0 0.5rem 0;
        padding: 0;
    }

    h5 {
        font-size: 1rem;
        font-weight: 400;
        color: #6B7280;
        margin: 0;
        padding: 0;
        line-height: 1.5;
    }

    @media only screen and (max-width: 768px) {
        padding: 1.5rem 1rem;

        .progress-content {
            flex-direction: column;
            gap: 1.5rem;
        }

        h1 {
            font-size: 1.5rem;
        }

        h5 {
            font-size: 0.875rem;
        }
    }
`;

const ProgressHeaderIndex = styled.div`
    display: flex;
    flex-direction: column;
    position: relative;
    background: #F8FAFC;
    border: 1px solid #E2E8F0;
    padding: 1.5rem;
    gap: 1rem;
    border-radius: 16px;
    box-sizing: border-box;
    min-width: 400px;

    @media only screen and (max-width: 768px) {
        width: 100%;
        min-width: unset;
        padding: 1rem;
    }
`;

const ProgressHeaderTab = styled.div<{ $clickable?: boolean; }>`
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    transition: all 0.2s ease;
    flex: 1;
    min-height: 44px;
    cursor: ${props => props.$clickable ? 'pointer' : 'default'};

    ${props => props.$clickable && `
        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    `}

    &.active {
        background: ${colors.PRIMARY.primary};
        color: white;
        box-shadow: 0 4px 12px rgba(67, 0, 255, 0.3);
    }

    &.completed {
        background: #10B981;
        color: white;
    }

    &.upcoming {
        background: #E5E7EB;
        color: #6B7280;
    }

    span {
        font-size: 0.875rem;
        font-weight: 600;
    }

    @media only screen and (max-width: 768px) {
        padding: 0.5rem 0.75rem;

        span {
            font-size: 0.75rem;
        }
    }
`;

const ProgressBar = styled.div`
    display: flex;
    height: 8px;
    width: 100%;
    background-color: #E5E7EB;
    border-radius: 4px;
    overflow: hidden;

    .progress-fill {
        height: 100%;
        background: ${colors.PRIMARY.primary};
        border-radius: 4px;
        transition: width 0.3s ease;
    }
`;

const ProgressInfo = styled.div`
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;

    .progress-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: #1F2937;
    }

    .progress-percentage {
        font-size: 0.875rem;
        font-weight: 600;
        color: ${colors.PRIMARY.primary};
    }
`;

const titleMap: { [key: number]: string; } = {
    0: 'Your Hair, Your Goals',
    1: 'Your Hair, Your Goals',
    2: 'Upload Pictures',
    3: 'Extra Information',
    4: 'Medical Conditions',
    5: 'Lifestyle',
    6: 'Hair Care',
    7: 'A little about you',
};

const subTitleMap: { [key: number]: string; } = {
    0: 'The following questions will help us to get a picture of you!',
    1: 'The following questions will help us to get a picture of you!',
    2: 'Your x3 images should be clear & high quality !',
    3: 'Please share any relevant information and observations about your hair E.g. Multiple hair types, Problems,  Reactions, Behaviour, treatment history, etc',
    4: 'The following questions will help us to get a picture of you!',
    5: 'The following questions will help us to get a picture of you!',
    6: 'The following questions will help us to get a picture of you!',
    7: 'The following questions will help us to get a picture of you!',
};

interface ProgressHeaderProps {
    index?: number;
    answers?: any;
    totalQuestions?: number;
    onStepClick?: (step: number) => void;
}

const ProgressHeader: React.FC<ProgressHeaderProps> = ({
    index = 0,
    answers = {},
    totalQuestions = 0,
    onStepClick,
}) => {

    const title = useMemo(() => titleMap[index], [index]);
    const subTitle = useMemo(() => subTitleMap[index], [index]);

    const progress = useMemo(() => {
        const answerEntries = Object.values(answers).length;

        return (answerEntries / totalQuestions) * 100;
        // return percentage;
    }, [answers, totalQuestions]);

    const getStepStatus = (stepIndex: number) => {
        if (stepIndex < index) return 'completed';
        if (stepIndex === index) return 'active';
        return 'upcoming';
    };

    const steps = [
        { label: 'Hair Details', range: [1, 3] },
        { label: 'Medical', range: [4, 4] },
        { label: 'Lifestyle', range: [5, 5] },
        { label: 'Hair Care', range: [6, 7] }
    ];

    return (
        <Root>
            <div className="progress-content">
                <div className="step-info">
                    <h1>{title}</h1>
                    <h5>{subTitle}</h5>
                </div>

                <ProgressHeaderIndex>
                    <ProgressInfo>
                        <span className="progress-label">Progress</span>
                        <span className="progress-percentage">{Math.round(progress)}% Complete</span>
                    </ProgressInfo>
                    <ProgressBar>
                        <div className="progress-fill" style={{ width: `${progress}%` }} />
                    </ProgressBar>

                    <Row style={{ gap: '0.5rem', marginTop: '1rem' }}>
                        {steps.map((step, stepIndex) => {
                            const status = getStepStatus(stepIndex + 1);
                            const isInRange = index >= step.range[0] && index <= step.range[1];
                            const isClickable = onStepClick && (status === 'completed' || status === 'active');

                            return (
                                <ProgressHeaderTab
                                    key={step.label}
                                    className={isInRange ? 'active' : status}
                                    $clickable={isClickable}
                                    onClick={() => isClickable && onStepClick(step.range[0])}
                                    title={isClickable ? `Go to ${step.label}` : undefined}
                                >
                                    {status === 'completed' ? (
                                        <CheckCircle size={16} />
                                    ) : (
                                        <Circle size={16} />
                                    )}
                                    <span>{step.label}</span>
                                </ProgressHeaderTab>
                            );
                        })}
                    </Row>
                </ProgressHeaderIndex>
            </div>
        </Root>
    );
};

export default ProgressHeader;
