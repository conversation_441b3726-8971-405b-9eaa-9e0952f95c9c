import styled from "styled-components";
import { QuizPageRoot } from "../../style";
import Button from "../../../../components/Button";
import BlurOverlay from "./BlurOverlay";
import ProgressBar from "../../../../components/ProgressBar";
import Row from "../../../../components/Row";
import Column from "../../../../components/Column";
import { useCallback, useMemo, useState } from "react";
import { useAppSelector, useAppDispatch } from "../../../../redux/hooks";
import { uploadQuestionaire, generateRecommendations } from "../../../../apis";
import { markQuizCompleted } from "../../../../redux/QuizSlice";
import { useQuizSessionPreservation } from "../../../../hooks/useQuizSessionPreservation";
import QuizPaywall from "../../../../components/QuizPaywall";
import React from "react";
import { toast } from "react-toastify";

const GridRow = styled.div`
  display: flex;
  flex: 1;
  flex-direction: row;

  @media only screen and (max-width: 768px) {
    flex-direction: column;
  }
`;

const GridBox = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 2.5%;

  button {
    width: 200px;
    align-self: flex-end;
    vertical-align: bottom;
    background-color: #78d3f7;
  }

  @media only screen and (max-width: 768px) {
    p {
      font-size: 0.875em;
    }
  }
`;

const ScoreBox = styled(GridBox)`
  flex: 1.4;
  background-color: #e7f7fb;
  border-radius: 24px;
  padding: 2.5% 2.5% 7% 2.5%;
  position: relative;
  min-height: 25vw;
  justify-content: space-between;
  box-sizing: border-box;

  h1 {
    color: #303445;
    margin: 0;
    padding: 0;
    font-size: 1.8em;
    font-weight: 600;
  }

  span {
    width: 100px;
  }

  @media only screen and (max-width: 768px) {
    padding: 5vw;
    gap: 3vw;

    h1 {
      font-size: 1.275em;
    }

    span {
      font-size: 0.875em;
    }
  }
`;

const FlexEnd = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
  flex: 1;

  button {
    width: auto;
  }
`;

const RecommendationBox = styled.div`
  position: relative;

  h1 {
    margin: 70px 0 40px 0;
    padding: 0;
  }

  @media only screen and (max-width: 768px) {
    h1 {
      font-size: 1.575em;
    }
  }
`;

const ListContainer = styled.div`
  display: flex;
  position: relative;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  position: relative;
  gap: 5vw;

  img {
    resize: vertical;
    width: 50%;
    aspect-ratio: 0.8;
  }

  @media only screen and (max-width: 768px) {
    flex-direction: column;

    img {
      width: 70%;
      aspect-ratio: 0.7;
    }
  }
`;

const Results: React.FC<QuizPageProps> = React.memo(() => {
  const dispatch = useAppDispatch();
  const [showPaywall, setShowPaywall] = useState(false);
  const [products] = useState([
    {
      image: require("../../../../assets/images/product-1.png"),
    },
    {
      image: require("../../../../assets/images/product-2.png"),
    },
    {
      image: require("../../../../assets/images/product-3.png"),
    },
    {
      image: require("../../../../assets/images/product-4.png"),
    },
  ]);

  const sessionGuid = useAppSelector((state) => state.quiz.session_guid) || "";
  const answers = useAppSelector((state) => state.quiz);
  const { preserveQuizSession } = useQuizSessionPreservation();

  const handleCompleteQuiz = async (userId: string) => {
    // Check if user is authenticated
    if (!userId) {
      // Preserve quiz session and show paywall
      preserveQuizSession();
      setShowPaywall(true);
      return;
    }

    try {
      // Step 1: Upload quiz responses
      console.log('Uploading quiz responses...');
      await uploadQuestionaire(userId, answers, "", sessionGuid);

      // Step 2: Generate recommendations after successful quiz submission
      console.log('Generating recommendations...');
      try {
        await generateRecommendations(userId, sessionGuid);
        toast.success('Quiz completed and personalized recommendations generated!');
      } catch (recommendationError) {
        console.error('Error generating recommendations:', recommendationError);
        toast.warning('Quiz completed successfully, but recommendation generation failed. You can try again later.');
        // Continue with quiz completion even if recommendations fail
      }

      // Step 3: Mark quiz as completed in Redux
      dispatch(markQuizCompleted());

      console.log('Quiz completion process finished!');
    } catch (error) {
      console.error('Error during quiz completion process:', error);

      // Check if this is a quiz upload error or recommendation error
      if (error.message && error.message.includes('recommendation')) {
        // Recommendation error - quiz was uploaded successfully
        dispatch(markQuizCompleted());
        toast.warning('Quiz completed, but we couldn\'t generate recommendations right now. Please check your dashboard later.');
      } else {
        // Quiz upload error
        toast.error('Failed to submit quiz. Please try again.');
      }

      // If submission fails, still show paywall for anonymous users
      if (!userId) {
        preserveQuizSession();
        setShowPaywall(true);
      }
    }
  };

  const handleLogin = () => {
    // Preserve session before redirecting to login
    preserveQuizSession();
    window.location.href = '/login?redirect=/quiz/results';
  };

  const handleSignup = () => {
    // Preserve session before redirecting to signup
    preserveQuizSession();
    window.location.href = '/register?redirect=/quiz/results';
  };

  const handleClosePaywall = () => {
    setShowPaywall(false);
  };

  // Check authentication
  const auth = JSON.parse(localStorage.getItem("auth") || "{}");
  const user = auth?.user;

  // Auto-submit quiz for authenticated users
  React.useEffect(() => {
    if (user && user.id) {
      handleCompleteQuiz(user.id.toString());
      setTimeout(() => {
        window.location.href = "/dashboard";
      }, 2000);
    }
  }, [user]);

  const results = useMemo(() => {
    return {
      dryness: 85,
      damage: 5,
      oil: 55,
      scalp: 75,
      flakes: 30,
    };
  }, []);

  return (
    <>
      <QuizPageRoot>
        <GridRow>
          <ScoreBox>
            <h1>Hair Condition Scores</h1>

            <Column style={{ gap: 12 }}>
              <Row>
                <span>Dryness</span>
                <ProgressBar value={results.dryness} />
              </Row>
              <Row>
                <span>Damage</span>
                <ProgressBar value={results.damage} />
              </Row>
              <Column style={{ position: "relative", gap: 12 }}>
                {!user && <BlurOverlay />}

                <Row>
                  <span>Sebum Oil</span>
                  <ProgressBar value={results.oil} />
                </Row>
                <Row>
                  <span>Dry Scalp</span>
                  <ProgressBar value={results.scalp} />
                </Row>
                <Row>
                  <span>Flakes</span>
                  <ProgressBar value={results.flakes} />
                </Row>
              </Column>
            </Column>
          </ScoreBox>
          <GridBox>
            <p>
              Our smart system uses your data to calculate a score for each area
              mentioned.
            </p>
            <p>These scores provide an indication of your hair condition.</p>
            <p>
              Each score highlights how well or not so well your hair and scalp is
              doing at the moment.
            </p>
            <p>
              The analysis helps the system identify the best products to match
              your current needs.
            </p>
            {!user && (
              <FlexEnd>
                <Button onClick={handleSignup}>
                  {"Sign Up for Full Report"}
                </Button>
              </FlexEnd>
            )}
          </GridBox>
        </GridRow>

        <RecommendationBox>
          <h1>Product Recommendations for Dryness</h1>

          <ListContainer>
            {!user && (
              <BlurOverlay
                buttonLabel="Sign Up for Recommendations"
                onButtonPress={handleSignup}
              />
            )}

            {products &&
              products.map((item, index) => {
                return <img key={index} src={item.image} alt="" />;
              })}
          </ListContainer>
        </RecommendationBox>
      </QuizPageRoot>

      {/* Show paywall for anonymous users */}
      {showPaywall && (
        <QuizPaywall
          onLogin={handleLogin}
          onSignup={handleSignup}
          onClose={handleClosePaywall}
        />
      )}
    </>
  );
});

export default Results;
