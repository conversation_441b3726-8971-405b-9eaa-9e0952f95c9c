import styled from "styled-components";
import GridRow from "../../../../components/GridRow";
import PhotoStandards from "../../../../components/PhotoStandards";
import Button from "../../../../components/Button";
import { Camera, Trash, Upload, CheckCircle, Refresh } from "iconoir-react";
import LiveCamera from "./LiveCamera";
import React, { useState, useEffect } from "react";
import { Col } from "react-bootstrap";
import { toast } from "react-toastify";
// import { v4 as uuidv4 } from "uuid";
import { useAppSelector, useAppDispatch } from "../../../../redux/hooks";
import { setUploadedPhotosCount, setHasExistingPhotos } from "../../../../redux/QuizSlice";
import { handleError } from "../../../../services/errorHandler";
import colors from "../../../../styles/colors";
import { useExistingPhotos } from "../../../../hooks/useExistingPhotos";

import {
  // uploadQuestionaireReply,
  uploadQuestionaireReplyImages,
} from "../../../../hooks/user.actions";

const Root = styled.div`
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: calc(100vh - 240px);
  width: 100vw;
  padding: 2rem;
  background: #FAFBFC;

  @media only screen and (max-width: 768px) {
    padding: 1rem;
  }
`;

const CompactLayout = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
  max-width: 1200px;
  margin: 0 auto;

  @media only screen and (max-width: 968px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
`;

const UploadSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const PhotoStandardsSection = styled.div`
  background: #FFFFFF;
  border: 1px solid #E5E7EB;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  @media only screen and (max-width: 968px) {
    padding: 1.5rem;

    h3 {
      font-size: 1.125rem;
    }
  }
`;

const UploadBox = styled.div`
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  border: 2px dashed #D1D5DB;
  border-radius: 16px;
  padding: 3rem 2rem;
  text-align: center;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${colors.PRIMARY.primary};
    background: ${colors.PRIMARY.pastel};
  }

  .upload-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 1rem;
    padding: 12px;
    background: ${colors.PRIMARY.pastel};
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  h4 {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.5rem;
  }

  p {
    font-size: 0.875rem;
    color: #6B7280;
    margin-bottom: 1.5rem;
    line-height: 1.5;
  }

  input {
    display: none;
  }

  @media only screen and (max-width: 768px) {
    padding: 2rem 1.5rem;

    h4 {
      font-size: 1rem;
    }

    p {
      font-size: 0.8125rem;
    }
  }
`;

const ActionButtons = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;

  .divider {
    font-size: 0.875rem;
    color: #6B7280;
    font-weight: 500;
  }

  @media only screen and (min-width: 480px) {
    flex-direction: row;
    justify-content: center;

    .divider {
      margin: 0 0.5rem;
    }
  }
`;

const Row = styled(GridRow)`
  @media only screen and (max-width: 768px) {
    flex-direction: column-reverse;
  }
`;

const PhotoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;

  @media only screen and (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  @media only screen and (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;

const PhotoExample = styled.div<{ $approved?: boolean; }>`
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid ${props => props.$approved ? colors.GREEN.primary : '#EF4444'};
  background: #F9FAFB;

  img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    display: block;
  }

  .label {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: ${props => props.$approved ? colors.GREEN.primary : '#EF4444'};
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
  }

  @media only screen and (max-width: 768px) {
    img {
      height: 100px;
    }

    .label {
      font-size: 0.6875rem;
      padding: 0.2rem 0.4rem;
    }
  }
`;

interface UploadPhotosProps {
  onPhotosChange?: (count: number) => void;
}

const UploadPhotos: React.FC<UploadPhotosProps> = ({ onPhotosChange }) => {
  const dispatch = useAppDispatch();
  const [showCamera, setShowCamera] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<(string | File)[]>([]);
  const [removedFiles, setRemovedFiles] = useState<(number | File)[]>([]);
  const [imagesUploaded, setImagesUploaded] = useState(false);
  const [showExistingPhotos, setShowExistingPhotos] = useState(true);
  const [replaceMode, setReplaceMode] = useState(false);
  const sessionGuid = useAppSelector((state) => state.quiz.session_guid) || "";

  // Get user info
  const auth = JSON.parse(localStorage.getItem("auth") || "{}");
  const userId = auth?.user?.id;

  // Check for existing photos
  const { existingPhotos, photoStatus, loading: photosLoading, error: photosError, refetch } = useExistingPhotos(userId, sessionGuid);

  // Notify parent component when photos count changes
  useEffect(() => {
    const totalPhotos = showExistingPhotos && photoStatus?.has_photos
      ? photoStatus.count
      : selectedFiles.length;
    onPhotosChange?.(totalPhotos);
  }, [selectedFiles.length, photoStatus, showExistingPhotos, onPhotosChange]);

  // Set initial upload state based on existing photos
  useEffect(() => {
    if (photoStatus?.is_complete && showExistingPhotos) {
      setImagesUploaded(true);
    }
  }, [photoStatus, showExistingPhotos]);

  const handleKeepExistingPhotos = () => {
    setShowExistingPhotos(true);
    setReplaceMode(false);
    setImagesUploaded(photoStatus?.is_complete || false);

    // Update Redux state
    dispatch(setUploadedPhotosCount(photoStatus?.count || 0));
    dispatch(setHasExistingPhotos(true));
  };

  const handleReplacePhotos = () => {
    setShowExistingPhotos(false);
    setReplaceMode(true);
    setImagesUploaded(false);
    setSelectedFiles([]);

    // Update Redux state
    dispatch(setUploadedPhotosCount(0));
    dispatch(setHasExistingPhotos(false));
  };

  const handleFileUpload = () => {
    if (selectedFiles.length >= 3) {
      toast.error("Only 3 images are allowed");
      return;
    }

    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "image/*";
    fileInput.multiple = true;

    fileInput.onchange = (event: Event) => {
      const target = event.target as HTMLInputElement;
      if (target.files && target.files.length > 0) {
        const newFiles = Array.from(target.files).slice(
          0,
          3 - selectedFiles.length
        );
        setSelectedFiles((prevFiles) => [...prevFiles, ...newFiles]);
      }
    };

    fileInput.click();
  };
  // const blobUrlToFile = async (
  //   blobUrl: string,
  //   fileName: string
  // ): Promise<File> => {
  //   const response = await fetch(blobUrl);
  //   const blob = await response.blob();
  //   return new File([blob], fileName, { type: blob.type });
  // };

  const handleSubmit = async () => {
    if (selectedFiles.length === 0) {
      toast.error("Please select or capture at least one image.");
      return;
    }

    // No CSRF token needed for JWT-based authentication
    const csrfToken = "";
    let questionId = 11; // starts from Q11 to Q13
    let user;
    const auth = JSON.parse(localStorage.getItem("auth") || "{}");

    if (auth) {
      user = auth?.user;
    }
    for (const file of selectedFiles) {
      let convertedFile;
      const formData = new FormData();
      if (typeof file === "string") {
        convertedFile = dataURLtoFile(file, `photo_${questionId}.png`);
      } else {
        convertedFile = file;
      }

      formData.append("file", convertedFile);
      formData.append("question_id", String(questionId));
      formData.append("user_id", user ? user?.id : undefined);
      formData.append("session_guid", sessionGuid);

      try {
        // const reply = await uploadQuestionaireReplyImages(formData, csrfToken);
        setImagesUploaded(true);
      } catch (error) {
        setImagesUploaded(false);
        handleError(error, "Image upload failed", {
          context: {
            action: 'image_upload',
            component: 'UploadPhotos',
            metadata: { questionId, fileType: typeof convertedFile === 'string' ? 'unknown' : convertedFile.type }
          }
        });
      }

      questionId++; // Increment for the next file
    }
  };

  const dataURLtoFile = (dataUrl: string | File, filename: string) => {
    if (typeof dataUrl === "string") {
      const arr = dataUrl.split(",");
      const mime = arr[0].match(/:(.*?);/)?.[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);

      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }

      return new File([u8arr], filename, { type: mime });
    }
    return "";
  };

  const handleCapturedImage = (image: string) => {
    if (selectedFiles.length >= 3) {
      toast.error("Only 3 images are allowed");
      return;
    }
    setSelectedFiles((prevFiles) => [...prevFiles, image]);
    // Close camera when we reach 3 photos (length will be 2 before adding the new one)
    if (selectedFiles?.length >= 2) {
      setShowCamera(false);
    }
  };
  const handleHideCamera = () => {
    setShowCamera(false);
  };
  const handleRemoveImage = (index: number) => {
    setRemovedFiles((prev) => [...prev, index]);
    setSelectedFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };
  const photoExamples = [
    { src: require("../../../../assets/images/hair-1.png"), approved: true, label: "Approved" },
    { src: require("../../../../assets/images/hair-2.png"), approved: true, label: "Approved" },
    { src: require("../../../../assets/images/hair-3.png"), approved: true, label: "Approved" },
    { src: require("../../../../assets/images/hair-4.png"), approved: false, label: "Too Close" },
    { src: require("../../../../assets/images/hair-5.png"), approved: false, label: "Too Dark" },
    { src: require("../../../../assets/images/hair-6.png"), approved: false, label: "Background" },
  ];

  return (
    <Root>
      <CompactLayout>
        <UploadSection>
          <UploadBox>
            {photosLoading ? (
              <>
                <div className="upload-icon">
                  <Refresh width={24} height={24} style={{ color: colors.PRIMARY.primary }} />
                </div>
                <h4>Checking for existing photos...</h4>
                <p>Please wait while we check if you have already uploaded photos.</p>
              </>
            ) : photoStatus?.has_photos && showExistingPhotos && !replaceMode ? (
              <>
                <div className="upload-icon">
                  <CheckCircle width={24} height={24} style={{ color: colors.GREEN.primary }} />
                </div>
                <h4>Photos Already Uploaded!</h4>
                <p>
                  We found {photoStatus.count} photo{photoStatus.count > 1 ? 's' : ''} from your previous session.
                  {photoStatus.is_complete
                    ? " You can continue with these photos or take new ones."
                    : ` You need ${3 - photoStatus.count} more photo${3 - photoStatus.count > 1 ? 's' : ''} to complete this step.`
                  }
                </p>

                <ActionButtons>
                  <Button
                    onClick={handleKeepExistingPhotos}
                    style={{
                      background: colors.GREEN.primary,
                      border: 'none',
                      color: 'white',
                      fontWeight: 600,
                      padding: '0.75rem 1.5rem',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}
                  >
                    <CheckCircle width={16} height={16} />
                    {photoStatus.is_complete ? "Continue with these photos" : "Keep existing photos"}
                  </Button>
                  <span className="divider">Or</span>
                  <Button
                    onClick={handleReplacePhotos}
                    style={{
                      background: 'transparent',
                      border: `2px solid ${colors.PRIMARY.primary}`,
                      color: colors.PRIMARY.primary,
                      fontWeight: 600,
                      padding: '0.75rem 1.5rem',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}
                  >
                    <Camera width={16} height={16} />
                    Take new photos
                  </Button>
                </ActionButtons>
              </>
            ) : showCamera ? (
              <LiveCamera
                setShowCamera={handleHideCamera}
                onCapture={handleCapturedImage}
                onClose={() => setShowCamera(false)}
              />
            ) : imagesUploaded ? (
              <>
                <div className="upload-icon">
                  <CheckCircle width={24} height={24} style={{ color: colors.GREEN.primary }} />
                </div>
                <h4>
                  {showExistingPhotos && photoStatus?.has_photos
                    ? "Using Your Previous Photos!"
                    : "Images Uploaded Successfully!"
                  }
                </h4>
                <p>
                  {showExistingPhotos && photoStatus?.has_photos
                    ? `We're using your ${photoStatus.count} previously uploaded photo${photoStatus.count > 1 ? 's' : ''} for analysis.`
                    : "Your photos have been uploaded and are ready for analysis."
                  }
                </p>

                {showExistingPhotos && existingPhotos.length > 0 && (
                  <div style={{ marginTop: '1rem', display: 'flex', gap: '0.5rem', justifyContent: 'center', flexWrap: 'wrap' }}>
                    {existingPhotos.map((photo, index) => (
                      <div key={photo.question_id} style={{
                        width: '60px',
                        height: '60px',
                        borderRadius: '8px',
                        overflow: 'hidden',
                        border: `2px solid ${colors.GREEN.primary}`
                      }}>
                        <img
                          src={photo.image_url}
                          alt={`Hair photo ${index + 1}`}
                          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                        />
                      </div>
                    ))}
                  </div>
                )}

                {showExistingPhotos && photoStatus?.has_photos && (
                  <div style={{ marginTop: '1rem' }}>
                    <Button
                      onClick={handleReplacePhotos}
                      style={{
                        background: 'transparent',
                        border: `1px solid ${colors.SECONDARY.primary}`,
                        color: colors.SECONDARY.primary,
                        fontWeight: 500,
                        padding: '0.5rem 1rem',
                        borderRadius: '6px',
                        fontSize: '0.875rem'
                      }}
                    >
                      Take new photos instead
                    </Button>
                  </div>
                )}
              </>
            ) : selectedFiles.length < 3 ? (
              <>
                <div className="upload-icon">
                  <Upload width={24} height={24} style={{ color: colors.PRIMARY.primary }} />
                </div>
                <h4>
                  {selectedFiles.length === 0
                    ? "Upload Your Hair Photos"
                    : `Add More Photos (${selectedFiles.length}/3)`
                  }
                </h4>
                <p>
                  {selectedFiles.length === 0
                    ? "Please upload 3 clear photos of your hair from different angles for the best analysis results."
                    : `You need ${3 - selectedFiles.length} more photo${3 - selectedFiles.length > 1 ? 's' : ''} to continue.`
                  }
                </p>

                <ActionButtons>
                  <Button
                    onClick={handleFileUpload}
                    style={{
                      background: colors.PRIMARY.primary,
                      border: 'none',
                      color: 'white',
                      fontWeight: 600,
                      padding: '0.75rem 1.5rem',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}
                  >
                    <Upload width={16} height={16} />
                    {selectedFiles.length === 0 ? "Upload Pictures" : "Upload More"}
                  </Button>
                  <span className="divider">Or</span>
                  <Button
                    onClick={() => setShowCamera(true)}
                    style={{
                      background: 'transparent',
                      border: `2px solid ${colors.PRIMARY.primary}`,
                      color: colors.PRIMARY.primary,
                      fontWeight: 600,
                      padding: '0.75rem 1.5rem',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}
                  >
                    <Camera width={16} height={16} />
                    {selectedFiles.length === 0 ? "Take Pictures" : "Take More"}
                  </Button>
                </ActionButtons>
              </>
            ) : null}
            {selectedFiles.length > 0 && !imagesUploaded && (
              <div style={{ marginTop: '1.5rem' }}>
                <h4 style={{ textAlign: 'center', marginBottom: '1rem', color: '#374151' }}>
                  {selectedFiles.length < 3
                    ? `Selected Photos (${selectedFiles.length}/3)`
                    : `Ready to Upload (${selectedFiles.length}/3)`
                  }
                </h4>
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: selectedFiles.length === 3
                      ? "repeat(3, 1fr)"
                      : "repeat(auto-fit, minmax(100px, 1fr))",
                    gap: "1rem",
                    marginBottom: selectedFiles.length === 3 ? '1.5rem' : '1rem'
                  }}
                >
                  {selectedFiles.map((image, index) => {
                    const imageUrl =
                      typeof image === "string"
                        ? image
                        : URL.createObjectURL(image);

                    return (
                      <div key={index} style={{ position: "relative" }}>
                        <img
                          src={imageUrl}
                          alt={`Photo ${index + 1}`}
                          style={{
                            width: "100%",
                            height: "100px",
                            objectFit: "cover",
                            borderRadius: "8px",
                            border: selectedFiles.length === 3
                              ? `2px solid ${colors.GREEN.primary}`
                              : `2px solid ${colors.PRIMARY.primary}`
                          }}
                        />
                        <Button
                          onClick={() => handleRemoveImage(index)}
                          style={{
                            position: "absolute",
                            top: "0.25rem",
                            right: "0.25rem",
                            background: "#EF4444",
                            border: "none",
                            borderRadius: "50%",
                            width: "24px",
                            height: "24px",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            padding: 0
                          }}
                        >
                          <Trash width={12} height={12} color="white" />
                        </Button>
                      </div>
                    );
                  })}
                </div>

                {selectedFiles.length === 3 && (
                  <Button
                    onClick={handleSubmit}
                    style={{
                      background: colors.GREEN.primary,
                      border: 'none',
                      color: 'white',
                      fontWeight: 600,
                      padding: '0.75rem 2rem',
                      borderRadius: '8px',
                      width: '100%'
                    }}
                  >
                    Upload All Images ({selectedFiles.length}/3)
                  </Button>
                )}
              </div>
            )}

          </UploadBox>
        </UploadSection>

        <PhotoStandardsSection>
          <h3>Photo Guidelines for Best Results</h3>
          <PhotoGrid>
            {photoExamples.map((example, index) => (
              <PhotoExample key={index} $approved={example.approved}>
                <img src={example.src} alt={example.label} />
                <div className="label">{example.label}</div>
              </PhotoExample>
            ))}
          </PhotoGrid>
          <p style={{
            fontSize: '0.875rem',
            color: '#6B7280',
            textAlign: 'center',
            margin: 0,
            lineHeight: 1.5
          }}>
            Clear, well-lit photos from multiple angles help our AI provide the most accurate analysis.
          </p>
        </PhotoStandardsSection>
      </CompactLayout>
    </Root>
  );
};

export default UploadPhotos;
