import styled from "styled-components";
import React from "react";
import { <PERSON><PERSON> } from "react-bootstrap";
import { <PERSON><PERSON><PERSON>, <PERSON>, Brain, Award } from "lucide-react";
import PhotoStandards from "../../../../components/PhotoStandards";
import { v4 as uuidv4 } from "uuid";
import { useDispatch } from "react-redux";
import { setSessionGuid } from "../../../../redux/QuizSlice";
import colors from "../../../../styles/colors";

const Root = styled.div`
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: calc(100vh - 160px);
  width: 100vw;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #F8FAFC 0%, #FFFFFF 100%);

  @media only screen and (max-width: 768px) {
    padding: 2rem 1rem;
  }
`;

const HeroSection = styled.div`
  text-align: center;
  margin-bottom: 3rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .subtitle {
    font-size: 1.125rem;
    color: #6B7280;
    max-width: 600px;
    margin: 0 auto 1.5rem;
    line-height: 1.6;
  }

  .highlight {
    font-size: 1rem;
    color: #374151;
    font-weight: 600;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.5;
  }

  @media only screen and (max-width: 768px) {
    margin-bottom: 2rem;

    h1 {
      font-size: 2rem;
    }

    .subtitle {
      font-size: 1rem;
    }

    .highlight {
      font-size: 0.875rem;
    }
  }
`;

const GridRow = styled.div`
  display: flex;
  flex: 1;
  flex-direction: row;
  gap: 3rem;
  margin-bottom: 3rem;

  @media only screen and (max-width: 768px) {
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
  }
`;

const GridBox = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;

  @media only screen and (max-width: 768px) {
    // Mobile styles
  }
`;

const FeatureGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;

  @media only screen and (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
  }
`;

const FeatureCard = styled.div`
  background: #FFFFFF;
  border: 1px solid #E2E8F0;
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: ${colors.PRIMARY.primary}40;
  }

  .icon-container {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, ${colors.PRIMARY.pastel} 0%, ${colors.SECONDARY.pastel} 100%);
  }

  h6 {
    font-size: 1rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.5rem;
  }

  p {
    font-size: 0.875rem;
    color: #6B7280;
    margin: 0;
    line-height: 1.4;
  }
`;

const CTASection = styled.div`
  text-align: center;
  margin-top: auto;
  padding-top: 2rem;
`;

const FlexEnd = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: flex-end;
`;

interface Props {
  onStartQuiz: () => void;
}

const Landing: React.FC<Props> = ({ onStartQuiz }) => {
  const dispatch = useDispatch();

  const handleStartQuiz = () => {
    const newSessionGuid = uuidv4();
    dispatch(setSessionGuid(newSessionGuid));
    onStartQuiz?.();
  };
  const features = [
    {
      icon: Brain,
      title: "AI-Powered Analysis",
      description: "Advanced algorithms analyze your hair type and scalp condition for personalized insights."
    },
    {
      icon: Camera,
      title: "Photo Assessment",
      description: "Upload photos for detailed visual analysis of your hair health and condition."
    },
    {
      icon: Award,
      title: "Expert Recommendations",
      description: "Get professional product recommendations tailored to your specific hair needs."
    }
  ];

  return (
    <Root>
      <HeroSection>
        <h1>Free Hair Diagnostic</h1>
        <p className="subtitle">
          Say goodbye to trial and error! Get personalized hair care recommendations
          based on advanced AI analysis and expert insights.
        </p>
        <p className="highlight">
          Help us develop life-changing technology while getting accurate,
          personalized product recommendations for your unique hair needs.
        </p>
      </HeroSection>

      <FeatureGrid>
        {features.map((feature, index) => (
          <FeatureCard key={index}>
            <div className="icon-container">
              <feature.icon size={24} style={{ color: colors.PRIMARY.primary }} />
            </div>
            <h6>{feature.title}</h6>
            <p>{feature.description}</p>
          </FeatureCard>
        ))}
      </FeatureGrid>

      <GridRow>
        <GridBox>
          <PhotoStandards />
        </GridBox>
      </GridRow>

      <CTASection>
        <Button
          className="rounded-pill px-5 py-3 d-flex align-items-center gap-2 mx-auto"
          onClick={handleStartQuiz}
          style={{
            background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
            border: 'none',
            color: 'white',
            fontWeight: 700,
            fontSize: '1.125rem',
            boxShadow: `0 8px 25px rgba(67, 0, 255, 0.3)`,
            transition: 'all 0.2s ease',
            minWidth: '200px'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 12px 35px rgba(67, 0, 255, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 8px 25px rgba(67, 0, 255, 0.3)';
          }}
        >
          <Sparkles size={20} />
          Start Your Analysis
        </Button>
      </CTASection>
    </Root>
  );
};

export default Landing;
