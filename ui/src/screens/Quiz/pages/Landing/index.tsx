import styled from "styled-components";
import React from "react";
import { <PERSON><PERSON> } from "react-bootstrap";
import { <PERSON><PERSON><PERSON>, <PERSON>, Brain, Award } from "lucide-react";
import PhotoStandards from "../../../../components/PhotoStandards";
import { v4 as uuidv4 } from "uuid";
import { useDispatch } from "react-redux";
import { setSessionGuid, resetQuiz } from "../../../../redux/QuizSlice";
import { useAppSelector } from "../../../../redux/hooks";
import colors from "../../../../styles/colors";

const Root = styled.div`
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: calc(100vh - 160px);
  width: 100vw;
  padding: 2rem;
  background: linear-gradient(135deg, #F8FAFC 0%, #FFFFFF 100%);

  @media only screen and (max-width: 768px) {
    padding: 1rem;
  }
`;

const CompactLayout = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
  max-width: 1200px;
  margin: 0 auto;

  @media only screen and (max-width: 968px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
`;

const HeroSection = styled.div`
  h1 {
    font-size: 2.25rem;
    font-weight: 700;
    color: ${colors.PRIMARY.primary};
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .subtitle {
    font-size: 1.125rem;
    color: #6B7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }

  .highlight {
    font-size: 0.9375rem;
    color: #374151;
    font-weight: 500;
    margin-bottom: 2rem;
    line-height: 1.5;
  }

  @media only screen and (max-width: 968px) {
    text-align: center;

    h1 {
      font-size: 2rem;
    }

    .subtitle {
      font-size: 1rem;
    }

    .highlight {
      font-size: 0.875rem;
    }
  }
`;

const GridRow = styled.div`
  display: flex;
  flex: 1;
  flex-direction: row;
  gap: 3rem;
  margin-bottom: 3rem;

  @media only screen and (max-width: 768px) {
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
  }
`;

const GridBox = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;

  @media only screen and (max-width: 768px) {
    // Mobile styles
  }
`;

const FeatureGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;

  @media only screen and (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
  }
`;

const FeatureCard = styled.div`
  background: #FFFFFF;
  border: 1px solid #E2E8F0;
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: ${colors.PRIMARY.primary}40;
  }

  .icon-container {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    background: ${colors.PRIMARY.pastel};
    border: 1px solid ${colors.PRIMARY.primary}20;
  }

  h6 {
    font-size: 1rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.5rem;
  }

  p {
    font-size: 0.875rem;
    color: #6B7280;
    margin: 0;
    line-height: 1.4;
  }
`;

const CTASection = styled.div`
  text-align: center;
  margin-top: auto;
  padding-top: 2rem;
`;

const FlexEnd = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: flex-end;
`;

const PhotoStandardsSection = styled.div`
  background: #FFFFFF;
  border: 1px solid #E5E7EB;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  @media only screen and (max-width: 968px) {
    padding: 1.5rem;

    h3 {
      font-size: 1.125rem;
    }
  }
`;

const PhotoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;

  @media only screen and (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  @media only screen and (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;

const PhotoExample = styled.div<{ $approved?: boolean; }>`
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid ${props => props.$approved ? colors.GREEN.primary : '#EF4444'};
  background: #F9FAFB;

  img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    display: block;
  }

  .label {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: ${props => props.$approved ? colors.GREEN.primary : '#EF4444'};
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
  }

  @media only screen and (max-width: 768px) {
    img {
      height: 100px;
    }

    .label {
      font-size: 0.6875rem;
      padding: 0.2rem 0.4rem;
    }
  }
`;

interface Props {
  onStartQuiz: () => void;
}

const Landing: React.FC<Props> = ({ onStartQuiz }) => {
  const dispatch = useDispatch();
  const existingSessionGuid = useAppSelector((state) => state.quiz.session_guid);
  const isCompleted = useAppSelector((state) => state.quiz.is_completed);

  const handleStartQuiz = () => {
    // If quiz is completed or no existing session, start fresh
    if (isCompleted || !existingSessionGuid) {
      dispatch(resetQuiz());
      const newSessionGuid = uuidv4();
      dispatch(setSessionGuid(newSessionGuid));
    }
    // If there's an existing session, continue with it
    onStartQuiz?.();
  };
  const features = [
    {
      icon: Brain,
      title: "AI-Powered Analysis",
      description: "Advanced algorithms analyze your hair type and scalp condition for personalized insights."
    },
    {
      icon: Camera,
      title: "Photo Assessment",
      description: "Upload photos for detailed visual analysis of your hair health and condition."
    },
    {
      icon: Award,
      title: "Expert Recommendations",
      description: "Get professional product recommendations tailored to your specific hair needs."
    }
  ];

  const photoExamples = [
    { src: require("../../../../assets/images/hair-1.png"), approved: true, label: "Approved" },
    { src: require("../../../../assets/images/hair-2.png"), approved: true, label: "Approved" },
    { src: require("../../../../assets/images/hair-3.png"), approved: true, label: "Approved" },
    { src: require("../../../../assets/images/hair-4.png"), approved: false, label: "Too Close" },
    { src: require("../../../../assets/images/hair-5.png"), approved: false, label: "Too Dark" },
    { src: require("../../../../assets/images/hair-6.png"), approved: false, label: "Background" },
  ];

  return (
    <Root>
      <CompactLayout>
        <div>
          <HeroSection>
            <h1>Free Hair Diagnostic</h1>
            <p className="subtitle">
              Say goodbye to trial and error! Get personalized hair care recommendations
              based on advanced AI analysis and expert insights.
            </p>
            <p className="highlight">
              Help us develop life-changing technology while getting accurate,
              personalized product recommendations for your unique hair needs.
            </p>
          </HeroSection>

          <FeatureGrid>
            {features.map((feature, index) => (
              <FeatureCard key={index}>
                <div className="icon-container">
                  <feature.icon size={20} style={{ color: colors.PRIMARY.primary }} />
                </div>
                <h6>{feature.title}</h6>
                <p>{feature.description}</p>
              </FeatureCard>
            ))}
          </FeatureGrid>

          <CTASection>
            <Button
              className="rounded-pill px-5 py-3 d-flex align-items-center gap-2 mx-auto"
              onClick={handleStartQuiz}
              style={{
                background: colors.PRIMARY.primary,
                border: 'none',
                color: 'white',
                fontWeight: 700,
                fontSize: '1.125rem',
                boxShadow: '0 8px 25px rgba(67, 0, 255, 0.3)',
                transition: 'all 0.2s ease',
                minWidth: '200px'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 12px 35px rgba(67, 0, 255, 0.4)';
                e.currentTarget.style.background = '#3700CC';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(67, 0, 255, 0.3)';
                e.currentTarget.style.background = colors.PRIMARY.primary;
              }}
            >
              <Sparkles size={20} />
              Start Your Analysis
            </Button>
          </CTASection>
        </div>

        <PhotoStandardsSection>
          <h3>Clear images are needed to conduct a detailed hair analysis and assess its condition.</h3>
          <PhotoGrid>
            {photoExamples.map((example, index) => (
              <PhotoExample key={index} $approved={example.approved}>
                <img src={example.src} alt={example.label} />
                <div className="label">{example.label}</div>
              </PhotoExample>
            ))}
          </PhotoGrid>
        </PhotoStandardsSection>
      </CompactLayout>
    </Root>
  );
};

export default Landing;
