import React, { useEffect, useMemo, useState } from "react";
import styled from "styled-components";
import { Check } from "lucide-react";
import Row from "../../../../components/Row";
import Column from "../../../../components/Column";
import colors from "../../../../styles/colors";

export const QuestionDiv = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  border: 1px solid #E2E8F0;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: ${colors.PRIMARY.primary}20;
  }

  h3 {
    margin: 0 0 0.5rem 0;
    padding: 0;
    font-weight: 700;
    font-size: 1.125rem;
    color: #1F2937;
    line-height: 1.4;
  }

  h4 {
    margin: 0;
    padding: 0;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6B7280;
  }

  h5 {
    margin: 0.75rem 0 1.5rem 0;
    padding: 0;
    font-size: 0.875rem;
    font-weight: 500;
    color: ${colors.PRIMARY.primary};
  }

  @media only screen and (max-width: 768px) {
    padding: 1.5rem;

    h3 {
      font-size: 1rem;
    }
  }
`;

const CircleCheckBox = styled.div<{
  $selected?: boolean;
  $type: "radio" | "checkbox";
}>`
  height: 20px;
  width: 20px;
  border-radius: ${(props) => (props.$type === "radio" ? "50%" : "6px")};
  border: 2px solid ${(props) => (props.$selected ? colors.PRIMARY.primary : "#D1D5DB")};
  background-color: ${(props) => (props.$selected ? colors.PRIMARY.primary : "transparent")};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;

  &:hover {
    border-color: ${colors.PRIMARY.primary};
  }
`;

const InnerCircle = styled.div`
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: white;
`;

const OptionContainer = styled.div<{ $selected?: boolean; }>`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid ${(props) => (props.$selected ? colors.PRIMARY.primary : "#E5E7EB")};
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: ${(props) => (props.$selected ? colors.PRIMARY.pastel : "#FFFFFF")};

  &:hover {
    border-color: ${colors.PRIMARY.primary};
    background: ${colors.PRIMARY.pastel};
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(67, 0, 255, 0.15);
  }

  h4 {
    margin: 0;
    font-weight: 600;
    font-size: 0.9375rem;
    color: ${(props) => (props.$selected ? colors.PRIMARY.primary : "#374151")};
    transition: color 0.2s ease;
  }
`;

const OptionsGrid = styled.div`
  display: grid;
  gap: 0.75rem;
  grid-template-columns: 1fr;

  @media only screen and (min-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
`;

const Question: React.FC<QuestionProps & { index: number; }> = ({
  id,
  title,
  value,
  setAnswer,
  options,
  subheader,
  index,
  answerType,
}) => {
  const backgroundColor = useMemo(() => {
    const val = index;

    if (val % 2 === 0 || isNaN(val)) {
      return "#FFFFFF";
    } else {
      return "#FCFCFC";
    }
  }, [index]);

  // const [isSelected, setIsSelected] = useState<{ id: string; label: string }[]>(
  //   []
  // );

  const reduxValue = Array.isArray(value)
    ? value
    : typeof value === "string"
      ? [value]
      : [value];

  const handleSelection = (selectedValue: string, selectedId: string) => {
    // if (answerType === "MC") {

    // setIsSelected((prev) => {
    //   const isExists = prev.some((item) => item.id === selectedId);
    //   const updatedSelection = isExists
    //     ? prev.filter((item) => item.id !== selectedId) // Remove if exists
    //     : [...prev, { id: selectedId, label: selectedValue }]; // Add if not

    //   const currentIndex = updatedSelection.findIndex(
    //     (item) => item.id === selectedId
    //   );
    //   const currentLabel =
    //     currentIndex !== -1 ? updatedSelection[currentIndex].label : "";

    //   // Set the answer with the selected label
    //   setAnswer(id, currentLabel, answerType || "");

    //   return updatedSelection;
    // });

    // setAnswer(id, multipleAnswers[0], answerType || "");
    // } else {
    // setIsSelected([{ id: selectedId, label: selectedValue }]);
    setAnswer(id, selectedValue, answerType || "");
    // }
  };

  return (
    <QuestionDiv>
      <h3>{title}</h3>
      {subheader && <h5>{subheader}</h5>}

      <OptionsGrid>
        {options.map((option, key) => {
          const inputType = answerType === "MC" ? "checkbox" : "radio";
          const isSelected = reduxValue?.some((item) => item === option.label);

          return (
            <OptionContainer
              key={key}
              $selected={isSelected}
              onClick={() => handleSelection(option.label, option?.id ?? "")}
            >
              <input
                type={answerType === "MC" ? "checkbox" : "radio"}
                id={`${id}-${key}`}
                name={answerType === "SC" ? `question-${id}` : undefined}
                checked={isSelected}
                onChange={() => handleSelection(option.label, option?.id ?? "")}
                style={{ display: "none" }}
              />

              <CircleCheckBox
                $selected={isSelected}
                $type={inputType}
              >
                {isSelected && inputType === "checkbox" && (
                  <Check size={12} color="white" />
                )}
                {isSelected && inputType === "radio" && <InnerCircle />}
              </CircleCheckBox>

              <h4>{option.label}</h4>
            </OptionContainer>
          );
        })}
      </OptionsGrid>
    </QuestionDiv>
  );
};

export default React.memo(Question);
