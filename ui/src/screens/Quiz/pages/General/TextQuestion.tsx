import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { QuestionDiv } from "./Question";
import colors from "../../../../styles/colors";

const TextQuestionDiv = styled(QuestionDiv)`
  grid-column: 1 / -1; /* Span full width for text questions */
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 120px;
  padding: 1rem;
  border: 2px solid #E5E7EB;
  border-radius: 12px;
  font-size: 0.9375rem;
  font-family: inherit;
  line-height: 1.5;
  resize: vertical;
  transition: all 0.2s ease;
  background: #FFFFFF;

  &:focus {
    outline: none;
    border-color: ${colors.PRIMARY.primary};
    box-shadow: 0 0 0 3px ${colors.PRIMARY.primary}20;
  }

  &::placeholder {
    color: #9CA3AF;
    font-style: italic;
  }

  @media only screen and (max-width: 768px) {
    min-height: 100px;
    font-size: 0.875rem;
  }
`;

const CharacterCount = styled.div`
  text-align: right;
  font-size: 0.8125rem;
  color: #6B7280;
  margin-top: 0.5rem;
`;

const TextQuestion: React.FC<QuestionProps & { index: number; }> = ({
  id,
  title,
  value,
  setAnswer,
  subheader,
  answerType,
}) => {
  const [textValue, setTextValue] = useState<string>((value as string) || "");

  useEffect(() => {
    setTextValue((value as string) || "");
  }, [value]);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setTextValue(newValue);
    setAnswer(id, newValue, answerType || "CR");
  };

  const maxLength = 500;
  const remainingChars = maxLength - textValue.length;

  return (
    <TextQuestionDiv>
      <h3>{title}</h3>
      {subheader && <h5>{subheader}</h5>}

      <TextArea
        value={textValue}
        onChange={handleTextChange}
        placeholder="Please share your thoughts here..."
        maxLength={maxLength}
      />

      <CharacterCount>
        {remainingChars} characters remaining
      </CharacterCount>
    </TextQuestionDiv>
  );
};

export default React.memo(TextQuestion);
