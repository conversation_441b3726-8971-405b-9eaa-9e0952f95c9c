import styled from "styled-components";
import { QuestionDiv } from "./Question";
import Column from "../../../../components/Column";
import React, { useMemo } from "react";
import colors from "../../../../styles/colors";

const ImageQuestionDiv = styled(QuestionDiv)`
  grid-column: 1 / -1; /* Span full width for image questions */
`;

const ImageGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;

  @media only screen and (max-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
  }
`;

const ImageOption = styled.div<{ $selected?: boolean; }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 2px solid ${(props) => (props.$selected ? colors.PRIMARY.primary : "#E5E7EB")};
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: ${(props) => (props.$selected ? colors.PRIMARY.pastel : "#FFFFFF")};

  &:hover {
    border-color: ${colors.PRIMARY.primary};
    background: ${colors.PRIMARY.pastel};
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(67, 0, 255, 0.15);
  }

  img {
    height: 120px;
    width: 120px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 0.75rem;
  }

  span {
    font-weight: 600;
    font-size: 0.875rem;
    color: ${(props) => (props.$selected ? colors.PRIMARY.primary : "#374151")};
    text-align: center;
    transition: color 0.2s ease;
  }

  @media only screen and (max-width: 768px) {
    padding: 0.75rem;

    img {
      height: 100px;
      width: 100px;
    }

    span {
      font-size: 0.8125rem;
    }
  }
`;

const ImageQuestion: React.FC<QuestionProps & { index: number; }> = ({
  title,
  subheader,
  options,
  index,
  setAnswer,
  value,
  id,
}) => {
  const reduxValue = Array.isArray(value)
    ? value
    : typeof value === "string" ? [value] : [value];

  return (
    <ImageQuestionDiv>
      <h3>{title}</h3>
      {subheader && <h5>{subheader}</h5>}

      <ImageGrid>
        {options.map((val, key) => {
          const selected = reduxValue?.some((item) => item === val.label);
          return (
            <ImageOption
              $selected={selected}
              onClick={() => setAnswer(id, val.label, "IU")}
              key={key}
            >
              {val.image && (
                <img
                  src={val.image}
                  alt={val.label}
                />
              )}
              <span>{val.label}</span>
            </ImageOption>
          );
        })}
      </ImageGrid>
    </ImageQuestionDiv>
  );
};

export default ImageQuestion;
