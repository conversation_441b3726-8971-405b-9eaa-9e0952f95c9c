import React from "react";
import styled from "styled-components";
import Question from "./Question";
import ImageQuestion from "./ImageQuestion";
import TextQuestion from "./TextQuestion";
import useQuestionaire from "./useQuizAnswer";
import { useAppSelector } from "../../../../redux/hooks";

const Root = styled.div`
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: calc(100vh - 240px);
  width: 100vw;
  padding: 2rem;
  background: #FAFBFC;
  gap: 1.5rem;

  @media only screen and (max-width: 768px) {
    padding: 1rem;
    gap: 1rem;
  }
`;

const QuestionGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;

  @media only screen and (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  @media only screen and (max-width: 480px) {
    gap: 1rem;
  }
`;

const General: React.FC<QuizPageProps> = ({ questions }) => {
  const answers = useAppSelector((state) => state.quiz);
  const { handleUpdateQuizAnswer } = useQuestionaire();

  return (
    <Root>
      <QuestionGrid>
        {questions &&
          questions.map((question, index) => {
            if (question.type === "question") {
              return (
                <Question
                  {...question}
                  key={question.id}
                  value={answers[question.id]}
                  setAnswer={handleUpdateQuizAnswer}
                  index={index}
                  answerType={question?.answerType}
                />
              );
            } else if (question.type === "image_question") {
              return (
                <ImageQuestion
                  {...question}
                  key={question.id}
                  value={answers[question.id]}
                  setAnswer={handleUpdateQuizAnswer}
                  index={index}
                  answerType={question?.answerType}
                />
              );
            } else if (question.type === "text_question") {
              return (
                <TextQuestion
                  {...question}
                  key={question.id}
                  value={answers[question.id]}
                  setAnswer={handleUpdateQuizAnswer}
                  index={index}
                  answerType={question?.answerType}
                />
              );
            }
            return null;
          })}
      </QuestionGrid>
    </Root>
  );
};

export default General;
