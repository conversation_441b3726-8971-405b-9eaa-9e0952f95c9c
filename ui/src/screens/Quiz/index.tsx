import styled from "styled-components";
import CosmetricsLogo from "../../assets/images/cosmetrics-logo.svg";
import { Button } from "react-bootstrap";
import { useCallback, useEffect, useMemo, useState } from "react";
import { <PERSON>, <PERSON>rkles } from "lucide-react";
import Landing from "./pages/Landing";
import General from "./pages/General";
import UploadPhotos from "./pages/UploadPhotos";
import ExtraInformation from "./pages/ExtraInformation";
import MedicalConditions from "./pages/MedicalConditions";
import Lifestyle from "./pages/Lifestyle";
import HairCare from "./pages/HairCare";
import Preferences from "./pages/Preferences";
import AnalysingResults from "./pages/AnalysingResults";
import Results from "./pages/Results";
import ProgressHeader from "./ProgressHeader";
import { useAppSelector } from "../../redux/hooks";
import {
  haircareQuestions,
  initialQuestions,
  lifestyleQuestions,
  medicalQuestions,
  preferenceQuestions,
} from "./questions";
import { uploadQuestionaire } from "../../apis";
import colors from "../../styles/colors";

const Root = styled.div`
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: 100vh;
  width: 100vw;
`;

const Header = styled.header`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  height: 80px;
  width: 100vw;
  padding: 0 2rem;
  background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFC 100%);
  border-bottom: 1px solid #E2E8F0;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.05);
  z-index: 10001;

  .logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .quiz-title {
    color: #1F2937;
    font-weight: 700;
    font-size: 1.125rem;
    margin: 0;
  }

  .quiz-subtitle {
    color: #6B7280;
    font-size: 0.875rem;
    margin: 0;
  }

  @media only screen and (max-width: 768px) {
    padding: 0 1rem;

    .quiz-title {
      font-size: 1rem;
    }

    .quiz-subtitle {
      display: none;
    }
  }
`;

const Footer = styled.div`
  display: flex;
  flex-direction: row;
  position: relative;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  padding: 2rem;
  background: #FFFFFF;
  border-top: 1px solid #E2E8F0;
  gap: 1rem;

  @media only screen and (max-width: 768px) {
    padding: 1.5rem 1rem;
    gap: 0.75rem;
  }
`;

const Quiz = () => {
  const answers = useAppSelector((state) => state.quiz);
  const sessionGuid = useAppSelector((state) => state.quiz.session_guid) || "";

  const [index, setIndex] = useState(0);

  const RenderView = useMemo(() => {
    switch (index) {
      case 0:
        return <Landing onStartQuiz={() => setIndex(1)} />;
      case 1:
        return <General questions={initialQuestions} />;
      case 2:
        return <UploadPhotos />;
      case 3:
        return <ExtraInformation />;
      case 4:
        return <MedicalConditions questions={medicalQuestions} />;
      case 5:
        return <Lifestyle questions={lifestyleQuestions} />;
      case 6:
        return <HairCare questions={haircareQuestions} />;
      case 7:
        return <Preferences questions={preferenceQuestions} />;
      case 8:
        return <AnalysingResults />;
      case 9:
        return <Results />;
      default:
        return null;
    }
  }, [index]);

  const handleExitQuiz = useCallback(() => {
    uploadQuestionaire("user_1", answers, "", sessionGuid);
    window.location.href = "/";
  }, [answers]);

  useEffect(() => {
    window.scrollTo(0, 0);

    if (index === 8) {
      const timeoutId = setTimeout(() => {
        setIndex(9);
      }, 5000);

      // Cleanup timeout on unmount or index change
      return () => clearTimeout(timeoutId);
    }
  }, [index]);

  return (
    <Root>
      <Header>
        <div className="logo-section">
          <img src={CosmetricsLogo} alt="Cosmetrics AI Logo" style={{ height: '40px' }} />
          <div>
            <h6 className="quiz-title">Hair Diagnostic</h6>
            <p className="quiz-subtitle">Personalized Analysis</p>
          </div>
        </div>
        <Button
          onClick={() => {
            handleExitQuiz();
          }}
          variant="outline-primary"
          className="rounded-pill px-4 d-flex align-items-center gap-2"
          style={{
            borderColor: colors.PRIMARY.primary,
            color: colors.PRIMARY.primary,
            fontWeight: 600,
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = colors.PRIMARY.primary;
            e.currentTarget.style.color = 'white';
            e.currentTarget.style.transform = 'translateY(-1px)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent';
            e.currentTarget.style.color = colors.PRIMARY.primary;
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          <X size={16} />
          Exit Quiz
        </Button>
      </Header>
      {index !== 0 && index !== 8 && index !== 9 && (
        <ProgressHeader
          index={index}
          answers={answers}
          totalQuestions={
            initialQuestions.length +
            medicalQuestions.length +
            lifestyleQuestions.length +
            haircareQuestions.length +
            preferenceQuestions.length
          }
        />
      )}
      {RenderView}
      {index !== 0 && index !== 8 && index !== 9 && (
        <Footer>
          <Button
            onClick={() => {
              setIndex((prev) => prev - 1);
            }}
            variant="outline-secondary"
            className="rounded-pill px-4 d-flex align-items-center gap-2"
            style={{
              fontWeight: 600,
              minWidth: '120px',
              transition: 'all 0.2s ease'
            }}
          >
            Back
          </Button>
          {index === 7 ? (
            <Button
              onClick={() => setIndex((prev) => prev + 1)}
              className="rounded-pill px-4 d-flex align-items-center gap-2"
              style={{
                background: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
                border: 'none',
                color: 'white',
                fontWeight: 600,
                minWidth: '140px',
                boxShadow: '0 4px 16px rgba(16, 185, 129, 0.3)',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(16, 185, 129, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 16px rgba(16, 185, 129, 0.3)';
              }}
            >
              <Sparkles size={16} />
              Complete Assessment
            </Button>
          ) : (
            <Button
              onClick={() => setIndex((prev) => prev + 1)}
              className="rounded-pill px-4 d-flex align-items-center gap-2"
              style={{
                background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
                border: 'none',
                color: 'white',
                fontWeight: 600,
                minWidth: '120px',
                boxShadow: `0 4px 16px rgba(67, 0, 255, 0.3)`,
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(67, 0, 255, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 16px rgba(67, 0, 255, 0.3)';
              }}
            >
              Continue
            </Button>
          )}
        </Footer>
      )}
    </Root>
  );
};

export default Quiz;
