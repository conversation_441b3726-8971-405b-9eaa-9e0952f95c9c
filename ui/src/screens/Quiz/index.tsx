import styled from "styled-components";
import CosmetricsLogo from "../../assets/images/cosmetrics-logo.svg";
import { Button } from "react-bootstrap";
import { useCallback, useEffect, useMemo, useState } from "react";
import { <PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import Landing from "./pages/Landing";
import General from "./pages/General";
import UploadPhotos from "./pages/UploadPhotos";
import ExtraInformation from "./pages/ExtraInformation";
import MedicalConditions from "./pages/MedicalConditions";
import Lifestyle from "./pages/Lifestyle";
import HairCare from "./pages/HairCare";
import Preferences from "./pages/Preferences";
import AnalysingResults from "./pages/AnalysingResults";
import Results from "./pages/Results";
import ProgressHeader from "./ProgressHeader";
import { useAppSelector, useAppDispatch } from "../../redux/hooks";
import {
  haircareQuestions,
  initialQuestions,
  lifestyleQuestions,
  medicalQuestions,
  preferenceQuestions,
} from "./questions";
import { uploadQuestionaire } from "../../apis";
import colors from "../../styles/colors";
import { setCurrentStep, markQuizCompleted } from "../../redux/QuizSlice";
import { useQuizRestore } from "../../hooks/useQuizRestore";

const Root = styled.div`
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: 100vh;
  width: 100vw;
`;

const Header = styled.header`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  height: 80px;
  width: 100vw;
  padding: 0 2rem;
  background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFC 100%);
  border-bottom: 1px solid #E2E8F0;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.05);
  z-index: 10001;

  .logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .quiz-title {
    color: #1F2937;
    font-weight: 700;
    font-size: 1.125rem;
    margin: 0;
  }

  .quiz-subtitle {
    color: #6B7280;
    font-size: 0.875rem;
    margin: 0;
  }

  @media only screen and (max-width: 768px) {
    padding: 0 1rem;

    .quiz-title {
      font-size: 1rem;
    }

    .quiz-subtitle {
      display: none;
    }
  }
`;

const Footer = styled.div`
  display: flex;
  flex-direction: row;
  position: relative;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  padding: 2rem;
  background: #FFFFFF;
  border-top: 1px solid #E2E8F0;
  gap: 1rem;

  @media only screen and (max-width: 768px) {
    padding: 1.5rem 1rem;
    gap: 0.75rem;
  }
`;

const Quiz = () => {
  const dispatch = useAppDispatch();
  const answers = useAppSelector((state) => state.quiz);
  const sessionGuid = useAppSelector((state) => state.quiz.session_guid) || "";
  const currentStep = useAppSelector((state) => state.quiz.current_step);
  const isCompleted = useAppSelector((state) => state.quiz.is_completed);

  // Initialize index from stored progress or start at 0
  const [index, setIndex] = useState(() => {
    // If quiz is completed, start from landing page
    if (isCompleted) return 0;
    // If there's a stored current step and session, restore it
    if (currentStep !== undefined && sessionGuid) return currentStep;
    // Otherwise start from beginning
    return 0;
  });
  const [uploadedPhotosCount, setUploadedPhotosCount] = useState(0);

  // Use the quiz restore hook
  useQuizRestore();

  // Enhanced setIndex that also updates Redux state
  const updateIndex = useCallback((newIndex: number | ((prev: number) => number)) => {
    const finalIndex = typeof newIndex === 'function' ? newIndex(index) : newIndex;
    setIndex(finalIndex);
    dispatch(setCurrentStep(finalIndex));
  }, [index, dispatch]);

  // Track quiz start for A/B testing
  const handleStartQuiz = useCallback(() => {
    // Dispatch custom event for A/B test tracking
    window.dispatchEvent(new CustomEvent('quiz_started', {
      detail: {
        timestamp: new Date().toISOString(),
        sessionGuid: sessionGuid
      }
    }));

    updateIndex(1);
  }, [updateIndex, sessionGuid]);

  const RenderView = useMemo(() => {
    switch (index) {
      case 0:
        return <Landing onStartQuiz={handleStartQuiz} />;
      case 1:
        return <General questions={initialQuestions} />;
      case 2:
        return <UploadPhotos onPhotosChange={setUploadedPhotosCount} />;
      case 3:
        return <ExtraInformation />;
      case 4:
        return <MedicalConditions questions={medicalQuestions} />;
      case 5:
        return <Lifestyle questions={lifestyleQuestions} />;
      case 6:
        return <HairCare questions={haircareQuestions} />;
      case 7:
        return <Preferences questions={preferenceQuestions} />;
      case 8:
        return <AnalysingResults />;
      case 9:
        return <Results />;
      default:
        return null;
    }
  }, [index]);

  const handleExitQuiz = useCallback(() => {
    uploadQuestionaire("user_1", answers, "", sessionGuid);
    window.location.href = "/";
  }, [answers]);

  useEffect(() => {
    window.scrollTo(0, 0);

    if (index === 8) {
      const timeoutId = setTimeout(() => {
        updateIndex(9);
      }, 5000);

      // Cleanup timeout on unmount or index change
      return () => clearTimeout(timeoutId);
    }
  }, [index]);

  return (
    <Root>
      <Header>
        <div className="logo-section">
          <img src={CosmetricsLogo} alt="Cosmetrics AI Logo" style={{ height: '40px' }} />
          <div>
            <h6 className="quiz-title">Hair Diagnostic</h6>
            <p className="quiz-subtitle">Personalized Analysis</p>
          </div>
        </div>
        <Button
          onClick={() => {
            handleExitQuiz();
          }}
          variant="outline-primary"
          className="rounded-pill px-4 d-flex align-items-center gap-2"
          style={{
            borderColor: colors.PRIMARY.primary,
            color: colors.PRIMARY.primary,
            fontWeight: 600,
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = colors.PRIMARY.primary;
            e.currentTarget.style.color = 'white';
            e.currentTarget.style.transform = 'translateY(-1px)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent';
            e.currentTarget.style.color = colors.PRIMARY.primary;
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          <X size={16} />
          Exit Quiz
        </Button>
      </Header>
      {index !== 0 && index !== 8 && index !== 9 && (
        <ProgressHeader
          index={index}
          answers={answers}
          totalQuestions={
            initialQuestions.length +
            medicalQuestions.length +
            lifestyleQuestions.length +
            haircareQuestions.length +
            preferenceQuestions.length
          }
          onStepClick={(step) => updateIndex(step)}
        />
      )}
      {RenderView}
      {index !== 0 && index !== 8 && index !== 9 && (
        <Footer>
          <Button
            onClick={() => {
              setIndex((prev) => prev - 1);
            }}
            variant="outline-secondary"
            className="rounded-pill px-4 d-flex align-items-center gap-2"
            style={{
              fontWeight: 600,
              minWidth: '120px',
              transition: 'all 0.2s ease'
            }}
          >
            Back
          </Button>
          {index === 7 ? (
            <Button
              onClick={() => updateIndex((prev) => prev + 1)}
              className="rounded-pill px-4 d-flex align-items-center gap-2"
              style={{
                background: colors.GREEN.primary,
                border: 'none',
                color: 'white',
                fontWeight: 600,
                minWidth: '140px',
                boxShadow: '0 4px 16px rgba(16, 185, 129, 0.3)',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(16, 185, 129, 0.4)';
                e.currentTarget.style.background = '#059669';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 16px rgba(16, 185, 129, 0.3)';
                e.currentTarget.style.background = colors.GREEN.primary;
              }}
            >
              <Sparkles size={16} />
              Complete Assessment
            </Button>
          ) : (
            <Button
              onClick={() => {
                // Special validation for upload photos page (index 2)
                if (index === 2 && uploadedPhotosCount < 3) {
                  alert('Please upload 3 photos before continuing.');
                  return;
                }
                updateIndex((prev) => prev + 1);
              }}
              className="rounded-pill px-4 d-flex align-items-center gap-2"
              style={{
                background: colors.PRIMARY.primary,
                border: 'none',
                color: 'white',
                fontWeight: 600,
                minWidth: '120px',
                boxShadow: '0 4px 16px rgba(67, 0, 255, 0.3)',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(67, 0, 255, 0.4)';
                e.currentTarget.style.background = '#3700CC';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 16px rgba(67, 0, 255, 0.3)';
                e.currentTarget.style.background = colors.PRIMARY.primary;
              }}
            >
              Continue
            </Button>
          )}
        </Footer>
      )}
    </Root>
  );
};

export default Quiz;
