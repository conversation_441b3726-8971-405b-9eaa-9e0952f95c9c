import styled from "styled-components";
import DashboardLayout from "../../../layouts/DashboardLayout";
import Column from "../../../components/Column";
import Button from "../../../components/Button";
import { useState } from "react";
import ReportRow from "./ReportRow";
import UserSessionList from "./UserSessions";
import { Col, Container, Row } from "react-bootstrap";
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { useLocalUser } from "../../../hooks/api/useLocalUser";
import { useUserSessionGuid } from "../../../hooks/api/useUserSessionGuid";
import { useQuestionnaireStatus } from "../../../hooks/api/useQuestionnaireStatus";
import QuestionnairePaywall from "../../../components/QuestionnairePaywall";

const Root = styled.div`
    display: flex;
    flex-direction: column;
    position: relative;
    box-sizing: border-box;
    padding: 3vw 5vw 3vw 5vw;
    color: #303445;
    height: 100%;
    width: 100%;
    gap: 5vw;

    header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    h1 {
        font-size: 2em;
        font-weight: 500;
        margin: 0;
        padding: 0;
    }

    h3 {
        font-size: 1em;
        font-weight: 600;
        margin: 0 0 24px 0;
        padding: 0;
    }

    p {
        font-size: 0.875em;
        margin: 0;
        padding: 0;
    }

    button {
        width: 200px;
    }

    @media only screen and (max-width: 768px) {
        header {
            flex-direction: column;
            gap: 3vw;
        }

        h1 {
            font-size: 1.8em;
        }
    }
`;

const ReportHistory = () => {
    // Get user data from hooks
    const {
        data: user,
        isLoading: userLoading,
        isError: userError,
    } = useLocalUser();

    const userId = user?.id;

    // Only fetch session data if we have a valid user ID
    const {
        data: sessionData,
        isLoading: sessionLoading,
        isError: sessionError,
    } = useUserSessionGuid(userId && userId > 0 ? userId : undefined);

    // Check questionnaire completion status
    const { data: questionnaireStatus, isLoading: statusLoading } = useQuestionnaireStatus(userId);

    // Determine if user has completed quiz based on session data
    const hasSessionData = sessionData && sessionData.length > 0;

    // Show paywall only if user has no session data AND hasn't completed questionnaire
    const shouldShowPaywall = !statusLoading && !hasSessionData && questionnaireStatus?.status !== 'Completed';

    // Handle navigation to questionnaire
    const handleStartQuestionnaire = () => {
        if (questionnaireStatus?.status === 'Started') {
            window.location.href = '/questionaire/questions';
        } else {
            window.location.href = '/quiz';
        }
    };

    const [reports] = useState<QuizReport[]>([
        {
            date: new Date().toISOString(),
        },
        {
            date: new Date().toISOString(),
        },
        {
            date: new Date().toISOString(),
        },
    ]);

    if (userLoading || sessionLoading || statusLoading) return <Skeleton height={200} />;
    if (userError || sessionError) return <p>Error loading user/session data.</p>;

    return (
        <Container fluid style={{ backgroundColor: '#F8FAFC', minHeight: '100vh' }} className="border-2">
            <DashboardLayout style={{ padding: '3vw 5vw 3vw 5vw' }}>
                {/* Questionnaire Paywall Overlay */}
                {shouldShowPaywall && (
                    <QuestionnairePaywall
                        onStartQuestionnaire={handleStartQuestionnaire}
                        completedQuestions={questionnaireStatus?.completed_questions}
                        totalQuestions={questionnaireStatus?.total_questions}
                        status={questionnaireStatus?.status}
                    />
                )}

                {/* Main Content with conditional blur */}
                <Container
                    className="my-4 rounded-5 p-3"
                    style={{
                        backgroundColor: '#ffffff',
                        filter: shouldShowPaywall ? 'blur(4px)' : 'none',
                        pointerEvents: shouldShowPaywall ? 'none' : 'auto',
                        transition: 'filter 0.3s ease'
                    }}
                >
                    <Row className="mb-2">
                        <Col>
                            <h1 className="mb-2 fs-3 fw-semibold" style={{ color: '#000957' }}>Hair Reports History</h1>
                            <p>All of your hair diagnostic score reports can be found here.</p>
                        </Col>
                    </Row>

                    <Row>
                        <Col>
                            <UserSessionList userId={userId || 0} />
                        </Col>
                    </Row>
                </Container>
            </DashboardLayout>
        </Container>
    );
};

export default ReportHistory;
