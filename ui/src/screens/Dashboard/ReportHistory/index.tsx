import React from "react";
import { Contain<PERSON>, <PERSON>, Col, Card } from "react-bootstrap";
import { FileText, Clock, TrendingUp, BarChart3 } from "lucide-react";
import DashboardLayout from "../../../layouts/DashboardLayout";
import UserSessionList from "./UserSessions";
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { useLocalUser } from "../../../hooks/api/useLocalUser";
import { useUserSessionGuid } from "../../../hooks/api/useUserSessionGuid";
import { useQuestionnaireStatus } from "../../../hooks/api/useQuestionnaireStatus";
import QuestionnairePaywall from "../../../components/QuestionnairePaywall";
import colors from "../../../styles/colors";

interface QuizReport {
    date: string;
}

const pageStyle: React.CSSProperties = {
    backgroundColor: "#FAFBFC",
    minHeight: "100vh",
    color: "#1F2937",
};

const ReportHistory = () => {
    // Get user data from hooks
    const {
        data: user,
        isLoading: userLoading,
        isError: userError,
    } = useLocalUser();

    const userId = user?.id;

    // Only fetch session data if we have a valid user ID
    const {
        data: sessionData,
        isLoading: sessionLoading,
        isError: sessionError,
    } = useUserSessionGuid(userId && userId > 0 ? userId : undefined);

    // Check questionnaire completion status
    const { data: questionnaireStatus, isLoading: statusLoading } = useQuestionnaireStatus(userId);

    // Determine if user has completed quiz based on session data
    const hasSessionData = sessionData && sessionData.length > 0;

    // Show paywall only if user has no session data AND hasn't completed questionnaire
    const shouldShowPaywall = !statusLoading && !hasSessionData && questionnaireStatus?.status !== 'Completed';

    // Handle navigation to questionnaire
    const handleStartQuestionnaire = () => {
        if (questionnaireStatus?.status === 'Started') {
            window.location.href = '/questionaire/questions';
        } else {
            window.location.href = '/quiz';
        }
    };

    if (userLoading || sessionLoading || statusLoading) {
        return (
            <Container fluid style={pageStyle}>
                <DashboardLayout>
                    <div className="text-center my-4 py-5">
                        <div className="d-inline-flex align-items-center justify-content-center p-3 mb-3 rounded-4"
                            style={{ background: colors.PRIMARY.pastel }}>
                            <Skeleton
                                height={200}
                                style={{
                                    color: colors.PRIMARY.primary,
                                    width: '100%'
                                }}
                            />
                        </div>
                        <p className="text-muted">Loading your report history...</p>
                    </div>
                </DashboardLayout>
            </Container>
        );
    }

    if (userError || sessionError) {
        return (
            <Container fluid style={pageStyle}>
                <DashboardLayout>
                    <div className="text-center my-4 py-5">
                        <p className="text-danger">Error loading user/session data.</p>
                    </div>
                </DashboardLayout>
            </Container>
        );
    }

    return (
        <Container fluid style={pageStyle}>
            <DashboardLayout>
                {/* Questionnaire Paywall Overlay */}
                {shouldShowPaywall && (
                    <QuestionnairePaywall
                        onStartQuestionnaire={handleStartQuestionnaire}
                        completedQuestions={questionnaireStatus?.completed_questions}
                        totalQuestions={questionnaireStatus?.total_questions}
                        status={questionnaireStatus?.status}
                    />
                )}

                {/* Main Content with conditional blur */}
                <div style={{
                    filter: shouldShowPaywall ? 'blur(4px)' : 'none',
                    pointerEvents: shouldShowPaywall ? 'none' : 'auto',
                    transition: 'filter 0.3s ease'
                }}>
                    {/* Modern Welcome Header */}
                    <div className="position-relative overflow-hidden" style={{
                        background: '#FFFFFF',
                        borderRadius: '0 0 40px 40px',
                        marginBottom: '2rem',
                        border: '1px solid #E2E8F0'
                    }}>
                        {/* Animated Background Elements */}
                        <div style={{
                            position: 'absolute',
                            top: '-50%',
                            right: '-10%',
                            width: '300px',
                            height: '300px',
                            background: `${colors.PRIMARY.pastel}40`,
                            borderRadius: '50%',
                            filter: 'blur(60px)'
                        }} />
                        <div style={{
                            position: 'absolute',
                            bottom: '-30%',
                            left: '-5%',
                            width: '200px',
                            height: '200px',
                            background: `${colors.SECONDARY.pastel}60`,
                            borderRadius: '50%',
                            filter: 'blur(40px)'
                        }} />

                        <Container className="px-4 py-5 position-relative">
                            <Row className="align-items-center">
                                <Col lg={12}>
                                    <div className="d-flex align-items-center mb-4">
                                        <div className="p-3 rounded-4 me-4" style={{
                                            backgroundColor: colors.PRIMARY.pastel,
                                            border: `1px solid ${colors.PRIMARY.primary}20`
                                        }}>
                                            <FileText size={32} style={{ color: colors.PRIMARY.primary }} />
                                        </div>
                                        <div>
                                            <h1 className="display-5 fw-bold mb-1" style={{ color: '#1F2937' }}>
                                                Hair Reports History
                                            </h1>
                                            <p className="fs-6 mb-0" style={{ color: '#6B7280' }}>
                                                Track your hair health journey with detailed diagnostic reports
                                            </p>
                                        </div>
                                    </div>
                                </Col>
                            </Row>
                        </Container>
                    </div>

                    <Container className="px-4">
                        {/* Stats Cards */}
                        <Row className="g-4 mb-4">
                            <Col md={4}>
                                <Card className="h-100 border-0 shadow-sm" style={{ borderRadius: '16px' }}>
                                    <Card.Body className="p-4">
                                        <div className="d-flex align-items-center mb-3">
                                            <div className="p-2 rounded-3 me-3" style={{
                                                backgroundColor: colors.PRIMARY.pastel,
                                                color: colors.PRIMARY.primary
                                            }}>
                                                <BarChart3 size={20} />
                                            </div>
                                            <h5 className="fw-bold mb-0" style={{ color: '#1F2937' }}>Diagnostic Reports</h5>
                                        </div>
                                        <p className="text-muted mb-0">
                                            View detailed analysis of your hair health metrics and personalized recommendations from each assessment.
                                        </p>
                                    </Card.Body>
                                </Card>
                            </Col>
                            <Col md={4}>
                                <Card className="h-100 border-0 shadow-sm" style={{ borderRadius: '16px' }}>
                                    <Card.Body className="p-4">
                                        <div className="d-flex align-items-center mb-3">
                                            <div className="p-2 rounded-3 me-3" style={{
                                                backgroundColor: colors.SECONDARY.pastel,
                                                color: colors.SECONDARY.primary
                                            }}>
                                                <TrendingUp size={20} />
                                            </div>
                                            <h5 className="fw-bold mb-0" style={{ color: '#1F2937' }}>Progress Tracking</h5>
                                        </div>
                                        <p className="text-muted mb-0">
                                            Monitor changes in your hair health over time and see how your care routine is improving your results.
                                        </p>
                                    </Card.Body>
                                </Card>
                            </Col>
                            <Col md={4}>
                                <Card className="h-100 border-0 shadow-sm" style={{ borderRadius: '16px' }}>
                                    <Card.Body className="p-4">
                                        <div className="d-flex align-items-center mb-3">
                                            <div className="p-2 rounded-3 me-3" style={{
                                                backgroundColor: colors.GREEN.pastel,
                                                color: colors.GREEN.primary
                                            }}>
                                                <Clock size={20} />
                                            </div>
                                            <h5 className="fw-bold mb-0" style={{ color: '#1F2937' }}>Historical Data</h5>
                                        </div>
                                        <p className="text-muted mb-0">
                                            Access all your previous assessments with timestamps to understand your hair journey timeline.
                                        </p>
                                    </Card.Body>
                                </Card>
                            </Col>
                        </Row>

                        {/* Reports List */}
                        <UserSessionList userId={userId || 0} />
                    </Container>

                    {/* Bottom Padding */}
                    <div style={{ paddingBottom: '4rem' }}></div>
                </div>
            </DashboardLayout>
        </Container>
    );
};

export default ReportHistory;
