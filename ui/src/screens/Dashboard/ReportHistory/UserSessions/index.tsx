import React, { useEffect, useState } from "react";
import { AxiosError } from "axios";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Badge, Row, Col } from "react-bootstrap";
import { RefreshCw, Calendar, Bar<PERSON>hart3, Clock, AlertCircle } from "lucide-react";
import api from "../../../../api";
import ScoresModal from "../../../../components/ScoresModal";
import colors from "../../../../styles/colors";

export interface SessionGuidWithTimestamp {
  session_guid: string;
  updated_at: string; // ISO string
}

export interface SessionGuidWithCompletionStatus {
  session_guid: string;
  updated_at: string; // ISO string
  is_completed: boolean;
  replies_count: number;
  total_questions: number;
}

interface MessageOut {
  message: string;
}

interface Props {
  userId: number | string;
  onSelect?: (sessionGuid: string) => void;
}


const UserSessionTable: React.FC<Props> = ({ userId, onSelect }) => {
  const [sessions, setSessions] = useState<SessionGuidWithCompletionStatus[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [refresh, setRefresh] = useState(0);

  useEffect(() => {
    // Only fetch if we have a valid user ID
    if (!userId || (typeof userId === 'number' && userId <= 0)) {
      console.warn('UserSessionTable: No valid user ID provided');
      setLoading(false);
      setError('No valid user ID provided');
      return;
    }

    let mounted = true;
    setLoading(true);

    console.log(`UserSessionTable: Fetching sessions for user ID: ${userId}`);

    api.get<SessionGuidWithCompletionStatus[]>(`/quiz/replies/sessions/user/${userId}/with-completion`)
      .then((res) => {
        if (!mounted) return;
        console.log(`UserSessionTable: Successfully fetched ${res.data.length} sessions for user ${userId}`);
        setSessions(res.data);
        setError(null);
      })
      .catch((err: AxiosError<MessageOut>) => {
        if (!mounted) return;
        console.error(`UserSessionTable: Error fetching sessions for user ${userId}:`, err);

        // Handle 404 specifically
        if (err.response?.status === 404) {
          console.log(`UserSessionTable: User ${userId} has no sessions yet (404)`);
          setSessions([]); // Set empty array instead of error
          setError(null);
        } else {
          const msg = err.response?.data?.message || err.message || "Unknown error";
          setError(msg);
        }
      })
      .finally(() => mounted && setLoading(false));
    return () => {
      mounted = false;
    };
  }, [userId, refresh]);

  if (loading)
    return (
      <Card className="border-0 shadow-sm" style={{ borderRadius: '16px' }}>
        <Card.Body className="p-5 text-center">
          <div className="d-inline-flex align-items-center justify-content-center p-3 mb-3 rounded-4"
            style={{ background: colors.PRIMARY.pastel }}>
            <Spinner
              animation="border"
              style={{
                color: colors.PRIMARY.primary,
                width: '2rem',
                height: '2rem'
              }}
            />
          </div>
          <p className="text-muted mb-0">Loading your session history...</p>
        </Card.Body>
      </Card>
    );

  if (error)
    return (
      <Card className="border-0 shadow-sm" style={{ borderRadius: '16px' }}>
        <Card.Body className="p-4">
          <Alert variant="danger" className="rounded-4 border-0 shadow-sm mb-0">
            <div className="d-flex align-items-center justify-content-between">
              <div className="d-flex align-items-center">
                <div className="p-2 rounded-circle me-3" style={{ background: 'rgba(220, 53, 69, 0.1)' }}>
                  <AlertCircle size={20} className="text-danger" />
                </div>
                <div>
                  <h5 className="mb-1">Unable to Load Sessions</h5>
                  <p className="mb-0">{error}</p>
                </div>
              </div>
              <Button
                variant="outline-danger"
                size="sm"
                onClick={() => setRefresh((r) => r + 1)}
                aria-label="Retry"
                className="rounded-pill"
              >
                <RefreshCw size={16} />
              </Button>
            </div>
          </Alert>
        </Card.Body>
      </Card>
    );

  if (sessions.length === 0)
    return (
      <Card className="border-0 shadow-sm" style={{ borderRadius: '16px' }}>
        <Card.Body className="p-5 text-center">
          <div className="d-inline-flex align-items-center justify-content-center p-3 mb-3 rounded-4"
            style={{ background: colors.BLUE.pastel }}>
            <BarChart3 size={32} style={{ color: colors.BLUE.primary }} />
          </div>
          <h5 className="fw-bold mb-2" style={{ color: '#1F2937' }}>No Reports Yet</h5>
          <p className="text-muted mb-4">
            Complete your first hair diagnostic assessment to start tracking your hair health journey.
          </p>
          <Button
            variant="primary"
            className="rounded-pill px-4"
            style={{
              background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
              border: 'none'
            }}
            onClick={() => window.location.href = '/quiz'}
          >
            Take Hair Assessment
          </Button>
        </Card.Body>
      </Card>
    );

  let user: any;
  const auth = JSON.parse(localStorage.getItem("auth") || "{}");
  if (auth) {
    user = auth?.user;
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  };

  return (
    <Card className="border-0 shadow-sm" style={{ borderRadius: '16px' }}>
      <Card.Header
        className="bg-white border-0 p-4"
        style={{ borderRadius: '16px 16px 0 0' }}
      >
        <div className="d-flex align-items-center">
          <div className="p-2 rounded-3 me-3" style={{
            backgroundColor: colors.PRIMARY.pastel,
            color: colors.PRIMARY.primary
          }}>
            <BarChart3 size={20} />
          </div>
          <div>
            <h5 className="fw-bold mb-1" style={{ color: '#1F2937' }}>Your Assessment History</h5>
            <p className="text-muted mb-0 small">
              {sessions.length} assessment{sessions.length !== 1 ? 's' : ''} completed
            </p>
          </div>
        </div>
      </Card.Header>

      <Card.Body className="p-0">
        {sessions.map((session, index) => {
          const { date, time } = formatDate(session.updated_at);
          return (
            <div
              key={session.session_guid}
              className={`p-4 ${index !== sessions.length - 1 ? 'border-bottom' : ''}`}
              style={{
                cursor: onSelect ? "pointer" : "default",
                transition: 'background-color 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (onSelect) e.currentTarget.style.backgroundColor = '#F8FAFC';
              }}
              onMouseLeave={(e) => {
                if (onSelect) e.currentTarget.style.backgroundColor = 'transparent';
              }}
              onClick={() => onSelect?.(session.session_guid)}
            >
              <Row className="align-items-center">
                <Col md={8}>
                  <div className="d-flex align-items-center">
                    <div className="p-2 rounded-3 me-3" style={{
                      backgroundColor: colors.SECONDARY.pastel,
                      color: colors.SECONDARY.primary
                    }}>
                      <Calendar size={16} />
                    </div>
                    <div>
                      <h6 className="fw-semibold mb-1" style={{ color: '#1F2937' }}>
                        Hair Assessment #{sessions.length - index}
                      </h6>
                      <div className="d-flex align-items-center text-muted small">
                        <Clock size={14} className="me-1" />
                        {date} at {time}
                      </div>
                    </div>
                  </div>
                </Col>
                <Col md={4} className="text-md-end">
                  <div className="d-flex align-items-center justify-content-md-end gap-2">
                    <Badge
                      bg={session.is_completed ? "success" : "warning"}
                      className="rounded-pill px-2 py-1"
                      style={{ fontSize: '0.75rem' }}
                    >
                      {session.is_completed ? "Completed" : `In Progress (${session.replies_count}/${session.total_questions})`}
                    </Badge>
                    {session.is_completed ? (
                      <ScoresModal
                        sessionGuid={session.session_guid}
                        userId={user.id}
                        buttonLabel="View Report"
                        variant="outline-primary"
                      />
                    ) : (
                      <Button
                        variant="outline-secondary"
                        size="sm"
                        className="rounded-pill px-3"
                        onClick={() => {
                          // Store the session GUID in localStorage for restoration
                          localStorage.setItem('continueSessionGuid', session.session_guid);
                          window.location.href = '/quiz';
                        }}
                        style={{ fontSize: '0.875rem' }}
                      >
                        Continue Quiz
                      </Button>
                    )}
                  </div>
                </Col>
              </Row>
            </div>
          );
        })}
      </Card.Body>
    </Card>
  );
};

export default UserSessionTable;
