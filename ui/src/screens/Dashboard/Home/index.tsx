import React, { Suspense } from "react";
import { Contain<PERSON>, <PERSON>, Col, Card } from "react-bootstrap";
import Skeleton from "react-loading-skeleton";
import 'react-loading-skeleton/dist/skeleton.css';
import {
  TrendingUp,
  Target,
  Activity,
  AlertCircle,
  Award,
  BarChart3,
  ChevronRight,
  <PERSON>rkles,
  CheckCircle
} from "lucide-react";

import DashboardLayout from "../../../layouts/DashboardLayout";
import useScale from "../../../hooks/useScale";
import { useLocalUser } from "../../../hooks/api/useLocalUser";
import { useUserSessionGuid, useUserSessionsWithCompletion } from "../../../hooks/api/useUserSessionGuid";
import { useQuestionnaireStatus } from "../../../hooks/api/useQuestionnaireStatus";
import Button from "../../../components/Button";
import colors from "../../../styles/colors";
import ScoresDashboard from "../../../components/ScoresDashboard";
import SessionReplies from "../../../components/SessionReplies";
import TopIssue from "../../../components/TopIssue";
import ScalpStatusCard from "../../../components/ScalpStatusCard";
import AggregatedUserPromptReplies from "../AggregatedUserPromptReplies";
import QuestionnairePaywall from "../../../components/QuestionnairePaywall";

const pageStyle: React.CSSProperties = {
  backgroundColor: "#FAFBFC",
  minHeight: "100vh",
  color: "#1F2937",
};

const tipCategories = [
  "Environmental",
  "Diet & Nutrition",
  "Health & Fitness",
  "Hair Condition",
];

const Home = () => {
  const { isMobile } = useScale();

  const {
    data: user,
    isLoading: userLoading,
    isError: userError,
  } = useLocalUser();

  const userId = user?.id;

  // Fetch sessions with completion status
  const {
    data: sessionsWithCompletion,
    isLoading: sessionsLoading,
    isError: sessionsError,
  } = useUserSessionsWithCompletion(userId && userId > 0 ? userId : undefined);

  // Check questionnaire completion status
  const { data: questionnaireStatus, isLoading: statusLoading } = useQuestionnaireStatus(userId);

  // Find the most recent completed session
  const completedSessions = sessionsWithCompletion?.filter(session => session.is_completed) || [];
  const mostRecentCompletedSession = completedSessions.length > 0 ? completedSessions[0] : null;
  const sessionGuid = mostRecentCompletedSession?.session_guid ?? "";

  // Find the most recent session (regardless of completion)
  const mostRecentSession = sessionsWithCompletion && sessionsWithCompletion.length > 0 ? sessionsWithCompletion[0] : null;
  const hasIncompleteLatestSession = mostRecentSession && !mostRecentSession.is_completed;

  // Determine if user has any session data
  const hasSessionData = sessionsWithCompletion && sessionsWithCompletion.length > 0;
  const hasCompletedQuizData = completedSessions.length > 0;

  // Show paywall only if user has no session data AND hasn't completed questionnaire
  const shouldShowPaywall = !statusLoading && !hasSessionData && questionnaireStatus?.status !== 'Completed';

  // Debug logging
  console.log('Dashboard Debug:', {
    userId,
    hasSessionData,
    sessionsCount: sessionsWithCompletion?.length || 0,
    completedSessionsCount: completedSessions.length,
    questionnaireStatus: questionnaireStatus?.status,
    hasCompletedQuizData,
    hasIncompleteLatestSession,
    shouldShowPaywall,
    statusLoading,
    sessionGuid,
    mostRecentSession: mostRecentSession ? {
      guid: mostRecentSession.session_guid,
      completed: mostRecentSession.is_completed,
      progress: `${mostRecentSession.replies_count}/${mostRecentSession.total_questions}`
    } : null
  });

  // Handle navigation to questionnaire
  const handleStartQuestionnaire = () => {
    if (questionnaireStatus?.status === 'Started' || hasIncompleteLatestSession) {
      window.location.href = '/questionaire/questions';
    } else {
      window.location.href = '/quiz';
    }
  };

  if (userLoading || sessionsLoading || statusLoading) return <Skeleton height={200} />;
  if (userError || sessionsError) return <p>Error loading user/session data.</p>;

  return (
    <Container fluid style={pageStyle}>
      <DashboardLayout>
        {/* Questionnaire Paywall Overlay */}
        {shouldShowPaywall && (
          <QuestionnairePaywall
            onStartQuestionnaire={handleStartQuestionnaire}
            completedQuestions={questionnaireStatus?.completed_questions}
            totalQuestions={questionnaireStatus?.total_questions}
            status={questionnaireStatus?.status}
          />
        )}

        {/* Main Dashboard Content with conditional blur */}
        <div style={{
          filter: shouldShowPaywall ? 'blur(4px)' : 'none',
          pointerEvents: shouldShowPaywall ? 'none' : 'auto',
          transition: 'filter 0.3s ease'
        }}>
          {/* Modern Welcome Header */}
          <div className="position-relative overflow-hidden" style={{
            background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
            borderRadius: '0 0 40px 40px',
            marginBottom: '2rem'
          }}>
            {/* Animated Background Elements */}
            <div style={{
              position: 'absolute',
              top: '-50%',
              right: '-10%',
              width: '300px',
              height: '300px',
              background: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '50%',
              filter: 'blur(60px)'
            }} />
            <div style={{
              position: 'absolute',
              bottom: '-30%',
              left: '-5%',
              width: '200px',
              height: '200px',
              background: 'rgba(255, 255, 255, 0.08)',
              borderRadius: '50%',
              filter: 'blur(40px)'
            }} />

            <Container className="py-5 position-relative" style={{ zIndex: 2 }}>
              <Row className="align-items-center">
                <Col lg={8}>
                  <div className="text-white">
                    {/* Greeting with Time-based Message */}
                    <div className="d-flex align-items-center mb-3">
                      <div
                        className="p-3 rounded-4 me-3"
                        style={{
                          background: 'rgba(255, 255, 255, 0.2)',
                          backdropFilter: 'blur(20px)',
                          border: '1px solid rgba(255, 255, 255, 0.3)'
                        }}
                      >
                        <Sparkles size={28} style={{ color: 'white' }} />
                      </div>
                      <div>
                        <h1 className="display-5 fw-bold mb-1">
                          {(() => {
                            const hour = new Date().getHours();
                            if (hour < 12) return 'Good morning';
                            if (hour < 17) return 'Good afternoon';
                            return 'Good evening';
                          })()}, {user?.first_name || 'there'}!
                        </h1>
                        <p className="fs-6 mb-0 opacity-90">
                          Ready to continue your hair care journey?
                        </p>
                      </div>
                    </div>

                    {/* Quick Stats Row */}
                    <Row className="g-3 mb-4">
                      <Col md={4}>
                        <div
                          className="p-3 rounded-3 h-100"
                          style={{
                            background: 'rgba(255, 255, 255, 0.15)',
                            backdropFilter: 'blur(10px)',
                            border: '1px solid rgba(255, 255, 255, 0.2)'
                          }}
                        >
                          <div className="d-flex align-items-center">
                            <div className="p-2 rounded-3 me-3" style={{ backgroundColor: 'rgba(255,255,255,0.3)' }}>
                              <Target size={20} style={{ color: 'white' }} />
                            </div>
                            <div>
                              <div className="fw-bold fs-5">{completedSessions.length || 0}</div>
                              <div className="fs-7 opacity-90">Analysis Complete</div>
                            </div>
                          </div>
                        </div>
                      </Col>
                      <Col md={4}>
                        <div
                          className="p-3 rounded-3 h-100"
                          style={{
                            background: 'rgba(255, 255, 255, 0.15)',
                            backdropFilter: 'blur(10px)',
                            border: '1px solid rgba(255, 255, 255, 0.2)'
                          }}
                        >
                          <div className="d-flex align-items-center">
                            <div className="p-2 rounded-3 me-3" style={{ backgroundColor: 'rgba(255,255,255,0.3)' }}>
                              <TrendingUp size={20} style={{ color: 'white' }} />
                            </div>
                            <div>
                              <div className="fw-bold fs-5">78%</div>
                              <div className="fs-7 opacity-90">Health Score</div>
                            </div>
                          </div>
                        </div>
                      </Col>
                      <Col md={4}>
                        <div
                          className="p-3 rounded-3 h-100"
                          style={{
                            background: 'rgba(255, 255, 255, 0.15)',
                            backdropFilter: 'blur(10px)',
                            border: '1px solid rgba(255, 255, 255, 0.2)'
                          }}
                        >
                          <div className="d-flex align-items-center">
                            <div className="p-2 rounded-3 me-3" style={{ backgroundColor: 'rgba(255,255,255,0.3)' }}>
                              <Award size={20} style={{ color: 'white' }} />
                            </div>
                            <div>
                              <div className="fw-bold fs-5">12</div>
                              <div className="fs-7 opacity-90">Days Streak</div>
                            </div>
                          </div>
                        </div>
                      </Col>
                    </Row>

                    {/* Action Buttons */}
                    <div className="d-flex flex-wrap gap-3">
                      <Button
                        className="px-4 py-2 border-0"
                        style={{
                          background: 'rgba(255, 255, 255, 0.2)',
                          backdropFilter: 'blur(10px)',
                          borderRadius: '12px',
                          color: 'white',
                          fontWeight: '600',
                          border: '1px solid rgba(255, 255, 255, 0.3)',
                          transition: 'all 0.2s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.3)';
                          e.currentTarget.style.transform = 'translateY(-2px)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
                          e.currentTarget.style.transform = 'translateY(0)';
                        }}
                      >
                        <ChevronRight size={16} className="me-2" />
                        View Recommendations
                      </Button>
                      <Button
                        className="px-4 py-2 border-0"
                        style={{
                          background: 'transparent',
                          borderRadius: '12px',
                          color: 'white',
                          fontWeight: '600',
                          border: '1px solid rgba(255, 255, 255, 0.3)',
                          transition: 'all 0.2s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                          e.currentTarget.style.transform = 'translateY(-2px)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = 'transparent';
                          e.currentTarget.style.transform = 'translateY(0)';
                        }}
                      >
                        <Activity size={16} className="me-2" />
                        Retake Quiz
                      </Button>
                    </div>
                  </div>
                </Col>
                <Col lg={4} className="text-center mt-4 mt-lg-0">
                  <div className="position-relative">
                    {/* Floating Achievement Card */}
                    <div
                      className="p-4 rounded-4 d-inline-block"
                      style={{
                        background: 'rgba(255, 255, 255, 0.2)',
                        backdropFilter: 'blur(20px)',
                        border: '1px solid rgba(255, 255, 255, 0.3)',
                        boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)'
                      }}
                    >
                      <div className="text-white text-center">
                        <div className="p-3 rounded-4 d-inline-flex mb-3" style={{ backgroundColor: 'rgba(255,255,255,0.3)' }}>
                          <Award size={32} style={{ color: 'white' }} />
                        </div>
                        <h4 className="fw-bold mb-2">Hair Health Champion</h4>
                        <p className="fs-6 mb-3 opacity-90">
                          You're on track with your hair care routine!
                        </p>
                        <div className="d-flex justify-content-center gap-2">
                          <div className="p-1 rounded-circle" style={{ backgroundColor: 'rgba(255,255,255,0.4)', width: '8px', height: '8px' }} />
                          <div className="p-1 rounded-circle" style={{ backgroundColor: 'rgba(255,255,255,0.4)', width: '8px', height: '8px' }} />
                          <div className="p-1 rounded-circle" style={{ backgroundColor: 'rgba(255,255,255,0.8)', width: '8px', height: '8px' }} />
                        </div>
                      </div>
                    </div>
                  </div>
                </Col>
              </Row>
            </Container>
          </div>

          <Container className="px-4">
            {/* Incomplete Quiz Notification */}
            {hasIncompleteLatestSession && (
              <Row className="mb-4">
                <Col>
                  <Card className="border-0 shadow-sm" style={{
                    borderRadius: '16px',
                    background: `linear-gradient(135deg, ${colors.BLUE.pastel} 0%, #f7faff 100%)`
                  }}>
                    <Card.Body className="p-4">
                      <div className="d-flex align-items-center justify-content-between">
                        <div className="d-flex align-items-center">
                          <div className="p-3 rounded-3 me-3" style={{
                            backgroundColor: 'rgba(255, 255, 255, 0.8)',
                            color: colors.BLUE.primary
                          }}>
                            <AlertCircle size={24} />
                          </div>
                          <div>
                            <h5 className="fw-bold mb-1" style={{ color: colors.BLUE.primary }}>
                              Complete Your Hair Assessment
                            </h5>
                            <p className="text-muted mb-0">
                              You have an incomplete assessment ({mostRecentSession?.replies_count}/{mostRecentSession?.total_questions} questions answered).
                              Complete it to see your updated hair health metrics and personalized recommendations.
                            </p>
                          </div>
                        </div>
                        <Button
                          className="px-4 py-2 border-0 rounded-pill"
                          style={{
                            background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
                            color: 'white',
                            fontWeight: '600',
                            transition: 'all 0.2s ease'
                          }}
                          onClick={handleStartQuestionnaire}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = '0 8px 25px rgba(67, 0, 255, 0.3)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = 'none';
                          }}
                        >
                          Continue Assessment
                        </Button>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>
            )}

            {/* Modern Scores Dashboard - Only show if user has completed quiz data */}
            {hasCompletedQuizData ? (
              <Row className="mb-4">
                <Col>
                  <Suspense fallback={<Skeleton height={300} className="rounded-4" />}>
                    <ScoresDashboard userId={String(userId)} sessionGuid={sessionGuid} />
                  </Suspense>
                </Col>
              </Row>
            ) : !hasIncompleteLatestSession && (
              <Row className="mb-4">
                <Col>
                  <Card className="border-0 shadow-sm text-center" style={{ borderRadius: '16px' }}>
                    <Card.Body className="p-5">
                      <div className="d-inline-flex align-items-center justify-content-center p-3 mb-3 rounded-4"
                        style={{ background: colors.PRIMARY.pastel }}>
                        <BarChart3 size={32} style={{ color: colors.PRIMARY.primary }} />
                      </div>
                      <h5 className="fw-bold mb-2" style={{ color: '#1F2937' }}>No Hair Analysis Yet</h5>
                      <p className="text-muted mb-4">
                        Take your first hair diagnostic assessment to see personalized metrics and recommendations.
                      </p>
                      <Button
                        className="px-4 py-2 border-0 rounded-pill"
                        style={{
                          background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
                          color: 'white',
                          fontWeight: '600'
                        }}
                        onClick={handleStartQuestionnaire}
                      >
                        Start Hair Assessment
                      </Button>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>
            )}

            {/* Hair Profile - Detailed Informative Cards - Only show if user has completed quiz data */}
            {hasCompletedQuizData && (
              <Card className="border-0 shadow-sm mb-4" style={{ borderRadius: '20px' }}>
                <Card.Body className="p-4">
                  <div className="d-flex align-items-center justify-content-between mb-4">
                    <div className="d-flex align-items-center">
                      <div className="p-2 rounded-3 me-3" style={{ backgroundColor: colors.PRIMARY.pastel }}>
                        <Activity size={24} style={{ color: colors.PRIMARY.primary }} />
                      </div>
                      <div>
                        <h2 className="fs-5 fw-bold mb-1" style={{ color: '#1F2937' }}>Your Hair Profile</h2>
                        <p className="text-muted mb-0 fs-6">Understanding your unique hair characteristics for better care</p>
                      </div>
                    </div>
                    <div className="d-flex align-items-center">
                      <span className="badge rounded-pill px-3 py-2" style={{
                        backgroundColor: colors.PRIMARY.pastel,
                        color: colors.PRIMARY.primary,
                        fontSize: '12px',
                        fontWeight: '600'
                      }}>
                        Complete Profile
                      </span>
                    </div>
                  </div>

                  <Row className="g-4">
                    {/* Hair Type Card */}
                    <Col md={6} lg={3}>
                      <Card className="h-100 border-0" style={{
                        borderRadius: '16px',
                        backgroundColor: '#F8FAFC',
                        border: '1px solid #E2E8F0'
                      }}>
                        <Card.Body className="p-4">
                          <div className="d-flex align-items-center mb-3">
                            <div className="p-2 rounded-3 me-3" style={{
                              backgroundColor: colors.PRIMARY.pastel,
                              color: colors.PRIMARY.primary
                            }}>
                              <Activity size={18} />
                            </div>
                            <h6 className="fw-bold mb-0" style={{ color: '#1F2937' }}>Hair Type</h6>
                          </div>

                          <div className="mb-3">
                            <SessionReplies sessionGuid={sessionGuid} questionIds={[6]} hairFeature="" />
                          </div>

                          <div className="mb-3">
                            <p className="fs-7 text-muted mb-2">What this means:</p>
                            <p className="fs-7" style={{ color: '#4B5563', lineHeight: '1.4' }}>
                              Your hair type determines curl pattern and natural texture. This affects how oils travel down the hair shaft and influences styling approaches.
                            </p>
                          </div>

                          <div className="d-flex align-items-center">
                            <div className="flex-grow-1">
                              <div className="d-flex justify-content-between mb-1">
                                <span className="fs-8 text-muted">Care Complexity</span>
                                <span className="fs-8 fw-semibold" style={{ color: colors.PRIMARY.primary }}>Medium</span>
                              </div>
                              <div className="progress" style={{ height: '4px', borderRadius: '2px' }}>
                                <div
                                  className="progress-bar"
                                  style={{
                                    width: '60%',
                                    backgroundColor: colors.PRIMARY.primary,
                                    borderRadius: '2px'
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>

                    {/* Hair Density Card */}
                    <Col md={6} lg={3}>
                      <Card className="h-100 border-0" style={{
                        borderRadius: '16px',
                        backgroundColor: '#F8FAFC',
                        border: '1px solid #E2E8F0'
                      }}>
                        <Card.Body className="p-4">
                          <div className="d-flex align-items-center mb-3">
                            <div className="p-2 rounded-3 me-3" style={{
                              backgroundColor: colors.SECONDARY.pastel,
                              color: colors.SECONDARY.primary
                            }}>
                              <TrendingUp size={18} />
                            </div>
                            <h6 className="fw-bold mb-0" style={{ color: '#1F2937' }}>Hair Density</h6>
                          </div>

                          <div className="mb-3">
                            <SessionReplies sessionGuid={sessionGuid} questionIds={[8]} hairFeature="" />
                          </div>

                          <div className="mb-3">
                            <p className="fs-7 text-muted mb-2">What this means:</p>
                            <p className="fs-7" style={{ color: '#4B5563', lineHeight: '1.4' }}>
                              Hair density refers to how many hair strands you have per square inch. This affects volume, styling hold, and product application amounts.
                            </p>
                          </div>

                          <div className="d-flex align-items-center">
                            <div className="flex-grow-1">
                              <div className="d-flex justify-content-between mb-1">
                                <span className="fs-8 text-muted">Volume Potential</span>
                                <span className="fs-8 fw-semibold" style={{ color: colors.SECONDARY.primary }}>High</span>
                              </div>
                              <div className="progress" style={{ height: '4px', borderRadius: '2px' }}>
                                <div
                                  className="progress-bar"
                                  style={{
                                    width: '80%',
                                    backgroundColor: colors.SECONDARY.primary,
                                    borderRadius: '2px'
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>

                    {/* Hair Texture Card */}
                    <Col md={6} lg={3}>
                      <Card className="h-100 border-0" style={{
                        borderRadius: '16px',
                        backgroundColor: '#F8FAFC',
                        border: '1px solid #E2E8F0'
                      }}>
                        <Card.Body className="p-4">
                          <div className="d-flex align-items-center mb-3">
                            <div className="p-2 rounded-3 me-3" style={{
                              backgroundColor: colors.GREEN.pastel,
                              color: colors.GREEN.primary
                            }}>
                              <Sparkles size={18} />
                            </div>
                            <h6 className="fw-bold mb-0" style={{ color: '#1F2937' }}>Hair Texture</h6>
                          </div>

                          <div className="mb-3">
                            <SessionReplies sessionGuid={sessionGuid} questionIds={[9]} hairFeature="" />
                          </div>

                          <div className="mb-3">
                            <p className="fs-7 text-muted mb-2">What this means:</p>
                            <p className="fs-7" style={{ color: '#4B5563', lineHeight: '1.4' }}>
                              Hair texture describes the thickness of individual strands. Fine hair needs lightweight products, while coarse hair can handle heavier formulations.
                            </p>
                          </div>

                          <div className="d-flex align-items-center">
                            <div className="flex-grow-1">
                              <div className="d-flex justify-content-between mb-1">
                                <span className="fs-8 text-muted">Product Weight</span>
                                <span className="fs-8 fw-semibold" style={{ color: colors.GREEN.primary }}>Light</span>
                              </div>
                              <div className="progress" style={{ height: '4px', borderRadius: '2px' }}>
                                <div
                                  className="progress-bar"
                                  style={{
                                    width: '40%',
                                    backgroundColor: colors.GREEN.primary,
                                    borderRadius: '2px'
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>

                    {/* Hair Porosity Card */}
                    <Col md={6} lg={3}>
                      <Card className="h-100 border-0" style={{
                        borderRadius: '16px',
                        backgroundColor: '#F8FAFC',
                        border: '1px solid #E2E8F0'
                      }}>
                        <Card.Body className="p-4">
                          <div className="d-flex align-items-center mb-3">
                            <div className="p-2 rounded-3 me-3" style={{
                              backgroundColor: '#FEF3E2',
                              color: '#F59E0B'
                            }}>
                              <Target size={18} />
                            </div>
                            <h6 className="fw-bold mb-0" style={{ color: '#1F2937' }}>Hair Porosity</h6>
                          </div>

                          <div className="mb-3">
                            <SessionReplies sessionGuid={sessionGuid} questionIds={[37]} hairFeature="" />
                          </div>

                          <div className="mb-3">
                            <p className="fs-7 text-muted mb-2">What this means:</p>
                            <p className="fs-7" style={{ color: '#4B5563', lineHeight: '1.4' }}>
                              Porosity measures how well your hair absorbs and retains moisture. This determines drying time, product absorption, and moisture retention strategies.
                            </p>
                          </div>

                          <div className="d-flex align-items-center">
                            <div className="flex-grow-1">
                              <div className="d-flex justify-content-between mb-1">
                                <span className="fs-8 text-muted">Moisture Retention</span>
                                <span className="fs-8 fw-semibold" style={{ color: '#F59E0B' }}>Moderate</span>
                              </div>
                              <div className="progress" style={{ height: '4px', borderRadius: '2px' }}>
                                <div
                                  className="progress-bar"
                                  style={{
                                    width: '65%',
                                    backgroundColor: '#F59E0B',
                                    borderRadius: '2px'
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>
                  </Row>

                  {/* Educational Footer */}
                  <div className="mt-4 p-3 rounded-3" style={{ backgroundColor: colors.PRIMARY.pastel }}>
                    <div className="d-flex align-items-start">
                      <div className="p-2 rounded-3 me-3" style={{ backgroundColor: colors.WHITE.primary }}>
                        <Activity size={20} style={{ color: colors.PRIMARY.primary }} />
                      </div>
                      <div className="flex-grow-1">
                        <h6 className="fw-bold mb-2" style={{ color: colors.PRIMARY.primary }}>
                          Why Your Hair Profile Matters
                        </h6>
                        <p className="fs-6 mb-2" style={{ color: '#4B5563', lineHeight: '1.5' }}>
                          Understanding these four key characteristics helps us recommend the right products, techniques, and routines for your specific hair needs. Each factor influences how your hair behaves and what care approach works best.
                        </p>
                        <div className="d-flex flex-wrap gap-2 mt-3">
                          <span className="badge rounded-pill px-3 py-1" style={{
                            backgroundColor: colors.WHITE.primary,
                            color: colors.PRIMARY.primary,
                            fontSize: '11px',
                            fontWeight: '500'
                          }}>
                            Personalized Care
                          </span>
                          <span className="badge rounded-pill px-3 py-1" style={{
                            backgroundColor: colors.WHITE.primary,
                            color: colors.PRIMARY.primary,
                            fontSize: '11px',
                            fontWeight: '500'
                          }}>
                            Product Selection
                          </span>
                          <span className="badge rounded-pill px-3 py-1" style={{
                            backgroundColor: colors.WHITE.primary,
                            color: colors.PRIMARY.primary,
                            fontSize: '11px',
                            fontWeight: '500'
                          }}>
                            Styling Techniques
                          </span>
                          <span className="badge rounded-pill px-3 py-1" style={{
                            backgroundColor: colors.WHITE.primary,
                            color: colors.PRIMARY.primary,
                            fontSize: '11px',
                            fontWeight: '500'
                          }}>
                            Routine Optimization
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            )}

            {/* Hair Goals - Action-Oriented Cards - Only show if user has completed quiz data */}
            {hasCompletedQuizData && (
              <>
                <Card className="border-0 shadow-sm mb-4" style={{ borderRadius: '20px' }}>
                  <Card.Body className="p-4">
                    <div className="d-flex align-items-center justify-content-between mb-4">
                      <div className="d-flex align-items-center">
                        <div className="p-2 rounded-3 me-3" style={{ backgroundColor: colors.SECONDARY.pastel }}>
                          <Target size={24} style={{ color: colors.SECONDARY.primary }} />
                        </div>
                        <div>
                          <h2 className="fs-5 fw-bold mb-1" style={{ color: '#1F2937' }}>Your Hair Goals</h2>
                          <p className="text-muted mb-0 fs-6">Track progress and take action</p>
                        </div>
                      </div>
                      <div className="d-flex align-items-center">
                        <span className="badge rounded-pill px-3 py-2" style={{
                          backgroundColor: colors.PRIMARY.pastel,
                          color: colors.PRIMARY.primary,
                          fontSize: '12px',
                          fontWeight: '600'
                        }}>
                          3 Active Goals
                        </span>
                      </div>
                    </div>

                    <Row className="g-4">
                      {/* Primary Goal Card */}
                      <Col md={4}>
                        <Card className="h-100 border-0" style={{
                          borderRadius: '16px',
                          backgroundColor: colors.PRIMARY.pastel,
                          border: `2px solid ${colors.PRIMARY.light}`,
                          transition: 'all 0.2s ease',
                          cursor: 'pointer'
                        }}>
                          <Card.Body className="p-4">
                            <div className="d-flex align-items-center justify-content-between mb-3">
                              <div className="p-2 rounded-3" style={{ backgroundColor: colors.WHITE.primary }}>
                                <Target size={20} style={{ color: colors.PRIMARY.primary }} />
                              </div>
                              <span className="badge rounded-pill" style={{
                                backgroundColor: colors.PRIMARY.primary,
                                color: colors.WHITE.primary,
                                fontSize: '10px'
                              }}>
                                PRIMARY
                              </span>
                            </div>

                            <h5 className="fw-bold mb-2" style={{ color: colors.PRIMARY.primary }}>
                              Primary Goal
                            </h5>

                            <div className="mb-3">
                              <SessionReplies sessionGuid={sessionGuid} questionIds={[1]} hairFeature="" />
                            </div>

                            <div className="mb-3">
                              <div className="d-flex justify-content-between align-items-center mb-2">
                                <span className="fs-7 text-muted">Progress</span>
                                <span className="fs-7 fw-semibold" style={{ color: colors.PRIMARY.primary }}>65%</span>
                              </div>
                              <div className="progress" style={{ height: '6px', borderRadius: '3px' }}>
                                <div
                                  className="progress-bar"
                                  style={{
                                    width: '65%',
                                    backgroundColor: colors.PRIMARY.primary,
                                    borderRadius: '3px'
                                  }}
                                />
                              </div>
                            </div>

                            <button
                              className="btn w-100 fw-semibold"
                              style={{
                                backgroundColor: colors.PRIMARY.primary,
                                color: colors.WHITE.primary,
                                borderRadius: '12px',
                                border: 'none',
                                padding: '10px',
                                fontSize: '14px',
                                transition: 'all 0.2s ease'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = colors.PRIMARY.dark;
                                e.currentTarget.style.transform = 'translateY(-2px)';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = colors.PRIMARY.primary;
                                e.currentTarget.style.transform = 'translateY(0)';
                              }}
                            >
                              <ChevronRight size={16} className="me-1" />
                              View Action Plan
                            </button>
                          </Card.Body>
                        </Card>
                      </Col>

                      {/* Focus Area Card */}
                      <Col md={4}>
                        <Card className="h-100 border-0" style={{
                          borderRadius: '16px',
                          backgroundColor: colors.SECONDARY.pastel,
                          border: `2px solid ${colors.SECONDARY.light}`,
                          transition: 'all 0.2s ease',
                          cursor: 'pointer'
                        }}>
                          <Card.Body className="p-4">
                            <div className="d-flex align-items-center justify-content-between mb-3">
                              <div className="p-2 rounded-3" style={{ backgroundColor: colors.WHITE.primary }}>
                                <Activity size={20} style={{ color: colors.SECONDARY.primary }} />
                              </div>
                              <span className="badge rounded-pill" style={{
                                backgroundColor: colors.SECONDARY.primary,
                                color: colors.WHITE.primary,
                                fontSize: '10px'
                              }}>
                                FOCUS
                              </span>
                            </div>

                            <h5 className="fw-bold mb-2" style={{ color: colors.SECONDARY.primary }}>
                              Focus Area
                            </h5>

                            <div className="mb-3">
                              <SessionReplies sessionGuid={sessionGuid} questionIds={[2]} hairFeature="" />
                            </div>

                            <div className="mb-3">
                              <div className="d-flex justify-content-between align-items-center mb-2">
                                <span className="fs-7 text-muted">Weekly Progress</span>
                                <span className="fs-7 fw-semibold" style={{ color: colors.SECONDARY.primary }}>4/7 days</span>
                              </div>
                              <div className="progress" style={{ height: '6px', borderRadius: '3px' }}>
                                <div
                                  className="progress-bar"
                                  style={{
                                    width: '57%',
                                    backgroundColor: colors.SECONDARY.primary,
                                    borderRadius: '3px'
                                  }}
                                />
                              </div>
                            </div>

                            <button
                              className="btn w-100 fw-semibold"
                              style={{
                                backgroundColor: colors.SECONDARY.primary,
                                color: colors.WHITE.primary,
                                borderRadius: '12px',
                                border: 'none',
                                padding: '10px',
                                fontSize: '14px',
                                transition: 'all 0.2s ease'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = colors.SECONDARY.dark;
                                e.currentTarget.style.transform = 'translateY(-2px)';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = colors.SECONDARY.primary;
                                e.currentTarget.style.transform = 'translateY(0)';
                              }}
                            >
                              <Sparkles size={16} className="me-1" />
                              Start Today's Routine
                            </button>
                          </Card.Body>
                        </Card>
                      </Col>

                      {/* Top Issue Card */}
                      <Col md={4}>
                        <Card className="h-100 border-0" style={{
                          borderRadius: '16px',
                          backgroundColor: colors.GREEN.pastel,
                          border: `2px solid #BBF7D0`,
                          transition: 'all 0.2s ease',
                          cursor: 'pointer'
                        }}>
                          <Card.Body className="p-4">
                            <div className="d-flex align-items-center justify-content-between mb-3">
                              <div className="p-2 rounded-3" style={{ backgroundColor: colors.WHITE.primary }}>
                                <Award size={20} style={{ color: colors.GREEN.primary }} />
                              </div>
                              <span className="badge rounded-pill" style={{
                                backgroundColor: colors.GREEN.primary,
                                color: colors.WHITE.primary,
                                fontSize: '10px'
                              }}>
                                PRIORITY
                              </span>
                            </div>

                            <h5 className="fw-bold mb-2" style={{ color: colors.GREEN.primary }}>
                              Top Priority
                            </h5>

                            <div className="mb-3">
                              <TopIssue userId={String(userId)} sessionGuid={sessionGuid} />
                            </div>

                            <div className="mb-3">
                              <div className="d-flex justify-content-between align-items-center mb-2">
                                <span className="fs-7 text-muted">Improvement</span>
                                <span className="fs-7 fw-semibold" style={{ color: colors.GREEN.primary }}>+12% this week</span>
                              </div>
                              <div className="progress" style={{ height: '6px', borderRadius: '3px' }}>
                                <div
                                  className="progress-bar"
                                  style={{
                                    width: '78%',
                                    backgroundColor: colors.GREEN.primary,
                                    borderRadius: '3px'
                                  }}
                                />
                              </div>
                            </div>

                            <button
                              className="btn w-100 fw-semibold"
                              style={{
                                backgroundColor: colors.GREEN.primary,
                                color: colors.WHITE.primary,
                                borderRadius: '12px',
                                border: 'none',
                                padding: '10px',
                                fontSize: '14px',
                                transition: 'all 0.2s ease'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = '#059669';
                                e.currentTarget.style.transform = 'translateY(-2px)';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = colors.GREEN.primary;
                                e.currentTarget.style.transform = 'translateY(0)';
                              }}
                            >
                              <TrendingUp size={16} className="me-1" />
                              Track Progress
                            </button>
                          </Card.Body>
                        </Card>
                      </Col>
                    </Row>
                  </Card.Body>
                </Card>

                {/* Scalp Health - Comprehensive Analysis */}
                <Card className="border-0 shadow-sm mb-4" style={{ borderRadius: '20px' }}>
                  <Card.Body className="p-4">
                    <div className="d-flex align-items-center justify-content-between mb-4">
                      <div className="d-flex align-items-center">
                        <div className="p-2 rounded-3 me-3" style={{ backgroundColor: colors.GREEN.pastel }}>
                          <TrendingUp size={24} style={{ color: colors.GREEN.primary }} />
                        </div>
                        <div>
                          <h2 className="fs-5 fw-bold mb-1" style={{ color: '#1F2937' }}>Scalp Health Analysis</h2>
                          <p className="text-muted mb-0 fs-6">Comprehensive scalp condition assessment and recommendations</p>
                        </div>
                      </div>
                      <div className="d-flex align-items-center">
                        <span className="badge rounded-pill px-3 py-2" style={{
                          backgroundColor: colors.GREEN.pastel,
                          color: colors.GREEN.primary,
                          fontSize: '12px',
                          fontWeight: '600'
                        }}>
                          Health Score: 78%
                        </span>
                      </div>
                    </div>

                    {/* Main Scalp Status */}
                    <div className="mb-4">
                      <ScalpStatusCard userId={String(userId)} sessionGuid={sessionGuid} />
                    </div>

                    {/* Scalp Health Metrics Grid */}
                    <Row className="g-3 mb-4">
                      <Col md={6} lg={3}>
                        <Card className="h-100 border-0" style={{
                          borderRadius: '12px',
                          backgroundColor: '#F0FDF4',
                          border: '1px solid #BBF7D0'
                        }}>
                          <Card.Body className="p-3 text-center">
                            <div className="mb-2">
                              <div className="p-2 rounded-circle d-inline-flex" style={{
                                backgroundColor: colors.WHITE.primary,
                                color: colors.GREEN.primary
                              }}>
                                <Activity size={16} />
                              </div>
                            </div>
                            <h6 className="fw-bold mb-1" style={{ color: colors.GREEN.primary }}>Oil Balance</h6>
                            <div className="fs-5 fw-bold mb-1" style={{ color: colors.GREEN.primary }}>Good</div>
                            <div className="progress" style={{ height: '4px', borderRadius: '2px' }}>
                              <div
                                className="progress-bar"
                                style={{
                                  width: '75%',
                                  backgroundColor: colors.GREEN.primary,
                                  borderRadius: '2px'
                                }}
                              />
                            </div>
                            <p className="fs-8 text-muted mt-2 mb-0">Balanced sebum production</p>
                          </Card.Body>
                        </Card>
                      </Col>

                      <Col md={6} lg={3}>
                        <Card className="h-100 border-0" style={{
                          borderRadius: '12px',
                          backgroundColor: '#FFFBEB',
                          border: '1px solid #FDE68A'
                        }}>
                          <Card.Body className="p-3 text-center">
                            <div className="mb-2">
                              <div className="p-2 rounded-circle d-inline-flex" style={{
                                backgroundColor: colors.WHITE.primary,
                                color: '#F59E0B'
                              }}>
                                <Target size={16} />
                              </div>
                            </div>
                            <h6 className="fw-bold mb-1" style={{ color: '#F59E0B' }}>Sensitivity</h6>
                            <div className="fs-5 fw-bold mb-1" style={{ color: '#F59E0B' }}>Low</div>
                            <div className="progress" style={{ height: '4px', borderRadius: '2px' }}>
                              <div
                                className="progress-bar"
                                style={{
                                  width: '30%',
                                  backgroundColor: '#F59E0B',
                                  borderRadius: '2px'
                                }}
                              />
                            </div>
                            <p className="fs-8 text-muted mt-2 mb-0">Minimal irritation risk</p>
                          </Card.Body>
                        </Card>
                      </Col>

                      <Col md={6} lg={3}>
                        <Card className="h-100 border-0" style={{
                          borderRadius: '12px',
                          backgroundColor: colors.PRIMARY.pastel,
                          border: `1px solid ${colors.PRIMARY.light}`
                        }}>
                          <Card.Body className="p-3 text-center">
                            <div className="mb-2">
                              <div className="p-2 rounded-circle d-inline-flex" style={{
                                backgroundColor: colors.WHITE.primary,
                                color: colors.PRIMARY.primary
                              }}>
                                <Sparkles size={16} />
                              </div>
                            </div>
                            <h6 className="fw-bold mb-1" style={{ color: colors.PRIMARY.primary }}>Hydration</h6>
                            <div className="fs-5 fw-bold mb-1" style={{ color: colors.PRIMARY.primary }}>Optimal</div>
                            <div className="progress" style={{ height: '4px', borderRadius: '2px' }}>
                              <div
                                className="progress-bar"
                                style={{
                                  width: '85%',
                                  backgroundColor: colors.PRIMARY.primary,
                                  borderRadius: '2px'
                                }}
                              />
                            </div>
                            <p className="fs-8 text-muted mt-2 mb-0">Well-moisturized scalp</p>
                          </Card.Body>
                        </Card>
                      </Col>

                      <Col md={6} lg={3}>
                        <Card className="h-100 border-0" style={{
                          borderRadius: '12px',
                          backgroundColor: colors.SECONDARY.pastel,
                          border: `1px solid ${colors.SECONDARY.light}`
                        }}>
                          <Card.Body className="p-3 text-center">
                            <div className="mb-2">
                              <div className="p-2 rounded-circle d-inline-flex" style={{
                                backgroundColor: colors.WHITE.primary,
                                color: colors.SECONDARY.primary
                              }}>
                                <Award size={16} />
                              </div>
                            </div>
                            <h6 className="fw-bold mb-1" style={{ color: colors.SECONDARY.primary }}>Circulation</h6>
                            <div className="fs-5 fw-bold mb-1" style={{ color: colors.SECONDARY.primary }}>Excellent</div>
                            <div className="progress" style={{ height: '4px', borderRadius: '2px' }}>
                              <div
                                className="progress-bar"
                                style={{
                                  width: '90%',
                                  backgroundColor: colors.SECONDARY.primary,
                                  borderRadius: '2px'
                                }}
                              />
                            </div>
                            <p className="fs-8 text-muted mt-2 mb-0">Healthy blood flow</p>
                          </Card.Body>
                        </Card>
                      </Col>
                    </Row>

                    {/* Scalp Care Recommendations */}
                    <div className="p-4 rounded-3" style={{ backgroundColor: '#F8FAFC' }}>
                      <div className="d-flex align-items-start">
                        <div className="p-2 rounded-3 me-3" style={{
                          backgroundColor: colors.GREEN.primary,
                          color: colors.WHITE.primary
                        }}>
                          <TrendingUp size={20} />
                        </div>
                        <div className="flex-grow-1">
                          <h6 className="fw-bold mb-2" style={{ color: colors.GREEN.primary }}>
                            Personalized Scalp Care Plan
                          </h6>
                          <Row className="g-3">
                            <Col md={4}>
                              <div className="d-flex align-items-center mb-2">
                                <div className="p-1 rounded-circle me-2" style={{
                                  backgroundColor: colors.GREEN.pastel,
                                  color: colors.GREEN.primary
                                }}>
                                  <CheckCircle size={14} />
                                </div>
                                <span className="fs-6 fw-semibold" style={{ color: '#1F2937' }}>Daily Care</span>
                              </div>
                              <p className="fs-7 text-muted mb-0">
                                Gentle cleansing 3-4 times per week with sulfate-free shampoo
                              </p>
                            </Col>
                            <Col md={4}>
                              <div className="d-flex align-items-center mb-2">
                                <div className="p-1 rounded-circle me-2" style={{
                                  backgroundColor: colors.SECONDARY.pastel,
                                  color: colors.SECONDARY.primary
                                }}>
                                  <Sparkles size={14} />
                                </div>
                                <span className="fs-6 fw-semibold" style={{ color: '#1F2937' }}>Weekly Treatment</span>
                              </div>
                              <p className="fs-7 text-muted mb-0">
                                Scalp massage with nourishing oils to improve circulation
                              </p>
                            </Col>
                            <Col md={4}>
                              <div className="d-flex align-items-center mb-2">
                                <div className="p-1 rounded-circle me-2" style={{
                                  backgroundColor: colors.PRIMARY.pastel,
                                  color: colors.PRIMARY.primary
                                }}>
                                  <Target size={14} />
                                </div>
                                <span className="fs-6 fw-semibold" style={{ color: '#1F2937' }}>Protection</span>
                              </div>
                              <p className="fs-7 text-muted mb-0">
                                UV protection and gentle handling to maintain scalp health
                              </p>
                            </Col>
                          </Row>

                          <div className="mt-3 pt-3" style={{ borderTop: '1px solid #E5E7EB' }}>
                            <div className="d-flex flex-wrap gap-2">
                              <span className="badge rounded-pill px-3 py-1" style={{
                                backgroundColor: colors.GREEN.primary,
                                color: colors.WHITE.primary,
                                fontSize: '11px',
                                fontWeight: '500'
                              }}>
                                Balanced Oil Production
                              </span>
                              <span className="badge rounded-pill px-3 py-1" style={{
                                backgroundColor: colors.PRIMARY.primary,
                                color: colors.WHITE.primary,
                                fontSize: '11px',
                                fontWeight: '500'
                              }}>
                                Optimal Hydration
                              </span>
                              <span className="badge rounded-pill px-3 py-1" style={{
                                backgroundColor: colors.SECONDARY.primary,
                                color: colors.WHITE.primary,
                                fontSize: '11px',
                                fontWeight: '500'
                              }}>
                                Healthy Circulation
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card.Body>
                </Card>

                {/* AI Recommendations - Modern Card */}
                <Card className="border-0 shadow-sm mb-4" style={{ borderRadius: '20px' }}>
                  <Card.Body className="p-4">
                    <div className="d-flex align-items-center mb-4">
                      <div className="p-2 rounded-3 me-3" style={{ backgroundColor: colors.PRIMARY.pastel }}>
                        <Award size={24} style={{ color: colors.PRIMARY.primary }} />
                      </div>
                      <div>
                        <h2 className="fs-5 fw-bold mb-1" style={{ color: '#1F2937' }}>AI Insights</h2>
                        <p className="text-muted mb-0 fs-6">Personalized recommendations for you</p>
                      </div>
                    </div>
                    <AggregatedUserPromptReplies sessionGuid={sessionGuid} userId={String(userId)} />
                  </Card.Body>
                </Card>

                {/* Care Tips - Modern Card */}
                {!isMobile && (
                  <Card className="border-0 shadow-sm mb-4" style={{
                    borderRadius: '20px',
                    background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`
                  }}>
                    <Card.Body className="p-4">
                      <div className="d-flex align-items-center mb-3">
                        <Sparkles size={24} className="text-white me-3" />
                        <h2 className="fs-5 fw-bold text-white mb-0">
                          Improve Your Hair Score
                        </h2>
                      </div>
                      <p className="text-white opacity-90 mb-4 fs-6">
                        Explore these care categories to enhance your hair health
                      </p>
                      <Row className="g-3">
                        {tipCategories.map((tip) => (
                          <Col key={tip} xs={6} md={3}>
                            <div
                              className="bg-white bg-opacity-90 rounded-3 p-3 text-center h-100 d-flex align-items-center justify-content-center"
                              style={{
                                backdropFilter: 'blur(10px)',
                                border: '1px solid rgba(255,255,255,0.2)',
                                transition: 'all 0.2s ease'
                              }}
                            >
                              <span className="text-dark fs-6 fw-medium">{tip}</span>
                            </div>
                          </Col>
                        ))}
                      </Row>
                    </Card.Body>
                  </Card>
                )}
              </>
            )}

            {/* Action Button */}
            <div className="text-center mb-4">
              <Button
                className="px-5 py-3 border-0 shadow-sm"
                style={{
                  backgroundColor: colors.SECONDARY.primary,
                  borderRadius: '16px',
                  fontSize: '16px',
                  fontWeight: '600',
                  color: colors.WHITE.primary,
                  transition: 'all 0.2s ease'
                }}
              >
                <ChevronRight size={20} className="me-2" />
                Add Your Hair Care Products
              </Button>
            </div>
          </Container>
        </div>
      </DashboardLayout>
    </Container>
  );
};

export default Home;
