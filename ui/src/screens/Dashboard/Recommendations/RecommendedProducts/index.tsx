import React, { useEffect, useMemo, useState } from "react";
import { AxiosError } from "axios";
import api from "../../../../api";

import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "react-bootstrap";
import { ArrowClockwise } from "react-bootstrap-icons";

import ProductCard, { ProductCardData } from "../../../../components/ProductCard";
import productExtras from "../data/products.json";

export interface Product extends ProductCardData {
  hairtype: string;
}

export interface ProductExtra {
  product_details: string;
  product_link: string;
  notes: string;
}

interface Props {
  productIds: number[];
  onSelect?: (product: Product) => void;
  apiRoot?: string;
}

interface MessageOut {
  message: string;
}

// ProductImage component removed - now handled by unified ProductCard component

const RecommendedProducts: React.FC<Props> = ({ productIds, onSelect, apiRoot }) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refresh, setRefresh] = useState(0);

  const root = useMemo(() => apiRoot || api.getUri(), [apiRoot]);

  const url = useMemo(() => {
    const params = new URLSearchParams();
    productIds.forEach((id) => params.append("product_ids", id.toString()));
    return `${root}/products/recommendations?${params.toString()}`;
  }, [root, productIds]);

  useEffect(() => {
    let mounted = true;
    setLoading(true);

    api
      .get<Product[]>(url)
      .then((res) => {
        if (mounted) {
          setProducts(res.data);
          setError(null);
        }
      })
      .catch((err: AxiosError<MessageOut>) => {
        if (mounted) {
          const msg = err.response?.data?.message || err.message || "Unknown error";
          setError(msg);
        }
      })
      .finally(() => mounted && setLoading(false));

    return () => {
      mounted = false;
    };
  }, [url, refresh]);

  const productLinkMap = useMemo(() => {
    const map = new Map<string, string>();
    (productExtras as ProductExtra[]).forEach((item) => {
      if (item.product_link) {
        map.set(item.product_details.trim().toLowerCase(), item.product_link);
      }
    });
    return map;
  }, []);

  const enrichedProducts = useMemo(() => {
    return products.map((p) => {
      const overrideLink = productLinkMap.get(p.name.trim().toLowerCase());
      return { ...p, link: overrideLink || p.link };
    });
  }, [products, productLinkMap]);

  if (loading)
    return (
      <div className="d-flex justify-content-center my-5">
        <Spinner animation="border" />
      </div>
    );

  if (error)
    return (
      <Alert variant="danger" className="d-flex justify-content-between align-items-center">
        <span>{error}</span>
        <Button variant="outline-light" size="sm" onClick={() => setRefresh((r) => r + 1)}>
          <ArrowClockwise />
        </Button>
      </Alert>
    );

  if (enrichedProducts.length === 0)
    return <Alert variant="warning">No recommended products. Complete a quiz to get recommendations.</Alert>;

  return (
    <Row xs={1} sm={2} md={2} lg={2} className="g-3">
      {enrichedProducts.map((p) => (
        <Col key={p.id}>
          <ProductCard
            product={p}
            variant="detailed"
            showBuyButton={true}
            showRating={true}
            showPrice={true}
            showTags={true}
            onSelect={onSelect}
            onBuyClick={(product) => {
              if (onSelect) {
                onSelect(product);
              } else if (product.link) {
                window.open(product.link, "_blank");
              } else {
                console.error("No onSelect or link provided for product:", product);
              }
            }}
            apiRoot={root}
          />
        </Col>
      ))}
    </Row>
  );
};

export default RecommendedProducts;
