import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col } from 'react-bootstrap';
import api from '../../../../api';
import ProductCard, { ProductCardData } from '../../../../components/ProductCard';
import colors from '../../../../styles/colors';



interface Recommendation {
    user_id: number;
    session_guid: string;
    created_at: string;
    conditioners_recs: string;
    shampoos_recs: string;
}

type Product = ProductCardData;

interface UserIdProps {
    userId: number;
}

// Simple text extractor for product names
const extractProductNames = (text: string): string[] => {
    if (!text) return [];
    const parts = text.split(/[/,]/);
    return parts.map((s) => s.trim()).filter((s) => s.split(' ').length >= 2);
};

const TopRecommendations: React.FC<UserIdProps> = ({ userId }) => {
    const {
        data: recommendation,
        isPending: isRecLoading,
        error: recError,
    } = useQuery<Recommendation[], Error, Recommendation>({
        queryKey: ['recommendations', userId],
        queryFn: async () => {
            const res = await api.get<Recommendation[]>(
                `/recommendations/user/${userId}`
            );
            console.log(res.data);
            return res.data;
        },
        select: (data) => data[0],
    });


    const {
        data: products,
        isPending: isProdLoading,
        error: prodError,
    } = useQuery<Product[]>({
        queryKey: [
            'recommended-products',
            recommendation?.conditioners_recs,
            recommendation?.shampoos_recs,
        ],
        queryFn: async () => {
            if (!recommendation) return [];

            console.log(`Got some RECOMMENDATIONS: ${recommendation}`);

            const conditionerNames = extractProductNames(recommendation.conditioners_recs);
            console.log(`Products -  CONDITIONER: ${conditionerNames}`);
            const shampooNames = extractProductNames(recommendation.shampoos_recs);
            const allNames = [...conditionerNames, ...shampooNames];

            const queryParams = allNames.map((name) => `name=${encodeURIComponent(name)}`).join('&');
            console.log('🔍 Product name query:', queryParams);

            const res = await api.get<Product[]>(
                `/products/names?${queryParams}`
            );
            return res.data;
        },
        enabled: !!recommendation,
    });

    if (isRecLoading || isProdLoading) {
        console.log(`isRecLoading Status: ${isRecLoading}`);
        console.log(`isProdLoading Status: ${isProdLoading}`);
        return (
            <div className="text-center my-4 py-5">
                <div className="d-inline-flex align-items-center justify-content-center p-3 mb-3 rounded-4"
                    style={{ background: colors.PRIMARY.pastel }}>
                    <Spinner
                        animation="border"
                        style={{
                            color: colors.PRIMARY.primary,
                            width: '2rem',
                            height: '2rem'
                        }}
                    />
                </div>
                <p className="text-muted">Loading your personalized recommendations...</p>
            </div>
        );
    }

    if (recError || prodError) {
        return (
            <Alert variant="danger" className="my-4 rounded-4 border-0 shadow-sm">
                <div className="d-flex align-items-center">
                    <div className="p-2 rounded-circle me-3" style={{ background: 'rgba(220, 53, 69, 0.1)' }}>
                        <i className="bi bi-exclamation-triangle-fill text-danger"></i>
                    </div>
                    <div>
                        <h5 className="mb-1">Unable to Load Recommendations</h5>
                        <p className="mb-0">We're having trouble retrieving your product recommendations. Please try again later.</p>
                    </div>
                </div>
            </Alert>
        );
    }

    // Function to enhance products with match data and descriptions
    const enrichProductData = (product: any) => {
        // Generate match percentage based on product characteristics
        const baseMatch = 80 + Math.floor(Math.random() * 15); // 80-95% range for personal recommendations

        // Generate description based on product name and type
        const descriptions = [
            "Perfectly matched to your hair analysis results.",
            "Recommended based on your unique hair profile and goals.",
            "Expertly selected to address your specific hair needs.",
            "Tailored recommendation from your personalized hair assessment.",
            "Ideal choice based on your hair type and styling preferences."
        ];

        const randomDescription = descriptions[Math.floor(Math.random() * descriptions.length)];

        // Generate tags based on hair characteristics
        const tags = ["Personalized", "Expert Pick"];

        return {
            ...product,
            match: baseMatch,
            description: randomDescription,
            tags: tags
        };
    };

    // Image handling is now done by the ProductCard component

    return (
        <div className="mb-5">
            <h2 className="fs-4 fw-bold mb-4" style={{ color: '#1F2937' }}>
                Recommended Products for Your Hair
            </h2>
            <Row className="g-4">
                {products?.map((product) => {
                    const enrichedProduct = enrichProductData(product);
                    return (
                        <Col sm={12} md={6} lg={4} xl={3} key={product.id}> {/* Better responsive breakpoints for larger cards */}
                            <ProductCard
                                product={enrichedProduct}
                                variant="default"
                                showBuyButton={true}
                                showRating={true}
                                showPrice={true}
                                showTags={true}
                                onBuyClick={(product) => {
                                    if (product.link) {
                                        window.open(product.link, '_blank');
                                    }
                                }}
                            />
                        </Col>
                    );
                })}
                {products?.length === 0 && (
                    <Col>
                        <Alert
                            variant="info"
                            className="rounded-4 border-0 shadow-sm"
                            style={{ background: colors.BLUE.pastel, color: colors.BLUE.primary }}
                        >
                            <div className="d-flex align-items-center">
                                <div className="p-2 rounded-circle me-3" style={{ background: 'rgba(255, 255, 255, 0.5)' }}>
                                    <i className="bi bi-info-circle-fill"></i>
                                </div>
                                <div>
                                    <h5 className="mb-1">No Products Found</h5>
                                    <p className="mb-0">We couldn't find any products matching your hair profile. Please check back later.</p>
                                </div>
                            </div>
                        </Alert>
                    </Col>
                )}
            </Row>
        </div>
    );
};

export default TopRecommendations;
