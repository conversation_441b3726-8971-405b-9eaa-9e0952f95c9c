import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Con<PERSON><PERSON>, <PERSON><PERSON>, Badge } from 'react-bootstrap';
import { ShoppingCart, Star, ExternalLink } from 'lucide-react';
import api from '../../../../api';
import colors from '../../../../styles/colors';



interface Recommendation {
    user_id: number;
    session_guid: string;
    created_at: string;
    conditioners_recs: string;
    shampoos_recs: string;
}

interface Product {
    id: number;
    name: string;
    rating?: string;
    image_url?: string;
    price?: string;
}

interface UserIdProps {
    userId: number;
}

// Simple text extractor for product names
const extractProductNames = (text: string): string[] => {
    if (!text) return [];
    const parts = text.split(/[/,]/);
    return parts.map((s) => s.trim()).filter((s) => s.split(' ').length >= 2);
};

const TopRecommendations: React.FC<UserIdProps> = ({ userId }) => {
    const {
        data: recommendation,
        isPending: isRecLoading,
        error: recError,
    } = useQuery<Recommendation[], Error, Recommendation>({
        queryKey: ['recommendations', userId],
        queryFn: async () => {
            const res = await api.get<Recommendation[]>(
                `/recommendations/user/${userId}`
            );
            console.log(res.data);
            return res.data;
        },
        select: (data) => data[0],
    });


    const {
        data: products,
        isPending: isProdLoading,
        error: prodError,
    } = useQuery<Product[]>({
        queryKey: [
            'recommended-products',
            recommendation?.conditioners_recs,
            recommendation?.shampoos_recs,
        ],
        queryFn: async () => {
            if (!recommendation) return [];

            console.log(`Got some RECOMMENDATIONS: ${recommendation}`);

            const conditionerNames = extractProductNames(recommendation.conditioners_recs);
            console.log(`Products -  CONDITIONER: ${conditionerNames}`);
            const shampooNames = extractProductNames(recommendation.shampoos_recs);
            const allNames = [...conditionerNames, ...shampooNames];

            const queryParams = allNames.map((name) => `name=${encodeURIComponent(name)}`).join('&');
            console.log('🔍 Product name query:', queryParams);

            const res = await api.get<Product[]>(
                `/products/names?${queryParams}`
            );
            return res.data;
        },
        enabled: !!recommendation,
    });

    if (isRecLoading || isProdLoading) {
        console.log(`isRecLoading Status: ${isRecLoading}`);
        console.log(`isProdLoading Status: ${isProdLoading}`);
        return (
            <div className="text-center my-4 py-5">
                <div className="d-inline-flex align-items-center justify-content-center p-3 mb-3 rounded-4"
                    style={{ background: colors.PRIMARY.pastel }}>
                    <Spinner
                        animation="border"
                        style={{
                            color: colors.PRIMARY.primary,
                            width: '2rem',
                            height: '2rem'
                        }}
                    />
                </div>
                <p className="text-muted">Loading your personalized recommendations...</p>
            </div>
        );
    }

    if (recError || prodError) {
        return (
            <Alert variant="danger" className="my-4 rounded-4 border-0 shadow-sm">
                <div className="d-flex align-items-center">
                    <div className="p-2 rounded-circle me-3" style={{ background: 'rgba(220, 53, 69, 0.1)' }}>
                        <i className="bi bi-exclamation-triangle-fill text-danger"></i>
                    </div>
                    <div>
                        <h5 className="mb-1">Unable to Load Recommendations</h5>
                        <p className="mb-0">We're having trouble retrieving your product recommendations. Please try again later.</p>
                    </div>
                </div>
            </Alert>
        );
    }

    const image_base = "http://localhost:8000/static/";

    return (
        <div className="mb-5">
            <h2 className="fs-4 fw-bold mb-4" style={{ color: '#1F2937' }}>
                Recommended Products for Your Hair
            </h2>
            <Row className="g-4">
                {products?.map((product) => (
                    <Col md={6} lg={4} key={product.id}>
                        <Card className="h-100 border-0 shadow-sm product-card" style={{
                            borderRadius: '16px',
                            transition: 'all 0.3s ease',
                            overflow: 'hidden'
                        }}>
                            <div className="position-relative">
                                <div className="text-center p-4" style={{ background: '#F8FAFC' }}>
                                    {product.image_url ? (
                                        <Card.Img
                                            variant="top"
                                            src={image_base + product.image_url}
                                            alt={product.name}
                                            style={{
                                                height: '180px',
                                                objectFit: 'contain',
                                                transition: 'transform 0.3s ease'
                                            }}
                                        />
                                    ) : (
                                        <div
                                            className="d-flex align-items-center justify-content-center"
                                            style={{
                                                height: '180px',
                                                background: colors.PRIMARY.pastel,
                                                color: colors.PRIMARY.primary
                                            }}
                                        >
                                            <p className="mb-0">Image not available</p>
                                        </div>
                                    )}
                                    {product.rating && (
                                        <Badge
                                            bg="warning"
                                            text="dark"
                                            className="position-absolute top-0 end-0 m-3 px-2 py-1"
                                            style={{ borderRadius: '8px' }}
                                        >
                                            <Star size={14} className="me-1" /> {product.rating}
                                        </Badge>
                                    )}
                                </div>
                            </div>
                            <Card.Body className="p-4">
                                <Card.Title className="fs-5 fw-bold mb-2" style={{ color: '#1F2937' }}>
                                    {product.name}
                                </Card.Title>
                                <div className="d-flex justify-content-between align-items-center mt-3">
                                    {product.price && (
                                        <span className="fs-5 fw-bold" style={{ color: colors.PRIMARY.primary }}>
                                            {product.price}
                                        </span>
                                    )}
                                    <Button
                                        variant="primary"
                                        className="rounded-pill px-3 py-2 border-0"
                                        style={{
                                            background: `${colors.PRIMARY.primary}`,
                                            boxShadow: '0 4px 15px rgba(67, 0, 255, 0.2)',
                                            transition: 'all 0.2s ease'
                                        }}
                                        onMouseEnter={(e) => {
                                            e.currentTarget.style.transform = 'translateY(-2px)';
                                            e.currentTarget.style.boxShadow = '0 8px 20px rgba(67, 0, 255, 0.3)';
                                        }}
                                        onMouseLeave={(e) => {
                                            e.currentTarget.style.transform = 'translateY(0)';
                                            e.currentTarget.style.boxShadow = '0 4px 15px rgba(67, 0, 255, 0.2)';
                                        }}
                                    >
                                        <ShoppingCart size={16} className="me-2" /> Buy Now
                                    </Button>
                                </div>
                            </Card.Body>
                        </Card>
                    </Col>
                ))}
                {products?.length === 0 && (
                    <Col>
                        <Alert
                            variant="info"
                            className="rounded-4 border-0 shadow-sm"
                            style={{ background: colors.BLUE.pastel, color: colors.BLUE.primary }}
                        >
                            <div className="d-flex align-items-center">
                                <div className="p-2 rounded-circle me-3" style={{ background: 'rgba(255, 255, 255, 0.5)' }}>
                                    <i className="bi bi-info-circle-fill"></i>
                                </div>
                                <div>
                                    <h5 className="mb-1">No Products Found</h5>
                                    <p className="mb-0">We couldn't find any products matching your hair profile. Please check back later.</p>
                                </div>
                            </div>
                        </Alert>
                    </Col>
                )}
            </Row>
        </div>
    );
};

export default TopRecommendations;
