import React from 'react';
import styled from 'styled-components';
import { ShoppingBag, Star } from 'lucide-react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import DashboardLayout from '../../../layouts/DashboardLayout';
import colors from '../../../styles/colors';
import TopRecommendations from './PersonalRecommendations';
import { useLocalUser } from '../../../hooks/api/useLocalUser';
import { useUserSessionGuid } from '../../../hooks/api/useUserSessionGuid';
import { useQuestionnaireStatus } from '../../../hooks/api/useQuestionnaireStatus';
import QuestionnairePaywall from '../../../components/QuestionnairePaywall';

const Root = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
  box-sizing: border-box;
  padding: 3vw 5vw 3vw 5vw;
  color: ${colors.PRIMARY.dark};
  min-height: 100%;
  width: 100%;
  background: linear-gradient(135deg, ${colors.BLUE.pastel} 0%, #f7faff 100%);
  gap: 2.5vw;

  h1 {
    font-size: 2.1em;
    font-weight: 600;
    margin: 0;
    padding: 0;
    color: ${colors.BLUE.primary};
  }

  p {
    font-size: 1em;
    margin: 0;
    padding: 0;
    color: ${colors.SECONDARY.dark};
  }
  h2 {
    font-size: 1.1em;
    font-weight: 600;
    color: ${colors.BLUE.primary};
    margin-bottom: 4px;
  }
    box-shadow: 0 2px 8px rgba(44, 130, 201, 0.08);
    transition: background 0.2s;
    border: none;
    cursor: pointer;
    color: #fff;
    background-color: ${colors.BLUE.primary};
    &:hover {
      background-color: ${colors.BLUE.primary};
    }
  }

  header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2vw;
  }

  @media only screen and (max-width: 768px) {
    h1 {
      font-size: 1.5em;
    }
    p {
      font-size: 0.9em;
    }
    button {
      width: 100%;
    }
    header {
      flex-direction: column;
      gap: 2vw;
    }
  }
`;

const CardContainer = styled.div`
  display: flex;
  flex-direction: column;
  padding: 24px 28px;
  border-radius: 14px;
  width: 100%;
  box-sizing: border-box;
  background: ${colors.BLUE.pastel};
  box-shadow: 0 2px 12px rgba(44, 130, 201, 0.07);
  gap: 18px;
`;

const CardSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;

  h2 {
    font-size: 1.1em;
    font-weight: 600;
    color: ${colors.BLUE.primary};
    margin-bottom: 4px;
  }
  p {
    font-size: 0.95em;
    margin: 0 0 8px 0;
    color: ${colors.SECONDARY.dark};
  }
`;

const Column = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Recommendations = () => {
  // Get user data from local storage and hooks
  const {
    data: user,
    isLoading: userLoading,
    isError: userError,
  } = useLocalUser();

  const userId = user?.id;

  // Only fetch session data if we have a valid user ID
  const {
    data: sessionData,
    isLoading: sessionLoading,
    isError: sessionError,
  } = useUserSessionGuid(userId && userId > 0 ? userId : undefined);

  // Check questionnaire completion status
  const { data: questionnaireStatus, isLoading: statusLoading } = useQuestionnaireStatus(userId);

  // Determine if user has completed quiz based on session data
  // If user has session GUIDs, they have quiz data and should see metrics
  const hasSessionData = sessionData && sessionData.length > 0;

  // Show paywall only if user has no session data AND hasn't completed questionnaire
  // Also consider loading states - don't show paywall while still loading
  const shouldShowPaywall = !statusLoading && !hasSessionData && questionnaireStatus?.status !== 'Completed';

  // Handle navigation to questionnaire
  const handleStartQuestionnaire = () => {
    if (questionnaireStatus?.status === 'Started') {
      window.location.href = '/questionaire/questions';
    } else {
      window.location.href = '/quiz';
    }
  };

  if (userLoading || sessionLoading || statusLoading) return <Skeleton height={200} />;
  if (userError || sessionError) return <p>Error loading user/session data.</p>;

  return (
    <DashboardLayout>
      {/* Questionnaire Paywall Overlay */}
      {shouldShowPaywall && (
        <QuestionnairePaywall
          onStartQuestionnaire={handleStartQuestionnaire}
          completedQuestions={questionnaireStatus?.completed_questions}
          totalQuestions={questionnaireStatus?.total_questions}
          status={questionnaireStatus?.status}
        />
      )}

      {/* Main Content with conditional blur */}
      <Root style={{
        filter: shouldShowPaywall ? 'blur(4px)' : 'none',
        pointerEvents: shouldShowPaywall ? 'none' : 'auto',
        transition: 'filter 0.3s ease'
      }}>
        <header>
          <Column>
            <h1>Your recommended products</h1>
            <p>
              These products are recommended based on your hair diagnostic. <a href="#" style={{ color: colors.BLUE.primary, textDecoration: 'underline' }}>Learn more</a>.
            </p>
          </Column>
          <button aria-label="Purchase all recommended products">
            <ShoppingBag size={20} /> Purchase all
          </button>
        </header>

        {/* <RecommendedProducts productIds={productIds} /> */}

        <TopRecommendations userId={userId || 0} />

        <CardContainer>
          <CardSection>
            <h2>Why these products?</h2>
            <p>
              The products chosen have been analyzed and found to contain the best formulations to meet your stated hair goals and address any issues identified.
            </p>
          </CardSection>
          <CardSection>
            <h2>Expertise & Science</h2>
            <p>
              Our expert-led diagnostic evaluation process ensures your recommended products complement your Hair ID profile.
            </p>
          </CardSection>
          <CardSection>
            <h2>Checking for changes</h2>
            <p>
              Seasonal changes, new locations, hormonal shifts, stress, and diet can affect your hair. We recommend scheduling 'Change check-in' updates every 2-3 months, as new changes may warrant updated recommendations.
            </p>
          </CardSection>
          <span style={{ fontSize: '0.8em', fontWeight: 400, margin: '16px 0 0 0', color: colors.SECONDARY.primary }}>
            <strong>Disclaimer:</strong> Cosmetrics Ai is not affiliated with any cosmetics brand or company and does not receive any form of endorsement to recommend hair care products. We are proudly Independent & Impartial forever!
          </span>
        </CardContainer>

        <button style={{ width: 250 }} aria-label="Rate your recommendation">
          <Star size={20} /> Rate Recommendation
        </button>
      </Root>
    </DashboardLayout>
  );
};

export default Recommendations;
