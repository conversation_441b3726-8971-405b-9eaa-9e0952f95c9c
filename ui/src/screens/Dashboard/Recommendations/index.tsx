import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { ShoppingBag, Star, Award, Target, CheckCircle, Sparkles } from 'lucide-react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import DashboardLayout from '../../../layouts/DashboardLayout';
import colors from '../../../styles/colors';
import TopRecommendations from './PersonalRecommendations';
import { useLocalUser } from '../../../hooks/api/useLocalUser';
import { useUserSessionGuid } from '../../../hooks/api/useUserSessionGuid';
import { useQuestionnaireStatus } from '../../../hooks/api/useQuestionnaireStatus';
import QuestionnairePaywall from '../../../components/QuestionnairePaywall';

const pageStyle: React.CSSProperties = {
  backgroundColor: "#FAFBFC",
  minHeight: "100vh",
  color: "#1F2937",
};

const Recommendations = () => {
  // Get user data from local storage and hooks
  const {
    data: user,
    isLoading: userLoading,
    isError: userError,
  } = useLocalUser();

  const userId = user?.id;

  // Only fetch session data if we have a valid user ID
  const {
    data: sessionData,
    isLoading: sessionLoading,
    isError: sessionError,
  } = useUserSessionGuid(userId && userId > 0 ? userId : undefined);

  // Check questionnaire completion status
  const { data: questionnaireStatus, isLoading: statusLoading } = useQuestionnaireStatus(userId);

  // Determine if user has completed quiz based on session data
  // If user has session GUIDs, they have quiz data and should see metrics
  const hasSessionData = sessionData && sessionData.length > 0;

  // Show paywall only if user has no session data AND hasn't completed questionnaire
  // Also consider loading states - don't show paywall while still loading
  const shouldShowPaywall = !statusLoading && !hasSessionData && questionnaireStatus?.status !== 'Completed';

  // Handle navigation to questionnaire
  const handleStartQuestionnaire = () => {
    if (questionnaireStatus?.status === 'Started') {
      window.location.href = '/questionaire/questions';
    } else {
      window.location.href = '/quiz';
    }
  };

  if (userLoading || sessionLoading || statusLoading) return <Skeleton height={200} />;
  if (userError || sessionError) return <p>Error loading user/session data.</p>;

  return (
    <Container fluid style={pageStyle}>
      <DashboardLayout>
        {/* Questionnaire Paywall Overlay */}
        {shouldShowPaywall && (
          <QuestionnairePaywall
            onStartQuestionnaire={handleStartQuestionnaire}
            completedQuestions={questionnaireStatus?.completed_questions}
            totalQuestions={questionnaireStatus?.total_questions}
            status={questionnaireStatus?.status}
          />
        )}

        {/* Main Content with conditional blur */}
        <div style={{
          filter: shouldShowPaywall ? 'blur(4px)' : 'none',
          pointerEvents: shouldShowPaywall ? 'none' : 'auto',
          transition: 'filter 0.3s ease'
        }}>
          {/* Modern Welcome Header */}
          <div className="position-relative overflow-hidden" style={{
            background: '#FFFFFF',
            borderRadius: '0 0 40px 40px',
            marginBottom: '2rem',
            border: '1px solid #E2E8F0'
          }}>
            {/* Animated Background Elements */}
            <div style={{
              position: 'absolute',
              top: '-50%',
              right: '-10%',
              width: '300px',
              height: '300px',
              background: `${colors.PRIMARY.pastel}40`,
              borderRadius: '50%',
              filter: 'blur(60px)'
            }} />
            <div style={{
              position: 'absolute',
              bottom: '-30%',
              left: '-5%',
              width: '200px',
              height: '200px',
              background: `${colors.SECONDARY.pastel}60`,
              borderRadius: '50%',
              filter: 'blur(40px)'
            }} />

            <Container className="px-4 py-5 position-relative">
              <Row className="align-items-center">
                <Col lg={8}>
                  <div className="d-flex align-items-center mb-4">
                    <div className="p-3 rounded-4 me-4" style={{
                      backgroundColor: colors.PRIMARY.pastel,
                      border: `1px solid ${colors.PRIMARY.primary}20`
                    }}>
                      <Award size={32} style={{ color: colors.PRIMARY.primary }} />
                    </div>
                    <div>
                      <h1 className="display-5 fw-bold mb-1" style={{ color: '#1F2937' }}>
                        Your Recommended Products
                      </h1>
                      <p className="fs-6 mb-0" style={{ color: '#6B7280' }}>
                        Personalized recommendations based on your hair diagnostic analysis
                      </p>
                    </div>
                  </div>
                </Col>
                <Col lg={4} className="text-lg-end">
                  <Button
                    size="lg"
                    className="px-4 py-3 border-0 shadow-sm"
                    style={{
                      background: `${colors.PRIMARY.primary}`,
                      borderRadius: '16px',
                      fontSize: '16px',
                      fontWeight: '600',
                      color: 'white',
                      border: 'none',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 8px 25px rgba(67, 0, 255, 0.3)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    <ShoppingBag size={20} className="me-2" />
                    Purchase All
                  </Button>
                </Col>
              </Row>
            </Container>
          </div>

          <Container className="px-4">
            {/* Product Recommendations */}
            <TopRecommendations userId={userId || 0} />

            {/* Information Cards */}
            <Row className="g-4 mb-4">
              <Col md={4}>
                <Card className="h-100 border-0 shadow-sm" style={{ borderRadius: '16px' }}>
                  <Card.Body className="p-4">
                    <div className="d-flex align-items-center mb-3">
                      <div className="p-2 rounded-3 me-3" style={{
                        backgroundColor: colors.PRIMARY.pastel,
                        color: colors.PRIMARY.primary
                      }}>
                        <Target size={20} />
                      </div>
                      <h5 className="fw-bold mb-0" style={{ color: '#1F2937' }}>Why these products?</h5>
                    </div>
                    <p className="text-muted mb-0">
                      The products chosen have been analyzed and found to contain the best formulations to meet your stated hair goals and address any issues identified.
                    </p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={4}>
                <Card className="h-100 border-0 shadow-sm" style={{ borderRadius: '16px' }}>
                  <Card.Body className="p-4">
                    <div className="d-flex align-items-center mb-3">
                      <div className="p-2 rounded-3 me-3" style={{
                        backgroundColor: colors.SECONDARY.pastel,
                        color: colors.SECONDARY.primary
                      }}>
                        <Award size={20} />
                      </div>
                      <h5 className="fw-bold mb-0" style={{ color: '#1F2937' }}>Expertise & Science</h5>
                    </div>
                    <p className="text-muted mb-0">
                      Our expert-led diagnostic evaluation process ensures your recommended products complement your Hair ID profile.
                    </p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={4}>
                <Card className="h-100 border-0 shadow-sm" style={{ borderRadius: '16px' }}>
                  <Card.Body className="p-4">
                    <div className="d-flex align-items-center mb-3">
                      <div className="p-2 rounded-3 me-3" style={{
                        backgroundColor: colors.GREEN.pastel,
                        color: colors.GREEN.primary
                      }}>
                        <CheckCircle size={20} />
                      </div>
                      <h5 className="fw-bold mb-0" style={{ color: '#1F2937' }}>Checking for changes</h5>
                    </div>
                    <p className="text-muted mb-0">
                      Seasonal changes, new locations, hormonal shifts, stress, and diet can affect your hair. We recommend scheduling 'Change check-in' updates every 2-3 months.
                    </p>
                  </Card.Body>
                </Card>
              </Col>
            </Row>

            {/* Disclaimer Card */}
            <Card className="border-0 shadow-sm mb-4" style={{
              borderRadius: '16px',
              background: `linear-gradient(135deg, ${colors.BLUE.pastel} 0%, #f7faff 100%)`
            }}>
              <Card.Body className="p-4">
                <div className="d-flex align-items-start">
                  <div className="p-2 rounded-3 me-3" style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    color: colors.BLUE.primary
                  }}>
                    <Sparkles size={20} />
                  </div>
                  <div>
                    <h6 className="fw-bold mb-2" style={{ color: colors.PRIMARY.primary }}>Independent & Impartial</h6>
                    <p className="text-muted mb-0 small">
                      <strong>Disclaimer:</strong> Cosmetrics AI is not affiliated with any cosmetics brand or company and does not receive any form of endorsement to recommend hair care products. We are proudly Independent & Impartial forever!
                    </p>
                  </div>
                </div>
              </Card.Body>
            </Card>

            {/* Rate Recommendation Button */}
            <div className="text-center mb-4">
              <Button
                size="lg"
                className="px-5 py-3 border-0 shadow-sm"
                style={{
                  background: `${colors.PRIMARY.primary}`,
                  borderRadius: '16px',
                  fontSize: '16px',
                  fontWeight: '600',
                  color: 'white',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(67, 0, 255, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                <Star size={20} className="me-2" />
                Rate Recommendation
              </Button>
            </div>
          </Container>

          {/* Bottom Padding */}
          <div style={{ paddingBottom: '4rem' }}></div>
        </div>
      </DashboardLayout>
    </Container>
  );
};

export default Recommendations;
