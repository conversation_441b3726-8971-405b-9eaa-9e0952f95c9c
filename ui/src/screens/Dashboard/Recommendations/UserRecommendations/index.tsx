import React, { useEffect, useState } from "react";
import axios, { AxiosError } from "axios";
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "react-bootstrap";
import { ArrowClockwise } from "react-bootstrap-icons";
import ProductCard, { ProductCardData } from "../../../../components/ProductCard";

/* ------------------------------------------------------------------
   Types — adapt to RecommendationOut model
------------------------------------------------------------------ */

export interface RecommendationOut {
  id: number;
  user_id: number;
  product_name: string;
  subtitle?: string;
  image_url?: string;
  created_at: string;
  details?: string;
}

interface MessageOut {
  message: string;
}

interface Props {
  userId: number | string;
  /** Click handler when a product card is clicked */
  onSelect?: (rec: RecommendationOut) => void;
  apiRoot?: string;
}

/* ------------------------------------------------------------------
   Helper Functions
------------------------------------------------------------------ */

// Convert RecommendationOut to ProductCardData format
const mapRecommendationToProduct = (rec: RecommendationOut): ProductCardData => ({
  id: rec.id,
  name: rec.product_name,
  image_url: rec.image_url || null,
  // Map other fields as needed
  price: undefined, // RecommendationOut doesn't have price
  rating: undefined, // RecommendationOut doesn't have rating
  link: undefined, // RecommendationOut doesn't have link
  category: rec.subtitle || undefined,
});

/* ------------------------------------------------------------------
   Component
------------------------------------------------------------------ */

const UserRecommendations: React.FC<Props> = ({ userId, onSelect, apiRoot }) => {
  const [recs, setRecs] = useState<RecommendationOut[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [refresh, setRefresh] = useState(0);

  const root = apiRoot || process.env.REACT_APP_BACKEND_API || "http://localhost:8000/api";
  const url = `${root}/recommendations/user/${userId}`;



  useEffect(() => {
    let mounted = true;
    setLoading(true);
    axios
      .get<RecommendationOut[]>(url)
      .then((res) => {
        if (!mounted) return;
        setRecs(res.data);
        setError(null);
      })
      .catch((err: AxiosError<MessageOut>) => {
        if (!mounted) return;
        const msg = err.response?.data?.message || err.message || "Unknown error";
        setError(msg);
      })
      .finally(() => mounted && setLoading(false));
    return () => {
      mounted = false;
    };
  }, [url, refresh]);

  // ------------------------------------------------------------- states
  if (loading)
    return (
      <div className="d-flex justify-content-center my-5">
        <Spinner animation="border" />
      </div>
    );

  if (error)
    return (
      <Alert variant="danger" className="d-flex justify-content-between align-items-center">
        <span>{error}</span>
        <Button variant="outline-light" size="sm" onClick={() => setRefresh((r) => r + 1)}>
          <ArrowClockwise />
        </Button>
      </Alert>
    );

  if (recs.length === 0)
    return <Alert variant="warning">No recommendations found.</Alert>;

  // ------------------------------------------------------------- render grid
  return (
    <Row xs={1} sm={2} md={3} lg={4} className="g-3">
      {recs.map((rec) => (
        <Col key={rec.id}>
          <ProductCard
            product={mapRecommendationToProduct(rec)}
            variant="compact"
            showBuyButton={false}
            showRating={false}
            showPrice={false}
            showTags={true}
            onSelect={() => onSelect?.(rec)}
            apiRoot={root}
            className="recommendation-card"
          />
          {/* Additional recommendation info */}
          <div className="mt-2 text-center">
            <small className="text-muted">
              Added {new Date(rec.created_at).toLocaleDateString()}
            </small>
            {rec.details && (
              <div className="small text-muted mt-1" style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden'
              }}>
                {rec.details}
              </div>
            )}
          </div>
        </Col>
      ))}
    </Row>
  );
};

export default UserRecommendations;
