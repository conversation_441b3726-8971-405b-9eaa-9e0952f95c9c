import { useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Con<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "react-bootstrap";
import { AxiosResponse } from "axios";
import { <PERSON><PERSON>, Ch<PERSON>ronDown, Ch<PERSON>ronUp, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Triangle } from "lucide-react";

import api from "../../../api";
import { DataItem, SessionApiResponse } from "../../../constants";
import { questionLabels } from "../../../constants";
import { ScoreItem, getHighestScoreItem } from "../../../utils/dashboard";
import SessionReplies from "../../../components/SessionReplies";
import { getHairIdValue } from "../../../components/SessionReplies";
import {
    generateLLMPrompt,
    validatePrompt,
    generateProviderOptimizedPrompt,
    PromptGenerationOptions
} from "../../../utils/promptGeneration";

const exceptionsList = [8, 37];

interface ScoreResponse {
    [key: string]: ScoreItem;
}

type SessionProps = {
    sessionGuid: string;
    userId: string;
};

const fetchResponses = async (sessionGuid: string): Promise<SessionApiResponse> => {
    const res: AxiosResponse<SessionApiResponse> = await api.get<SessionApiResponse>(`/quiz/replies/session/${sessionGuid}/prompt`);
    return res.data;
};

const fetchScores = async (userId: string, sessionGuid: string): Promise<ScoreResponse> => {
    const res = await api.get<ScoreResponse>(`/users/id/${userId}/session/${sessionGuid}/scores`);
    return res.data;
};

export const AggregatedUserPromptReplies: React.FC<SessionProps> = ({ sessionGuid, userId }) => {
    const [showAdvanced, setShowAdvanced] = useState(false);
    const [promptStyle, setPromptStyle] = useState<'detailed' | 'concise' | 'clinical'>('detailed');
    const [copiedToClipboard, setCopiedToClipboard] = useState(false);

    const { data, isLoading, isError, error } = useQuery<SessionApiResponse, Error>({
        queryKey: ['userRepliesData', sessionGuid],
        queryFn: () => fetchResponses(sessionGuid),
        enabled: !!sessionGuid,
        staleTime: 1000 * 60 * 5,
    });

    const { data: scoresData, isLoading: scoresLoading, isError: scoresError } = useQuery({
        queryKey: ['topIssueScores', userId, sessionGuid],
        queryFn: () => fetchScores(userId, sessionGuid),
        enabled: !!userId && !!sessionGuid,
        staleTime: 5 * 60 * 1000, // 5 min
    });

    if (isLoading || scoresLoading) {
        return <div className="text-center p-4"><Spinner animation="border" /></div>;
    }

    if (isError) {
        return <Alert variant="danger">{error?.message}</Alert>;
    }

    if (scoresError) {
        return <Alert variant="warning">Unable to load priority data, showing basic prompt only.</Alert>;
    }

    if (!data) return null;

    const replies: DataItem[] = data.data;

    // Get top priority issue from scores data
    let topIssueItem: ScoreItem | null = null;
    if (scoresData) {
        const [, issueItem] = getHighestScoreItem(scoresData);
        topIssueItem = issueItem;
    }

    // Get porosity information
    let porosityValue: string | null = null;
    const porosityReplyData = replies.find((reply) => reply.question_id === 37);
    if (porosityReplyData && data) {
        const hairIdVal = getHairIdValue("Hair Porosity", data);
        console.log(`Porosity HairIdValue: ${hairIdVal}`);
        porosityValue = hairIdVal;
    }

    // Generate the main LLM prompt using utility functions
    const promptOptions: PromptGenerationOptions = {
        promptStyle,
        targetAudience: 'general',
        includeInstructions: true
    };

    const llmPrompt = generateLLMPrompt(
        replies,
        questionLabels,
        topIssueItem,
        porosityValue,
        promptOptions
    );

    // Validate the generated prompt
    const validation = validatePrompt(llmPrompt);

    // Handle copy to clipboard
    const handleCopyPrompt = async () => {
        try {
            await navigator.clipboard.writeText(llmPrompt);
            setCopiedToClipboard(true);
            setTimeout(() => setCopiedToClipboard(false), 2000);
        } catch (err) {
            console.error('Failed to copy prompt:', err);
        }
    };

    return (
        <Container className="mt-4">
            <Card className="shadow-sm">
                <Card.Body>
                    <div className="d-flex justify-content-between align-items-center mb-3">
                        <Card.Title className="fs-5 fw-bold text-primary mb-0">
                            AI-Generated Prompt
                        </Card.Title>
                        <div className="d-flex align-items-center gap-2">
                            {validation.isValid ? (
                                <Badge bg="success" className="d-flex align-items-center gap-1">
                                    <CheckCircle size={14} />
                                    Valid
                                </Badge>
                            ) : (
                                <Badge bg="warning" className="d-flex align-items-center gap-1">
                                    <AlertTriangle size={14} />
                                    {validation.issues.length} Issues
                                </Badge>
                            )}
                            <Button
                                variant="link"
                                size="sm"
                                onClick={() => setShowAdvanced(!showAdvanced)}
                                className="p-0 text-decoration-none"
                            >
                                {showAdvanced ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                            </Button>
                        </div>
                    </div>

                    {/* Advanced Controls */}
                    <Collapse in={showAdvanced}>
                        <div className="mb-3 p-3 bg-light rounded">
                            <div className="row g-3">
                                <div className="col-md-6">
                                    <label className="form-label fw-bold">Prompt Style</label>
                                    <select
                                        className="form-select form-select-sm"
                                        value={promptStyle}
                                        onChange={(e) => setPromptStyle(e.target.value as any)}
                                    >
                                        <option value="detailed">Detailed</option>
                                        <option value="concise">Concise</option>
                                        <option value="clinical">Clinical</option>
                                    </select>
                                </div>
                                <div className="col-md-6">
                                    <label className="form-label fw-bold">Validation Status</label>
                                    <div className="small">
                                        {validation.isValid ? (
                                            <span className="text-success">✓ Prompt is grammatically correct</span>
                                        ) : (
                                            <div>
                                                <div className="text-warning">⚠ Issues found:</div>
                                                <ul className="mb-0 ps-3">
                                                    {validation.issues.map((issue, idx) => (
                                                        <li key={idx} className="text-muted">{issue}</li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Collapse>

                    {/* Main Prompt Display */}
                    <Card.Text
                        className="mb-3 p-3 border rounded"
                        style={{
                            lineHeight: '1.6',
                            fontSize: '0.95rem',
                            color: '#374151',
                            backgroundColor: '#F9FAFB',
                            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, monospace'
                        }}
                    >
                        {llmPrompt}
                    </Card.Text>

                    {/* Action Buttons */}
                    <div className="d-flex gap-2 flex-wrap">
                        <Button variant="primary" className="fw-bold">
                            Generate Recommendations
                        </Button>
                        <Button
                            variant={copiedToClipboard ? "success" : "outline-secondary"}
                            size="sm"
                            onClick={handleCopyPrompt}
                            className="d-flex align-items-center gap-1"
                        >
                            {copiedToClipboard ? (
                                <>
                                    <CheckCircle size={14} />
                                    Copied!
                                </>
                            ) : (
                                <>
                                    <Copy size={14} />
                                    Copy Prompt
                                </>
                            )}
                        </Button>
                        <Button
                            variant="outline-info"
                            size="sm"
                            onClick={() => {
                                const optimized = generateProviderOptimizedPrompt(llmPrompt, 'openai');
                                navigator.clipboard.writeText(optimized);
                                setCopiedToClipboard(true);
                                setTimeout(() => setCopiedToClipboard(false), 2000);
                            }}
                        >
                            Copy for OpenAI
                        </Button>
                    </div>

                    {/* Character Count */}
                    <div className="mt-2 text-muted small">
                        Character count: {llmPrompt.length} | Word count: {llmPrompt.split(' ').length}
                    </div>
                </Card.Body>
            </Card>
        </Container>
    );
};

export default AggregatedUserPromptReplies;
