import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Con<PERSON>er, Card, But<PERSON>, Table } from 'react-bootstrap';
import { getABTestResults, clearABTestData } from '../hooks/useABTest';
import { BarChart3, TrendingUp, <PERSON>, MousePointer, Target, RefreshCw } from 'lucide-react';
import Layout from './Layout';

const DashboardContainer = styled.div`
  background: #F8FAFC;
  min-height: 100vh;
  padding: 2rem 0;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const StatCard = styled(Card)`
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
  
  .card-body {
    padding: 1.5rem;
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
  }
  
  .stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.5rem;
  }
  
  .stat-label {
    color: #6B7280;
    font-size: 0.875rem;
    font-weight: 500;
  }
  
  .stat-change {
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.5rem;
  }
  
  .positive {
    color: #10B981;
  }
  
  .negative {
    color: #EF4444;
  }
`;

const VariantComparison = styled(Card)`
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
  
  .variant-header {
    background: #F8FAFC;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #E5E7EB;
    font-weight: 600;
    color: #1F2937;
  }
`;

const ABTestDashboard: React.FC = () => {
  const [results, setResults] = useState<any>({});
  const [lastUpdated, setLastUpdated] = useState<string>('');

  const loadResults = () => {
    const testResults = getABTestResults('landing_page_redesign');
    setResults(testResults);
    setLastUpdated(new Date().toLocaleString());
  };

  useEffect(() => {
    loadResults();
  }, []);

  const handleClearData = () => {
    if (window.confirm('Are you sure you want to clear all A/B test data? This cannot be undone.')) {
      clearABTestData('landing_page_redesign');
      loadResults();
    }
  };

  const variantA = results.A || { views: 0, clicks: 0, conversions: 0, clickRate: '0.00', conversionRate: '0.00' };
  const variantB = results.B || { views: 0, clicks: 0, conversions: 0, clickRate: '0.00', conversionRate: '0.00' };

  const totalViews = variantA.views + variantB.views;
  const totalClicks = variantA.clicks + variantB.clicks;
  const totalConversions = variantA.conversions + variantB.conversions;

  const getWinner = () => {
    if (variantA.views === 0 && variantB.views === 0) return 'No data';
    if (parseFloat(variantA.conversionRate) > parseFloat(variantB.conversionRate)) return 'Variant A';
    if (parseFloat(variantB.conversionRate) > parseFloat(variantA.conversionRate)) return 'Variant B';
    return 'Tie';
  };

  return (
    <Layout>
      <DashboardContainer>
        <Container>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h1 className="h2 mb-2">A/B Test Dashboard</h1>
              <p className="text-muted">Landing Page Redesign Test Results</p>
              {lastUpdated && (
                <small className="text-muted">Last updated: {lastUpdated}</small>
              )}
            </div>
            <div>
              <Button variant="outline-secondary" onClick={loadResults} className="me-2">
                <RefreshCw size={16} className="me-1" />
                Refresh
              </Button>
              <Button variant="outline-danger" onClick={handleClearData}>
                Clear Data
              </Button>
            </div>
          </div>

          {/* Overall Stats */}
          <StatsGrid>
            <StatCard>
              <Card.Body>
                <div className="stat-icon" style={{ background: '#EBF8FF', color: '#3B82F6' }}>
                  <Users size={24} />
                </div>
                <div className="stat-value">{totalViews.toLocaleString()}</div>
                <div className="stat-label">Total Views</div>
              </Card.Body>
            </StatCard>

            <StatCard>
              <Card.Body>
                <div className="stat-icon" style={{ background: '#F0FDF4', color: '#10B981' }}>
                  <MousePointer size={24} />
                </div>
                <div className="stat-value">{totalClicks.toLocaleString()}</div>
                <div className="stat-label">Total Clicks</div>
                <div className="stat-change positive">
                  {totalViews > 0 ? ((totalClicks / totalViews) * 100).toFixed(2) : '0.00'}% CTR
                </div>
              </Card.Body>
            </StatCard>

            <StatCard>
              <Card.Body>
                <div className="stat-icon" style={{ background: '#FEF3C7', color: '#F59E0B' }}>
                  <Target size={24} />
                </div>
                <div className="stat-value">{totalConversions.toLocaleString()}</div>
                <div className="stat-label">Total Conversions</div>
                <div className="stat-change positive">
                  {totalViews > 0 ? ((totalConversions / totalViews) * 100).toFixed(2) : '0.00'}% Rate
                </div>
              </Card.Body>
            </StatCard>

            <StatCard>
              <Card.Body>
                <div className="stat-icon" style={{ background: '#F3E8FF', color: '#8B5CF6' }}>
                  <TrendingUp size={24} />
                </div>
                <div className="stat-value">{getWinner()}</div>
                <div className="stat-label">Current Winner</div>
                <div className="stat-change">
                  Based on conversion rate
                </div>
              </Card.Body>
            </StatCard>
          </StatsGrid>

          {/* Variant Comparison */}
          <VariantComparison>
            <div className="variant-header">
              <BarChart3 size={20} className="me-2" />
              Variant Comparison
            </div>
            <Card.Body>
              <Table responsive>
                <thead>
                  <tr>
                    <th>Variant</th>
                    <th>Description</th>
                    <th>Views</th>
                    <th>Clicks</th>
                    <th>Click Rate</th>
                    <th>Conversions</th>
                    <th>Conversion Rate</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <strong>Variant A</strong>
                    </td>
                    <td>Current Design</td>
                    <td>{variantA.views.toLocaleString()}</td>
                    <td>{variantA.clicks.toLocaleString()}</td>
                    <td>{variantA.clickRate}%</td>
                    <td>{variantA.conversions.toLocaleString()}</td>
                    <td>
                      <strong style={{ color: getWinner() === 'Variant A' ? '#10B981' : '#6B7280' }}>
                        {variantA.conversionRate}%
                      </strong>
                    </td>
                    <td>
                      {getWinner() === 'Variant A' && (
                        <span className="badge bg-success">Winner</span>
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <strong>Variant B</strong>
                    </td>
                    <td>Editorial Design</td>
                    <td>{variantB.views.toLocaleString()}</td>
                    <td>{variantB.clicks.toLocaleString()}</td>
                    <td>{variantB.clickRate}%</td>
                    <td>{variantB.conversions.toLocaleString()}</td>
                    <td>
                      <strong style={{ color: getWinner() === 'Variant B' ? '#10B981' : '#6B7280' }}>
                        {variantB.conversionRate}%
                      </strong>
                    </td>
                    <td>
                      {getWinner() === 'Variant B' && (
                        <span className="badge bg-success">Winner</span>
                      )}
                    </td>
                  </tr>
                </tbody>
              </Table>
            </Card.Body>
          </VariantComparison>

          {/* Test Configuration */}
          <Card>
            <Card.Header>
              <h5 className="mb-0">Test Configuration</h5>
            </Card.Header>
            <Card.Body>
              <div className="row">
                <div className="col-md-6">
                  <h6>Test Details</h6>
                  <ul className="list-unstyled">
                    <li><strong>Test Name:</strong> landing_page_redesign</li>
                    <li><strong>Traffic Split:</strong> 50% / 50%</li>
                    <li><strong>Status:</strong> <span className="badge bg-success">Active</span></li>
                    <li><strong>Persistence:</strong> localStorage</li>
                  </ul>
                </div>
                <div className="col-md-6">
                  <h6>Tracked Events</h6>
                  <ul className="list-unstyled">
                    <li>• Page views (variant_shown)</li>
                    <li>• CTA clicks (cta_click)</li>
                    <li>• Quiz starts (conversion)</li>
                    <li>• Secondary CTA clicks</li>
                  </ul>
                </div>
              </div>
            </Card.Body>
          </Card>

          {/* Instructions */}
          <Card className="mt-3">
            <Card.Body>
              <h6>How to Use This Dashboard</h6>
              <ol>
                <li>Monitor the conversion rates for both variants</li>
                <li>Wait for statistical significance (recommended: 100+ conversions per variant)</li>
                <li>Choose the winning variant based on conversion rate</li>
                <li>Implement the winner as the permanent landing page</li>
              </ol>
              <p className="text-muted mb-0">
                <strong>Note:</strong> This dashboard shows data stored in localStorage. 
                For production, integrate with your analytics platform (Google Analytics, Mixpanel, etc.).
              </p>
            </Card.Body>
          </Card>
        </Container>
      </DashboardContainer>
    </Layout>
  );
};

export default ABTestDashboard;
