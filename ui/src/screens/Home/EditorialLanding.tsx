import React from 'react';
import styled from 'styled-components';
import { But<PERSON> } from 'react-bootstrap';
import { 
  ArrowRight, 
  Play,
  Camera,
  Brain,
  Award,
  TrendingUp
} from 'lucide-react';
import colors from '../../styles/colors';

// Clean, editorial-style layout inspired by magazine design
const EditorialContainer = styled.div`
  background: #FAFAFA;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
`;

const HeroSection = styled.section`
  background: white;
  padding: 4rem 0;
  
  .hero-grid {
    display: grid;
    grid-template-columns: 1fr 1.2fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    
    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: 3rem;
      text-align: center;
    }
  }
  
  .hero-content {
    h1 {
      font-size: 3.5rem;
      font-weight: 800;
      color: #1F2937;
      line-height: 1.1;
      margin-bottom: 1.5rem;
      letter-spacing: -0.02em;
      
      @media (max-width: 768px) {
        font-size: 2.5rem;
      }
    }
    
    .subtitle {
      font-size: 1.25rem;
      color: #6B7280;
      line-height: 1.6;
      margin-bottom: 2rem;
      font-weight: 400;
    }
    
    .stats {
      display: flex;
      gap: 2rem;
      margin: 2rem 0;
      
      @media (max-width: 768px) {
        justify-content: center;
      }
    }
  }
  
  .hero-image {
    position: relative;
    border-radius: 24px;
    overflow: hidden;
    aspect-ratio: 4/5;
    background: linear-gradient(135deg, #F3F4F6 0%, #E5E7EB 100%);
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .overlay-card {
      position: absolute;
      top: 2rem;
      right: 2rem;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      padding: 1.5rem;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      
      .metric {
        font-size: 2rem;
        font-weight: 800;
        color: ${colors.PRIMARY.primary};
        display: block;
      }
      
      .label {
        font-size: 0.875rem;
        color: #6B7280;
        font-weight: 500;
      }
    }
  }
`;

const StatItem = styled.div`
  .number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1F2937;
    display: block;
  }
  
  .label {
    font-size: 0.875rem;
    color: #6B7280;
    font-weight: 500;
  }
`;

const CTAButton = styled(Button)`
  background: #1F2937;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.2s ease;
  
  &:hover {
    background: #111827;
    transform: translateY(-1px);
  }
`;

const SecondaryButton = styled(Button)`
  background: transparent;
  border: 2px solid #E5E7EB;
  color: #6B7280;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #1F2937;
    color: #1F2937;
    background: transparent;
  }
`;

const FeaturesSection = styled.section`
  padding: 6rem 0;
  background: white;
  
  .section-header {
    text-align: center;
    margin-bottom: 4rem;
    
    h2 {
      font-size: 2.5rem;
      font-weight: 800;
      color: #1F2937;
      margin-bottom: 1rem;
      letter-spacing: -0.02em;
    }
    
    p {
      font-size: 1.125rem;
      color: #6B7280;
      max-width: 600px;
      margin: 0 auto;
    }
  }
  
  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }
`;

const FeatureCard = styled.div`
  background: #FAFAFA;
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
  border: 1px solid #F3F4F6;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.08);
    background: white;
  }
  
  .icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: ${colors.PRIMARY.primary}15;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: ${colors.PRIMARY.primary};
  }
  
  h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.75rem;
  }
  
  p {
    color: #6B7280;
    line-height: 1.6;
    margin: 0;
  }
`;

const TestimonialSection = styled.section`
  padding: 6rem 0;
  background: #F9FAFB;
  
  .testimonial-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    align-items: center;
    
    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: 3rem;
    }
  }
  
  .testimonial-image {
    border-radius: 20px;
    overflow: hidden;
    aspect-ratio: 4/5;
    background: #E5E7EB;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .testimonial-content {
    blockquote {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1F2937;
      line-height: 1.4;
      margin-bottom: 2rem;
      font-style: italic;
    }
    
    .author {
      display: flex;
      align-items: center;
      gap: 1rem;
      
      .avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: #E5E7EB;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .info {
        .name {
          font-weight: 600;
          color: #1F2937;
          display: block;
        }
        
        .title {
          color: #6B7280;
          font-size: 0.875rem;
        }
      }
    }
  }
`;

interface EditorialLandingProps {
  onCTAClick?: () => void;
  onSecondaryClick?: () => void;
}

const EditorialLanding: React.FC<EditorialLandingProps> = ({ 
  onCTAClick, 
  onSecondaryClick 
}) => {
  const features = [
    {
      icon: Brain,
      title: "AI Analysis",
      description: "Advanced algorithms analyze your hair condition with professional-grade accuracy."
    },
    {
      icon: Camera,
      title: "Photo Assessment",
      description: "Upload 3 photos and get instant insights into your hair's health and needs."
    },
    {
      icon: Award,
      title: "Expert Curation",
      description: "Personalized product recommendations from our database of premium hair care."
    },
    {
      icon: TrendingUp,
      title: "Progress Tracking",
      description: "Monitor your hair journey with detailed reports and improvement metrics."
    }
  ];

  const handleCTAClick = () => {
    onCTAClick?.();
    window.location.href = '/quiz';
  };

  const handleSecondaryClick = () => {
    onSecondaryClick?.();
    // Could open a demo modal or navigate to demo page
  };

  return (
    <EditorialContainer>
      {/* Hero Section */}
      <HeroSection>
        <div className="hero-grid">
          <div className="hero-content">
            <h1>
              WHERE YOUR HAIR
              <br />
              HEALTH FLOURISHES
            </h1>
            <p className="subtitle">
              Discover personalized hair care through AI-powered analysis, expert recommendations, and progress tracking.
            </p>
            
            <div className="stats">
              <StatItem>
                <span className="number">24k</span>
                <span className="label">Users helped</span>
              </StatItem>
              <StatItem>
                <span className="number">95%</span>
                <span className="label">Accuracy rate</span>
              </StatItem>
              <StatItem>
                <span className="number">1000+</span>
                <span className="label">Products</span>
              </StatItem>
            </div>
            
            <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>
              <CTAButton onClick={handleCTAClick}>
                Start Analysis
                <ArrowRight size={16} className="ms-2" />
              </CTAButton>
              <SecondaryButton onClick={handleSecondaryClick}>
                <Play size={16} className="me-2" />
                Watch Demo
              </SecondaryButton>
            </div>
          </div>
          
          <div className="hero-image">
            <img 
              src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=750&fit=crop&crop=face" 
              alt="Beautiful hair"
            />
            <div className="overlay-card">
              <span className="metric">17</span>
              <span className="label">Hair metrics analyzed</span>
            </div>
          </div>
        </div>
      </HeroSection>

      {/* Features Section */}
      <FeaturesSection>
        <div className="section-header">
          <h2>How It Works</h2>
          <p>
            Our technology combines computer vision, machine learning, and expert knowledge 
            to give you personalized hair care insights.
          </p>
        </div>
        
        <div className="features-grid">
          {features.map((feature, index) => (
            <FeatureCard key={index}>
              <div className="icon">
                <feature.icon size={24} />
              </div>
              <h3>{feature.title}</h3>
              <p>{feature.description}</p>
            </FeatureCard>
          ))}
        </div>
      </FeaturesSection>

      {/* Testimonial Section */}
      <TestimonialSection>
        <div className="testimonial-grid">
          <div className="testimonial-image">
            <img 
              src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=600&h=750&fit=crop&crop=face" 
              alt="Happy customer"
            />
          </div>
          
          <div className="testimonial-content">
            <blockquote>
              "Finally found products that actually work for my hair type. The AI analysis was spot-on and the recommendations transformed my routine."
            </blockquote>
            
            <div className="author">
              <div className="avatar">
                <img 
                  src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=96&h=96&fit=crop&crop=face" 
                  alt="Sarah Chen"
                />
              </div>
              <div className="info">
                <span className="name">Sarah Chen</span>
                <span className="title">Verified User</span>
              </div>
            </div>
          </div>
        </div>
      </TestimonialSection>
    </EditorialContainer>
  );
};

export default EditorialLanding;
