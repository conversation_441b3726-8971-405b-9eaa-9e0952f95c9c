import React from 'react';
import Layout from '../Layout';
import EditorialLandingVariant from './EditorialLandingVariant';

/**
 * Preview page for the Editorial Landing Variant
 * This allows you to see the new design before approving implementation
 */
const ModernLandingPreview: React.FC = () => {
  return (
    <Layout>
      {/* Header notice for preview */}
      <div
        style={{
          background: 'linear-gradient(135deg, #1F2937 0%, #4B5563 100%)',
          color: 'white',
          padding: '1rem',
          textAlign: 'center',
          fontSize: '0.875rem',
          fontWeight: '600'
        }}
      >
        🎨 DESIGN PREVIEW: Editorial Landing Page Variant - Inspired by Clean Magazine Design
      </div>

      <EditorialLandingVariant />
    </Layout>
  );
};

export default ModernLandingPreview;
