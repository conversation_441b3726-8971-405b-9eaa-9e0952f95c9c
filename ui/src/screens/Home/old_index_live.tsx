import React from "react";
import Layout from "../../screens/Layout";
import Landing from "./Landing";
import Recommendations from "./Recommendations";
import Discover from "./Discover";
import Analyse from "./Analyse";
import Quote from "./Quote";
import Final from "./Final";

/**
 * Full-width gradient that sits behind Bootstrap’s <Container>.
 * Using inline style here keeps the gradient local to this page
 * and avoids a new styled-component declaration.
 */
const GradientWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div
    style={{
      background: "linear-gradient(to bottom, #a9afea 0%, #de90c4 100%)",
      minHeight: "100vh",
      display: "block",
      flexDirection: "column",
      width: "100%",
    }}
  >
    {children}
  </div>
);

const Home: React.FC = () => (
  <Layout>
    <GradientWrapper>
      <Landing />
      <Recommendations />
      <Discover />
      <Analyse />
      <Quote />
      <Final />
    </GradientWrapper>
  </Layout>
);

export default Home;
