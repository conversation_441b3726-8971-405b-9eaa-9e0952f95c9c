import React from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON>, Con<PERSON>er, <PERSON>, Col } from 'react-bootstrap';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Brain, 
  Award, 
  ArrowRight, 
  CheckCircle, 
  Star,
  Users,
  Zap,
  Shield,
  TrendingUp,
  Heart
} from 'lucide-react';
import colors from '../../styles/colors';

// Modern gradient background with animated elements
const HeroSection = styled.section`
  min-height: 100vh;
  background: linear-gradient(135deg, 
    ${colors.PRIMARY.primary}15 0%, 
    ${colors.SECONDARY.primary}10 50%, 
    ${colors.WHITE.primary} 100%
  );
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 100%;
    height: 200%;
    background: radial-gradient(circle, ${colors.PRIMARY.primary}08 0%, transparent 70%);
    animation: float 20s ease-in-out infinite;
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -10%;
    width: 80%;
    height: 150%;
    background: radial-gradient(circle, ${colors.SECONDARY.primary}06 0%, transparent 60%);
    animation: float 25s ease-in-out infinite reverse;
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
  }
`;

const HeroContent = styled.div`
  position: relative;
  z-index: 2;
  text-align: center;
  
  h1 {
    font-size: 4rem;
    font-weight: 800;
    background: linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    
    @media (max-width: 768px) {
      font-size: 2.5rem;
    }
  }
  
  .subtitle {
    font-size: 1.5rem;
    color: #4B5563;
    margin-bottom: 2rem;
    font-weight: 400;
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    
    @media (max-width: 768px) {
      font-size: 1.25rem;
    }
  }
  
  .stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin: 3rem 0;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1.5rem;
    }
  }
`;

const StatItem = styled.div`
  text-align: center;
  
  .number {
    font-size: 2.5rem;
    font-weight: 800;
    color: ${colors.PRIMARY.primary};
    display: block;
  }
  
  .label {
    font-size: 0.875rem;
    color: #6B7280;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
`;

const CTAButton = styled(Button)`
  background: linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%);
  border: none;
  padding: 1rem 3rem;
  font-size: 1.125rem;
  font-weight: 600;
  border-radius: 50px;
  box-shadow: 0 10px 30px rgba(67, 0, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(67, 0, 255, 0.4);
    
    &::before {
      left: 100%;
    }
  }
  
  &:active {
    transform: translateY(-1px);
  }
`;

const FeatureSection = styled.section`
  padding: 6rem 0;
  background: ${colors.WHITE.primary};
`;

const FeatureGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
`;

const FeatureCard = styled.div`
  background: ${colors.WHITE.primary};
  border-radius: 24px;
  padding: 2.5rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #F3F4F6;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
    border-color: ${colors.PRIMARY.primary}30;
  }
  
  .icon-wrapper {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    background: linear-gradient(135deg, ${colors.PRIMARY.primary}15 0%, ${colors.SECONDARY.primary}15 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    border: 2px solid ${colors.PRIMARY.primary}20;
  }
  
  h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 1rem;
  }
  
  p {
    color: #6B7280;
    line-height: 1.6;
    margin: 0;
  }
`;

const SectionTitle = styled.div`
  text-align: center;
  margin-bottom: 4rem;
  
  h2 {
    font-size: 3rem;
    font-weight: 800;
    color: #1F2937;
    margin-bottom: 1rem;
    
    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
  
  p {
    font-size: 1.25rem;
    color: #6B7280;
    max-width: 600px;
    margin: 0 auto;
  }
`;

const TrustSection = styled.section`
  padding: 4rem 0;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  
  .trust-indicators {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4rem;
    flex-wrap: wrap;
    margin-top: 3rem;
    
    @media (max-width: 768px) {
      gap: 2rem;
    }
  }
`;

const TrustItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #374151;
  font-weight: 600;
  
  .icon {
    color: ${colors.GREEN.primary};
  }
`;

const ModernLandingVariant: React.FC = () => {
  const features = [
    {
      icon: Brain,
      title: "AI-Powered Analysis",
      description: "Advanced machine learning algorithms analyze your hair photos to provide precise condition assessment and personalized recommendations."
    },
    {
      icon: Camera,
      title: "Photo Diagnostic",
      description: "Simply upload 3 clear photos of your hair. Our technology examines texture, damage, dryness, and scalp health in seconds."
    },
    {
      icon: Award,
      title: "Expert Recommendations",
      description: "Get curated product suggestions from our database of 1000+ hair care products, matched specifically to your hair's needs."
    },
    {
      icon: TrendingUp,
      title: "Progress Tracking",
      description: "Monitor your hair health journey over time with detailed reports and see how your hair improves with the right products."
    }
  ];

  return (
    <>
      {/* Hero Section */}
      <HeroSection>
        <Container>
          <HeroContent>
            <h1>
              Discover Your Hair's
              <br />
              Perfect Match
            </h1>
            <p className="subtitle">
              Revolutionary AI technology meets expert knowledge to give you personalized hair care recommendations that actually work. No more guessing, no more wasted money.
            </p>
            
            <div className="stats">
              <StatItem>
                <span className="number">50K+</span>
                <span className="label">Happy Users</span>
              </StatItem>
              <StatItem>
                <span className="number">1000+</span>
                <span className="label">Products Analyzed</span>
              </StatItem>
              <StatItem>
                <span className="number">95%</span>
                <span className="label">Accuracy Rate</span>
              </StatItem>
            </div>
            
            <CTAButton 
              size="lg" 
              onClick={() => window.location.href = '/quiz'}
            >
              <Sparkles size={20} className="me-2" />
              Start Your Free Analysis
              <ArrowRight size={20} className="ms-2" />
            </CTAButton>
            
            <div style={{ marginTop: '2rem', color: '#6B7280', fontSize: '0.875rem' }}>
              ✨ Free forever • No credit card required • Results in 5 minutes
            </div>
          </HeroContent>
        </Container>
      </HeroSection>

      {/* Features Section */}
      <FeatureSection>
        <Container>
          <SectionTitle>
            <h2>How It Works</h2>
            <p>
              Our cutting-edge technology makes hair care simple and effective. 
              Get professional-grade analysis from the comfort of your home.
            </p>
          </SectionTitle>
          
          <FeatureGrid>
            {features.map((feature, index) => (
              <FeatureCard key={index}>
                <div className="icon-wrapper">
                  <feature.icon size={32} style={{ color: colors.PRIMARY.primary }} />
                </div>
                <h3>{feature.title}</h3>
                <p>{feature.description}</p>
              </FeatureCard>
            ))}
          </FeatureGrid>
        </Container>
      </FeatureSection>

      {/* Trust Section */}
      <TrustSection>
        <Container>
          <SectionTitle>
            <h2>Trusted by Hair Care Enthusiasts</h2>
            <p>Join thousands who have transformed their hair care routine with our AI-powered recommendations.</p>
          </SectionTitle>
          
          <div className="trust-indicators">
            <TrustItem>
              <Shield size={24} className="icon" />
              <span>100% Secure & Private</span>
            </TrustItem>
            <TrustItem>
              <Star size={24} className="icon" />
              <span>Expert Approved</span>
            </TrustItem>
            <TrustItem>
              <Users size={24} className="icon" />
              <span>50,000+ Happy Users</span>
            </TrustItem>
            <TrustItem>
              <Heart size={24} className="icon" />
              <span>Free Forever</span>
            </TrustItem>
          </div>
        </Container>
      </TrustSection>
    </>
  );
};

export default ModernLandingVariant;
