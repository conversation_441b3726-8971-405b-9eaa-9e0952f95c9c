import { useQuery } from "@tanstack/react-query";
import api from "../../api";

export interface SessionGuidResponse {
  session_guid: string;
  updated_at: string;
}

export interface SessionGuidWithCompletionResponse {
  session_guid: string;
  updated_at: string;
  is_completed: boolean;
  replies_count: number;
  total_questions: number;
}

export const useUserSessionGuid = (userId?: number) => {
  return useQuery<SessionGuidResponse[], Error>({
    queryKey: ["userSessionGuid", userId],
    queryFn: async () => {
      if (!userId) {
        console.warn("useUserSessionGuid: No user ID provided");
        throw new Error("No user ID provided");
      }

      console.log(`useUserSessionGuid: Fetching sessions for user ID: ${userId}`);

      try {
        const res = await api.get<SessionGuidResponse[]>(
          `/quiz/replies/sessions/user/${userId}`
        );
        console.log(`useUserSessionGuid: Successfully fetched ${res.data.length} sessions for user ${userId}`);
        return res.data;
      } catch (error: any) {
        console.error(`useUserSessionGuid: Error fetching sessions for user ${userId}:`, error);

        // Handle 404 specifically - user has no sessions yet
        if (error.response?.status === 404) {
          console.log(`useUserSessionGuid: User ${userId} has no sessions yet (404)`);
          return []; // Return empty array instead of throwing
        }

        throw error;
      }
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 404 errors (user not found)
      if (error?.response?.status === 404) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
};

export const useUserSessionsWithCompletion = (userId?: number) => {
  return useQuery<SessionGuidWithCompletionResponse[], Error>({
    queryKey: ["userSessionsWithCompletion", userId],
    queryFn: async () => {
      if (!userId) {
        console.warn("useUserSessionsWithCompletion: No user ID provided");
        throw new Error("No user ID provided");
      }

      console.log(`useUserSessionsWithCompletion: Fetching sessions for user ID: ${userId}`);

      try {
        const res = await api.get<SessionGuidWithCompletionResponse[]>(
          `/quiz/replies/sessions/user/${userId}/with-completion`
        );
        console.log(`useUserSessionsWithCompletion: Successfully fetched ${res.data.length} sessions for user ${userId}`);
        return res.data;
      } catch (error: any) {
        console.error(`useUserSessionsWithCompletion: Error fetching sessions for user ${userId}:`, error);

        // Handle 404 specifically - user has no sessions yet
        if (error.response?.status === 404) {
          console.log(`useUserSessionsWithCompletion: User ${userId} has no sessions yet (404)`);
          return []; // Return empty array instead of throwing
        }

        throw error;
      }
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 404 errors (user not found)
      if (error?.response?.status === 404) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
};
