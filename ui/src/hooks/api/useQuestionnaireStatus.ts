import { useQuery } from "@tanstack/react-query";
import api from "../../api";

export interface QuestionnaireStatus {
  questionaire_completed: boolean;
  completed_questions: number;
  total_questions: number;
  status: 'NotStarted' | 'Started' | 'Completed';
}

export const useQuestionnaireStatus = (userId?: number) => {
  return useQuery<QuestionnaireStatus>({
    queryKey: ["questionnaireStatus", userId],
    queryFn: async () => {
      if (!userId) {
        throw new Error("User ID is required");
      }

      try {
        // Try to get user profile with questionnaire status
        const response = await api.get(`/api/users/${userId}/profile`);
        const profile = response.data;
        
        // Get total questions count
        const questionsResponse = await api.get('/api/questionaire/questions/count');
        const totalQuestions = questionsResponse.data.count || 0;
        
        const completedQuestions = profile.completed_questions || 0;
        const questionaireCompleted = profile.questionaire_completed || false;
        
        let status: 'NotStarted' | 'Started' | 'Completed' = 'NotStarted';
        
        if (completedQuestions === 0) {
          status = 'NotStarted';
        } else if (completedQuestions >= totalQuestions || questionaireCompleted) {
          status = 'Completed';
        } else {
          status = 'Started';
        }
        
        return {
          questionaire_completed: questionaireCompleted,
          completed_questions: completedQuestions,
          total_questions: totalQuestions,
          status
        };
      } catch (error) {
        console.error("Error fetching questionnaire status:", error);
        // Return default values if API fails
        return {
          questionaire_completed: false,
          completed_questions: 0,
          total_questions: 0,
          status: 'NotStarted' as const
        };
      }
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
};
