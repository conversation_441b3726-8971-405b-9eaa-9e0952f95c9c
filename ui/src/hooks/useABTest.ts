import { useState, useEffect } from 'react';

export type ABTestVariant = 'A' | 'B';

export interface ABTestConfig {
  testName: string;
  variants: ABTestVariant[];
  weights?: number[]; // Optional weights for each variant (defaults to equal split)
}

export interface ABTestResult {
  variant: ABTestVariant;
  isLoading: boolean;
  trackEvent: (eventName: string, properties?: Record<string, any>) => void;
}

/**
 * Custom hook for A/B testing with localStorage persistence and analytics tracking
 */
export function useABTest(config: ABTestConfig): ABTestResult {
  const [variant, setVariant] = useState<ABTestVariant>('A');
  const [isLoading, setIsLoading] = useState(true);

  const { testName, variants, weights = [0.5, 0.5] } = config;

  useEffect(() => {
    const storageKey = `ab_test_${testName}`;
    
    // Check if user already has a variant assigned
    const existingVariant = localStorage.getItem(storageKey) as ABTestVariant;
    
    if (existingVariant && variants.includes(existingVariant)) {
      setVariant(existingVariant);
      setIsLoading(false);
      
      // Track that user saw this variant
      trackEvent('ab_test_variant_shown', {
        testName,
        variant: existingVariant,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        isReturningUser: true
      });
    } else {
      // Assign new variant based on weights
      const randomValue = Math.random();
      let cumulativeWeight = 0;
      let assignedVariant: ABTestVariant = 'A';

      for (let i = 0; i < variants.length; i++) {
        cumulativeWeight += weights[i];
        if (randomValue <= cumulativeWeight) {
          assignedVariant = variants[i];
          break;
        }
      }

      // Store the assignment
      localStorage.setItem(storageKey, assignedVariant);
      setVariant(assignedVariant);
      setIsLoading(false);

      // Track the new assignment
      trackEvent('ab_test_variant_assigned', {
        testName,
        variant: assignedVariant,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        isNewUser: true,
        randomValue
      });

      // Also track that user saw this variant
      trackEvent('ab_test_variant_shown', {
        testName,
        variant: assignedVariant,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        isNewUser: true
      });
    }
  }, [testName, variants, weights]);

  const trackEvent = (eventName: string, properties: Record<string, any> = {}) => {
    const eventData = {
      event: eventName,
      testName,
      variant,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      referrer: document.referrer,
      ...properties
    };

    // Log to console for development
    console.log('A/B Test Event:', eventData);

    // Store in localStorage for later analysis
    const eventsKey = `ab_test_events_${testName}`;
    const existingEvents = JSON.parse(localStorage.getItem(eventsKey) || '[]');
    existingEvents.push(eventData);
    
    // Keep only last 100 events to prevent localStorage bloat
    if (existingEvents.length > 100) {
      existingEvents.splice(0, existingEvents.length - 100);
    }
    
    localStorage.setItem(eventsKey, JSON.stringify(existingEvents));

    // TODO: Send to analytics service (Google Analytics, Mixpanel, etc.)
    // Example for Google Analytics:
    // if (window.gtag) {
    //   window.gtag('event', eventName, {
    //     custom_parameter_1: testName,
    //     custom_parameter_2: variant,
    //     ...properties
    //   });
    // }

    // Example for custom analytics endpoint:
    // fetch('/api/analytics/track', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(eventData)
    // }).catch(console.error);
  };

  return {
    variant,
    isLoading,
    trackEvent
  };
}

/**
 * Utility function to get A/B test results for analysis
 */
export function getABTestResults(testName: string) {
  const eventsKey = `ab_test_events_${testName}`;
  const events = JSON.parse(localStorage.getItem(eventsKey) || '[]');
  
  const variantStats = events.reduce((stats: Record<string, any>, event: any) => {
    const variant = event.variant;
    if (!stats[variant]) {
      stats[variant] = {
        variant,
        views: 0,
        clicks: 0,
        conversions: 0,
        events: []
      };
    }
    
    stats[variant].events.push(event);
    
    if (event.event === 'ab_test_variant_shown') {
      stats[variant].views++;
    } else if (event.event === 'cta_click') {
      stats[variant].clicks++;
    } else if (event.event === 'conversion') {
      stats[variant].conversions++;
    }
    
    return stats;
  }, {});

  // Calculate rates
  Object.values(variantStats).forEach((stats: any) => {
    stats.clickRate = stats.views > 0 ? (stats.clicks / stats.views * 100).toFixed(2) : '0.00';
    stats.conversionRate = stats.views > 0 ? (stats.conversions / stats.views * 100).toFixed(2) : '0.00';
  });

  return variantStats;
}

/**
 * Utility function to clear A/B test data (for testing purposes)
 */
export function clearABTestData(testName: string) {
  localStorage.removeItem(`ab_test_${testName}`);
  localStorage.removeItem(`ab_test_events_${testName}`);
}
