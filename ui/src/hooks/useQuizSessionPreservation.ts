import { useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '../redux/hooks';
import { setSessionGuid, updateQuizAnswers } from '../redux/QuizSlice';

interface PreservedQuizSession {
  session_guid: string;
  answers: any;
  timestamp: string;
  step: number;
}

export const useQuizSessionPreservation = () => {
  const dispatch = useAppDispatch();
  const quizState = useAppSelector((state) => state.quiz);

  // Save current quiz session to localStorage
  const preserveQuizSession = () => {
    if (!quizState.session_guid) {
      console.warn('No session GUID to preserve');
      return;
    }

    const preservedSession: PreservedQuizSession = {
      session_guid: quizState.session_guid,
      answers: quizState,
      timestamp: new Date().toISOString(),
      step: quizState.current_step || 0
    };

    localStorage.setItem('preservedQuizSession', JSON.stringify(preservedSession));
    console.log('Quiz session preserved for auth flow:', preservedSession.session_guid);
  };

  // Restore quiz session from localStorage
  const restoreQuizSession = () => {
    try {
      const preserved = localStorage.getItem('preservedQuizSession');
      if (!preserved) {
        console.log('No preserved quiz session found');
        return null;
      }

      const session: PreservedQuizSession = JSON.parse(preserved);
      
      // Check if session is not too old (24 hours)
      const sessionAge = Date.now() - new Date(session.timestamp).getTime();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      
      if (sessionAge > maxAge) {
        console.log('Preserved quiz session expired, removing');
        localStorage.removeItem('preservedQuizSession');
        return null;
      }

      // Restore the session
      dispatch(setSessionGuid(session.session_guid));
      dispatch(updateQuizAnswers(session.answers));

      console.log('Quiz session restored after auth:', session.session_guid);
      
      // Clean up after restoration
      localStorage.removeItem('preservedQuizSession');
      
      return session;
    } catch (error) {
      console.error('Error restoring quiz session:', error);
      localStorage.removeItem('preservedQuizSession');
      return null;
    }
  };

  // Check if there's a preserved session on mount
  const hasPreservedSession = () => {
    try {
      const preserved = localStorage.getItem('preservedQuizSession');
      if (!preserved) return false;

      const session: PreservedQuizSession = JSON.parse(preserved);
      
      // Check if session is not too old
      const sessionAge = Date.now() - new Date(session.timestamp).getTime();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      
      return sessionAge <= maxAge;
    } catch {
      return false;
    }
  };

  // Clear preserved session
  const clearPreservedSession = () => {
    localStorage.removeItem('preservedQuizSession');
    console.log('Preserved quiz session cleared');
  };

  return {
    preserveQuizSession,
    restoreQuizSession,
    hasPreservedSession,
    clearPreservedSession
  };
};
