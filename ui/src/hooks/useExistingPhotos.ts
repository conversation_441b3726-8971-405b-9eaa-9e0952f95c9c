import { useState, useEffect } from 'react';
import api from '../api';

interface ExistingPhoto {
  question_id: number;
  image_url: string;
  uploaded_at?: string;
}

interface PhotoStatus {
  count: number;
  has_photos: boolean;
  is_complete: boolean;
}

interface UseExistingPhotosReturn {
  existingPhotos: ExistingPhoto[];
  photoStatus: PhotoStatus | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useExistingPhotos = (userId: number | string, sessionGuid: string): UseExistingPhotosReturn => {
  const [existingPhotos, setExistingPhotos] = useState<ExistingPhoto[]>([]);
  const [photoStatus, setPhotoStatus] = useState<PhotoStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchExistingPhotos = async () => {
    if (!userId || !sessionGuid) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Fetch both photo list and count in parallel
      const [photosResponse, countResponse] = await Promise.all([
        api.get(`/api/quiz/replies/images/user/${userId}/session/${sessionGuid}`),
        api.get(`/api/quiz/replies/images/user/${userId}/session/${sessionGuid}/count`)
      ]);

      setExistingPhotos(photosResponse.data || []);
      setPhotoStatus(countResponse.data || { count: 0, has_photos: false, is_complete: false });

      console.log('Existing photos loaded:', {
        photos: photosResponse.data?.length || 0,
        status: countResponse.data
      });

    } catch (err: any) {
      console.error('Error fetching existing photos:', err);
      setError(err.response?.data?.message || 'Failed to load existing photos');
      setExistingPhotos([]);
      setPhotoStatus({ count: 0, has_photos: false, is_complete: false });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchExistingPhotos();
  }, [userId, sessionGuid]);

  return {
    existingPhotos,
    photoStatus,
    loading,
    error,
    refetch: fetchExistingPhotos
  };
};
