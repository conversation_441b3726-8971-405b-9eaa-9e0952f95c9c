import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useAppSelector } from '../redux/hooks';
import { setSessionGuid, updateQuizAnswers, setCurrentStep } from '../redux/QuizSlice';
import api from '../api';

interface QuizRestoreData {
  session_guid: string;
  answers: Record<string, any>;
  current_step: number;
}

export const useQuizRestore = () => {
  const dispatch = useDispatch();
  const currentSessionGuid = useAppSelector((state) => state.quiz.session_guid);

  useEffect(() => {
    const restoreQuizSession = async () => {
      // Check if there's a session to continue from localStorage
      const continueSessionGuid = localStorage.getItem('continueSessionGuid');
      
      if (continueSessionGuid && continueSessionGuid !== currentSessionGuid) {
        try {
          // Fetch the session data from API
          const response = await api.get(`/questionaire/session/${continueSessionGuid}`);
          
          if (response.data && response.data.data) {
            const sessionData = response.data.data;
            
            // Convert API response to quiz answers format
            const answers: Record<string, any> = {};
            let maxQuestionId = 0;
            
            sessionData.forEach((reply: any) => {
              if (reply.question_id) {
                answers[reply.question_id.toString()] = reply.value || reply.text || '';
                maxQuestionId = Math.max(maxQuestionId, reply.question_id);
              }
            });

            // Estimate current step based on answered questions
            // This is a rough mapping - you might need to adjust based on your quiz structure
            let estimatedStep = 1; // Start after landing page
            if (maxQuestionId >= 1) estimatedStep = 1; // General questions
            if (maxQuestionId >= 3) estimatedStep = 2; // Upload photos (assuming question 3+ are after photos)
            if (maxQuestionId >= 5) estimatedStep = 3; // Extra information
            if (maxQuestionId >= 10) estimatedStep = 4; // Medical conditions
            if (maxQuestionId >= 15) estimatedStep = 5; // Lifestyle
            if (maxQuestionId >= 20) estimatedStep = 6; // Hair care
            if (maxQuestionId >= 25) estimatedStep = 7; // Preferences

            // Restore the session
            dispatch(setSessionGuid(continueSessionGuid));
            dispatch(updateQuizAnswers(answers));
            dispatch(setCurrentStep(estimatedStep));

            console.log('Quiz session restored:', {
              sessionGuid: continueSessionGuid,
              answersCount: Object.keys(answers).length,
              estimatedStep,
              maxQuestionId
            });
          }
        } catch (error) {
          console.error('Failed to restore quiz session:', error);
        } finally {
          // Clear the continue session flag
          localStorage.removeItem('continueSessionGuid');
        }
      }
    };

    restoreQuizSession();
  }, [dispatch, currentSessionGuid]);
};
