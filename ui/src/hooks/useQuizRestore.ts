import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useAppSelector } from '../redux/hooks';
import { setSessionGuid, updateQuizAnswers, setCurrentStep, setUploadedPhotosCount, setHasExistingPhotos } from '../redux/QuizSlice';
import api from '../api';

interface QuizRestoreData {
  session_guid: string;
  answers: Record<string, any>;
  current_step: number;
}

export const useQuizRestore = () => {
  const dispatch = useDispatch();
  const currentSessionGuid = useAppSelector((state) => state.quiz.session_guid);

  useEffect(() => {
    const restoreQuizSession = async () => {
      // Check if there's a session to continue from localStorage
      const continueSessionGuid = localStorage.getItem('continueSessionGuid');

      if (continueSessionGuid && continueSessionGuid !== currentSessionGuid) {
        try {
          // Fetch the session data from API
          const response = await api.get(`/questionaire/session/${continueSessionGuid}`);

          if (response.data && response.data.data) {
            const sessionData = response.data.data;

            // Convert API response to quiz answers format
            const answers: Record<string, any> = {};
            let maxQuestionId = 0;

            sessionData.forEach((reply: any) => {
              if (reply.question_id) {
                answers[reply.question_id.toString()] = reply.value || reply.text || '';
                maxQuestionId = Math.max(maxQuestionId, reply.question_id);
              }
            });

            // Estimate current step based on answered questions
            // This mapping is based on the actual quiz structure
            let estimatedStep = 1; // Start after landing page
            const uniqueQuestions = Object.keys(answers).length;

            if (uniqueQuestions >= 1) estimatedStep = 1; // General questions (questions 1-2)
            if (uniqueQuestions >= 3) estimatedStep = 2; // Upload photos (after initial questions)
            if (uniqueQuestions >= 4) estimatedStep = 3; // Extra information
            if (uniqueQuestions >= 10) estimatedStep = 4; // Medical conditions
            if (uniqueQuestions >= 20) estimatedStep = 5; // Lifestyle
            if (uniqueQuestions >= 30) estimatedStep = 6; // Hair care
            if (uniqueQuestions >= 40) estimatedStep = 7; // Preferences
            if (uniqueQuestions >= 46) estimatedStep = 8; // Analysis (completed)

            // Check for existing photos
            try {
              const auth = JSON.parse(localStorage.getItem("auth") || "{}");
              const userId = auth?.user?.id;

              if (userId) {
                const photoResponse = await api.get(`/api/quiz/replies/images/user/${userId}/session/${continueSessionGuid}/count`);
                const photoStatus = photoResponse.data;

                if (photoStatus?.has_photos) {
                  dispatch(setUploadedPhotosCount(photoStatus.count));
                  dispatch(setHasExistingPhotos(true));
                  console.log('Existing photos found:', photoStatus);
                }
              }
            } catch (error) {
              console.warn('Could not check for existing photos:', error);
            }

            // Restore the session
            dispatch(setSessionGuid(continueSessionGuid));
            dispatch(updateQuizAnswers(answers));
            dispatch(setCurrentStep(estimatedStep));

            console.log('Quiz session restored:', {
              sessionGuid: continueSessionGuid,
              answersCount: Object.keys(answers).length,
              uniqueQuestions,
              estimatedStep,
              maxQuestionId
            });
          }
        } catch (error) {
          console.error('Failed to restore quiz session:', error);
        } finally {
          // Clear the continue session flag
          localStorage.removeItem('continueSessionGuid');
        }
      }
    };

    restoreQuizSession();
  }, [dispatch, currentSessionGuid]);
};
