import { createSlice } from "@reduxjs/toolkit";
import { PURGE } from "redux-persist";

interface QuizState {
  session_guid?: string;
  current_step?: number;
  completed_steps?: number[];
  last_updated?: string;
  is_completed?: boolean;
  [key: string]: any; // For quiz answers
}

const initialState: QuizState = {};

const QuizSlice = createSlice({
  name: "quiz",
  initialState,
  reducers: {
    updateQuizAnswers: (state, action) => {
      return {
        ...state,
        ...action.payload,
      };
    },
    updateQuizAnswer: (state, action) => {
      const { key, value, answerType } = action.payload;
      // Ensure currentValue is always an array of strings
      const currentValue: string[] = (
        Array.isArray(state[key])
          ? state[key]
          : typeof state[key] === "string"
            ? [state[key]]
            : []
      ) as string[];


      return {
        ...state,
        [key]:
          answerType === "MC"
            ? // currentValue.includes(value)
            //   ? currentValue.filter((v) => v !== value) // Remove if exists
            [...currentValue, value] // Add if not exists
            : [value], // Replace for single-answer types
      };
    },
    setSessionGuid: (state, action) => {
      state.session_guid = action.payload;
      state.last_updated = new Date().toISOString();
    },
    setCurrentStep: (state, action) => {
      state.current_step = action.payload;
      state.last_updated = new Date().toISOString();

      // Add to completed steps if not already there
      if (!state.completed_steps) {
        state.completed_steps = [];
      }
      if (action.payload > 0 && !state.completed_steps.includes(action.payload)) {
        state.completed_steps.push(action.payload);
      }
    },
    markQuizCompleted: (state) => {
      state.is_completed = true;
      state.last_updated = new Date().toISOString();
    },
    resetQuiz: (state) => {
      // Keep session_guid but reset progress
      const sessionGuid = state.session_guid;
      Object.keys(state).forEach(key => {
        if (key !== 'session_guid') {
          delete state[key];
        }
      });
      state.session_guid = sessionGuid;
      state.current_step = 0;
      state.completed_steps = [];
      state.is_completed = false;
      state.last_updated = new Date().toISOString();
    },
  },
  extraReducers: (builder) => {
    builder.addCase(PURGE, () => initialState);
  },
});

export const {
  updateQuizAnswers,
  updateQuizAnswer,
  setSessionGuid,
  setCurrentStep,
  markQuizCompleted,
  resetQuiz
} = QuizSlice.actions;

export default QuizSlice.reducer;
