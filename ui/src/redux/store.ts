import { combineReducers, configureStore } from "@reduxjs/toolkit";
import createSagaMiddleware from "redux-saga";
import { persistReducer, persistStore } from "redux-persist";
import storage from "redux-persist/lib/storage"; // defaults to localStorage for web
import UserReducer from "./UserSlice";
import QuizReducer from "./QuizSlice";

// Persist configuration
const config = {
  key: "root",
  storage: storage,
  debug: false,
  whitelist: ["user", "season", "auth", "quiz"],
};

const allReducer = {
  user: UserReducer,
  quiz: QuizReducer,
};

const rootReducer = combineReducers(allReducer);

export type RootState = ReturnType<typeof rootReducer>;
export type AppDispatch = typeof store.dispatch;

const sagaMiddleware = createSagaMiddleware();

const persistedReducer = persistReducer(config, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }).concat(sagaMiddleware),
});

export const persistor = persistStore(store);
