import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Col } from 'react-bootstrap';
import {
  Lock,
  CheckCircle,
  ArrowRight,
  Target,
  Sparkles,
  TrendingUp,
  Award,
  BarChart3,
  ShoppingBag,
  Calendar,
  Star
} from 'lucide-react';
import colors from '../../styles/colors';

interface QuizPaywallProps {
  onLogin: () => void;
  onSignup: () => void;
  onClose?: () => void;
}

const QuizPaywall: React.FC<QuizPaywallProps> = ({
  onLogin,
  onSignup,
  onClose
}) => {
  const benefits = [
    {
      icon: <BarChart3 size={24} />,
      title: "Detailed Hair Analysis",
      description: "Get comprehensive scores for dryness, damage, oil levels, and scalp health"
    },
    {
      icon: <Target size={24} />,
      title: "Personalized Recommendations",
      description: "AI-powered product suggestions tailored to your unique hair profile"
    },
    {
      icon: <TrendingUp size={24} />,
      title: "Progress Tracking",
      description: "Monitor your hair health journey with detailed reports over time"
    },
    {
      icon: <ShoppingBag size={24} />,
      title: "Curated Product Matches",
      description: "Access to our database of 1000+ hair products matched to your needs"
    },
    {
      icon: <Calendar size={24} />,
      title: "Ongoing Support",
      description: "Regular updates and new recommendations as your hair changes"
    },
    {
      icon: <Award size={24} />,
      title: "Expert Insights",
      description: "Professional hair care advice from certified trichologists"
    }
  ];

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        backdropFilter: 'blur(12px)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000,
        padding: '20px'
      }}
    >
      <Card
        className="border-0 shadow-lg"
        style={{
          borderRadius: '24px',
          maxWidth: '800px',
          width: '100%',
          background: colors.WHITE.primary,
          overflow: 'hidden',
          maxHeight: '90vh',
          overflowY: 'auto'
        }}
      >
        {/* Header with gradient */}
        <div
          style={{
            background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
            padding: '2.5rem 2rem',
            textAlign: 'center',
            color: colors.WHITE.primary
          }}
        >
          <div
            className="p-3 rounded-4 d-inline-flex mb-3"
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              backdropFilter: 'blur(10px)'
            }}
          >
            <Sparkles size={40} />
          </div>
          <h2 className="fs-2 fw-bold mb-3">🎉 Quiz Complete!</h2>
          <p className="fs-4 mb-0 opacity-90">
            Create your free account to unlock your personalized hair analysis results
          </p>
        </div>

        <Card.Body className="p-4">
          {/* Value Proposition */}
          <div className="text-center mb-4">
            <h3 className="fw-bold mb-3" style={{ color: '#1F2937' }}>
              What You'll Get With Your Free Account:
            </h3>
          </div>

          {/* Benefits Grid */}
          <Row className="g-3 mb-4">
            {benefits.map((benefit, index) => (
              <Col md={6} key={index}>
                <div 
                  className="d-flex align-items-start p-3 rounded-3"
                  style={{ backgroundColor: '#F8FAFC' }}
                >
                  <div
                    className="p-2 rounded-3 me-3 flex-shrink-0"
                    style={{
                      backgroundColor: colors.PRIMARY.pastel,
                      color: colors.PRIMARY.primary
                    }}
                  >
                    {benefit.icon}
                  </div>
                  <div>
                    <h6 className="fw-semibold mb-1" style={{ color: '#1F2937' }}>
                      {benefit.title}
                    </h6>
                    <p className="text-muted mb-0 small">
                      {benefit.description}
                    </p>
                  </div>
                </div>
              </Col>
            ))}
          </Row>

          {/* Trust Indicators */}
          <div 
            className="text-center p-3 rounded-3 mb-4"
            style={{ backgroundColor: colors.GREEN.pastel }}
          >
            <div className="d-flex justify-content-center align-items-center gap-4 flex-wrap">
              <div className="d-flex align-items-center gap-2">
                <CheckCircle size={20} style={{ color: colors.GREEN.primary }} />
                <span className="fw-semibold" style={{ color: colors.GREEN.primary }}>
                  100% Free Forever
                </span>
              </div>
              <div className="d-flex align-items-center gap-2">
                <Star size={20} style={{ color: colors.GREEN.primary }} />
                <span className="fw-semibold" style={{ color: colors.GREEN.primary }}>
                  50+ Hair Experts
                </span>
              </div>
              <div className="d-flex align-items-center gap-2">
                <Lock size={20} style={{ color: colors.GREEN.primary }} />
                <span className="fw-semibold" style={{ color: colors.GREEN.primary }}>
                  Secure & Private
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <Row className="g-3">
            <Col md={6}>
              <Button
                onClick={onSignup}
                className="w-100 py-3 border-0 shadow-sm"
                style={{
                  background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
                  borderRadius: '16px',
                  fontSize: '16px',
                  fontWeight: '600',
                  color: colors.WHITE.primary,
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = `0 8px 25px rgba(67, 0, 255, 0.3)`;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                <Sparkles size={20} className="me-2" />
                Create Free Account
              </Button>
            </Col>
            <Col md={6}>
              <Button
                onClick={onLogin}
                variant="outline-primary"
                className="w-100 py-3"
                style={{
                  borderRadius: '16px',
                  fontSize: '16px',
                  fontWeight: '600',
                  borderColor: colors.PRIMARY.primary,
                  color: colors.PRIMARY.primary,
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.PRIMARY.primary;
                  e.currentTarget.style.color = colors.WHITE.primary;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = colors.PRIMARY.primary;
                }}
              >
                <ArrowRight size={20} className="me-2" />
                Already Have Account? Sign In
              </Button>
            </Col>
          </Row>

          {/* Footer Note */}
          <div className="text-center mt-4">
            <p className="text-muted small mb-0">
              Your quiz answers are saved and will be available immediately after you sign in.
              <br />
              No need to retake the quiz! 🎉
            </p>
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default QuizPaywall;
