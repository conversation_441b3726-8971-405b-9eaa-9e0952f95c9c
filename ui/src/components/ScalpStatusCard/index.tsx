import React from 'react';
import { Card } from 'react-bootstrap';
import { useQuery } from '@tanstack/react-query';
import {
    AlertTriangle,
    CheckCircle,
    TrendingUp,
    Droplets,
    Shield,
    Zap,
    RefreshCw
} from 'lucide-react';
import api from '../../api';
import { getHighestScoreItem, ScoreItem } from '../../utils/dashboard';
import { FIVE_MINUTES } from '../../constants';
import colors from '../../styles/colors';

interface ScoreResponse {
    dry_score_percentage: ScoreItem;
    damage_score_percentage: ScoreItem;
    sensitivity_percentage: ScoreItem;
    sebum_oily_percentage: ScoreItem;
    sebum_dry_percentage: ScoreItem;
    dsc_percentage: ScoreItem;
    flake_score_percentage: ScoreItem;
}

interface Props {
    userId: string;
    sessionGuid: string;
}

// Enhanced scalp content with modern descriptions
const scalpContent: Record<string, {
    title: string;
    description: string;
    icon: React.ComponentType<any>;
    color: string;
    bgColor: string;
    recommendation: string;
}> = {
    oil: {
        title: 'Oily Scalp',
        description: 'Your scalp produces excess sebum, which can lead to greasy hair and potential buildup.',
        icon: Droplets,
        color: colors.BLUE.primary,
        bgColor: colors.BLUE.pastel,
        recommendation: 'Use clarifying shampoos and avoid over-washing to balance oil production.'
    },
    dry: {
        title: 'Dry Scalp',
        description: 'Your scalp lacks sufficient moisture and natural oils, which may cause flaking and irritation.',
        icon: AlertTriangle,
        color: colors.ORANGE.primary,
        bgColor: colors.ORANGE.pastel,
        recommendation: 'Focus on hydrating treatments and gentle, moisturizing hair care products.'
    },
    sensitive: {
        title: 'Sensitive Scalp',
        description: 'Your scalp is reactive and may be prone to irritation from products or environmental factors.',
        icon: Shield,
        color: colors.GREEN.primary,
        bgColor: colors.GREEN.pastel,
        recommendation: 'Choose gentle, fragrance-free products and avoid harsh chemicals.'
    },
    flake: {
        title: 'Flaky Scalp',
        description: 'Your scalp shows signs of flaking, which could indicate dryness or mild dermatitis.',
        icon: RefreshCw,
        color: colors.PURPLE.primary,
        bgColor: colors.PURPLE.pastel,
        recommendation: 'Use anti-dandruff treatments and maintain consistent scalp care routine.'
    },
    damage: {
        title: 'Damaged Scalp',
        description: 'Your scalp shows signs of damage that may affect hair growth and overall scalp health.',
        icon: Zap,
        color: colors.RED.primary,
        bgColor: colors.RED.pastel,
        recommendation: 'Focus on repairing treatments and protect your scalp from further damage.'
    },
};

// Map API keys to simplified keys
const scalpKeyMap: Record<string, string> = {
    sebum_oily_percentage: 'oil',
    sebum_dry_percentage: 'dry',
    dry_score_percentage: 'dry',
    sensitivity_percentage: 'sensitive',
    flake_score_percentage: 'flake',
    damage_score_percentage: 'damage',
    dsc_percentage: 'dry',
};

const fetchScores = async (
    userId: string,
    sessionGuid: string
): Promise<ScoreResponse> => {
    const res = await api.get<ScoreResponse>(
        `/users/id/${userId}/session/${sessionGuid}/scores`
    );
    return res.data;
};

const ScalpStatusCard: React.FC<Props> = ({ userId, sessionGuid }) => {
    const { data, isLoading, isError, error } = useQuery<ScoreResponse, Error>({
        queryKey: ['scoresScalpData', userId, sessionGuid],
        queryFn: () => fetchScores(userId, sessionGuid),
        enabled: !!userId && !!sessionGuid,
        staleTime: FIVE_MINUTES,
    });

    if (isLoading) {
        return (
            <Card className="border-0 shadow-sm" style={{ borderRadius: '16px' }}>
                <Card.Body className="p-4 text-center">
                    <div className="d-flex flex-column align-items-center">
                        <div className="p-3 rounded-4 mb-3" style={{
                            backgroundColor: colors.PRIMARY.pastel,
                            animation: 'pulse 2s infinite'
                        }}>
                            <TrendingUp size={24} style={{ color: colors.PRIMARY.primary }} />
                        </div>
                        <h6 className="fw-semibold mb-2" style={{ color: '#1F2937' }}>
                            Analyzing Scalp Health
                        </h6>
                        <p className="text-muted mb-0 small">
                            Processing your diagnostic results...
                        </p>
                    </div>
                </Card.Body>
            </Card>
        );
    }

    if (isError) {
        return (
            <Card className="border-0 shadow-sm" style={{ borderRadius: '16px' }}>
                <Card.Body className="p-4 text-center">
                    <div className="d-flex flex-column align-items-center">
                        <div className="p-3 rounded-4 mb-3" style={{
                            backgroundColor: colors.RED.pastel
                        }}>
                            <AlertTriangle size={24} style={{ color: colors.RED.primary }} />
                        </div>
                        <h6 className="fw-semibold mb-2" style={{ color: '#1F2937' }}>
                            Unable to Load Scalp Analysis
                        </h6>
                        <p className="text-muted mb-0 small">
                            {error?.message || 'Please try again later'}
                        </p>
                    </div>
                </Card.Body>
            </Card>
        );
    }

    if (!data) return null;

    // Include all relevant score types
    const relevantScores: Record<string, ScoreItem> = {
        dry_score_percentage: data.dry_score_percentage,
        sebum_oily_percentage: data.sebum_oily_percentage,
        sebum_dry_percentage: data.sebum_dry_percentage,
        dsc_percentage: data.dsc_percentage,
    };

    const [maxKey, maxItem] = getHighestScoreItem(relevantScores);
    const scalpKey = scalpKeyMap[maxKey] || 'dry';
    const scalpInfo = scalpContent[scalpKey];
    const IconComponent = scalpInfo.icon;

    return (
        <Card className="border-0 shadow-sm" style={{ borderRadius: '16px' }}>
            <Card.Body className="p-4">
                {/* Header with Icon and Title */}
                <div className="d-flex align-items-center mb-3">
                    <div
                        className="p-3 rounded-4 me-3"
                        style={{
                            backgroundColor: scalpInfo.bgColor,
                            border: `1px solid ${scalpInfo.color}20`
                        }}
                    >
                        <IconComponent size={24} style={{ color: scalpInfo.color }} />
                    </div>
                    <div className="flex-grow-1">
                        <h5 className="fw-bold mb-1" style={{ color: '#1F2937' }}>
                            {scalpInfo.title}
                        </h5>
                        <div className="d-flex align-items-center">
                            <span
                                className="badge rounded-pill px-2 py-1 me-2"
                                style={{
                                    backgroundColor: scalpInfo.bgColor,
                                    color: scalpInfo.color,
                                    fontSize: '0.75rem',
                                    fontWeight: '600'
                                }}
                            >
                                {maxItem.value}% Confidence
                            </span>
                            <span className="text-muted small">
                                Primary Concern
                            </span>
                        </div>
                    </div>
                </div>

                {/* Description */}
                <div className="mb-3">
                    <p className="text-muted mb-0" style={{ lineHeight: '1.6' }}>
                        {scalpInfo.description}
                    </p>
                </div>

                {/* Recommendation */}
                <div
                    className="p-3 rounded-3"
                    style={{
                        backgroundColor: '#F8FAFC',
                        border: `1px solid ${scalpInfo.color}20`
                    }}
                >
                    <div className="d-flex align-items-start">
                        <div
                            className="p-2 rounded-3 me-3 flex-shrink-0"
                            style={{
                                backgroundColor: scalpInfo.bgColor,
                                border: `1px solid ${scalpInfo.color}20`
                            }}
                        >
                            <CheckCircle size={16} style={{ color: scalpInfo.color }} />
                        </div>
                        <div>
                            <h6 className="fw-semibold mb-1" style={{ color: '#1F2937', fontSize: '0.875rem' }}>
                                Recommended Action
                            </h6>
                            <p className="text-muted mb-0 small" style={{ lineHeight: '1.5' }}>
                                {scalpInfo.recommendation}
                            </p>
                        </div>
                    </div>
                </div>
            </Card.Body>
        </Card>
    );
};

export default ScalpStatusCard;
