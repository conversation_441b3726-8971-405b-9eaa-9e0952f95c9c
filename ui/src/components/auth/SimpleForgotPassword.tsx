/**
 * Simple Forgot Password Component
 * 
 * A fallback version with minimal dependencies in case the main component has issues
 */

import React, { useState } from "react";
import { useNavigate } from "react-router-dom";

const SimpleForgotPassword: React.FC = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [isError, setIsError] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      setMessage("Email is required.");
      setIsError(true);
      return;
    }

    if (!email.includes("@")) {
      setMessage("Please enter a valid email address.");
      setIsError(true);
      return;
    }

    setIsLoading(true);
    setMessage("");
    setIsError(false);

    try {
      // Simple fetch instead of axios to avoid dependency issues
      const response = await fetch(`${process.env.REACT_APP_BACKEND_API || "http://localhost:8000/api"}/users/password-reset`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        setMessage("Check your inbox for the reset link! We've sent you an email with instructions.");
        setIsError(false);
      } else {
        throw new Error('Failed to send reset email');
      }
    } catch (err) {
      console.error("Password reset error:", err);
      setMessage("Failed to send reset email. Please try again.");
      setIsError(true);
    } finally {
      setIsLoading(false);
    }
  };

  const styles = {
    container: {
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px',
    },
    card: {
      background: 'white',
      borderRadius: '24px',
      padding: '48px',
      width: '100%',
      maxWidth: '480px',
      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)',
    },
    title: {
      fontSize: '32px',
      fontWeight: 700,
      color: '#1a1a1a',
      marginBottom: '8px',
      textAlign: 'center' as const,
    },
    subtitle: {
      fontSize: '16px',
      color: '#6b7280',
      marginBottom: '32px',
      textAlign: 'center' as const,
    },
    form: {
      display: 'flex',
      flexDirection: 'column' as const,
      gap: '24px',
    },
    label: {
      display: 'block',
      fontSize: '14px',
      fontWeight: 600,
      color: '#374151',
      marginBottom: '8px',
    },
    input: {
      width: '100%',
      padding: '16px',
      border: '2px solid #e5e7eb',
      borderRadius: '12px',
      fontSize: '16px',
      outline: 'none',
      background: '#fafafa',
      boxSizing: 'border-box' as const,
    },
    button: {
      width: '100%',
      padding: '16px',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      border: 'none',
      borderRadius: '12px',
      fontSize: '16px',
      fontWeight: 600,
      cursor: 'pointer',
      marginTop: '8px',
    },
    message: {
      padding: '12px 16px',
      borderRadius: '8px',
      fontSize: '14px',
      marginBottom: '16px',
    },
    backButton: {
      background: 'transparent',
      color: '#6b7280',
      border: 'none',
      padding: '8px',
      borderRadius: '8px',
      cursor: 'pointer',
      fontSize: '14px',
      marginBottom: '16px',
    },
    linkText: {
      color: '#667eea',
      textDecoration: 'none',
      fontWeight: 600,
      cursor: 'pointer',
    },
  };

  return (
    <div style={styles.container}>
      <div style={styles.card}>
        <button
          style={styles.backButton}
          onClick={() => navigate("/login")}
        >
          ← Back to login
        </button>

        <h1 style={styles.title}>Forgot password?</h1>
        <p style={styles.subtitle}>
          No worries! Enter your email and we'll send you a reset link.
        </p>

        {message && (
          <div style={{
            ...styles.message,
            background: isError ? '#fef2f2' : '#f0fdf4',
            color: isError ? '#dc2626' : '#16a34a',
            border: `1px solid ${isError ? '#fecaca' : '#bbf7d0'}`,
          }}>
            {message}
          </div>
        )}

        <form style={styles.form} onSubmit={handleSubmit}>
          <div>
            <label style={styles.label}>Email address</label>
            <input
              type="email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                setMessage("");
              }}
              placeholder="Enter your email"
              style={{
                ...styles.input,
                borderColor: email ? '#10b981' : '#e5e7eb',
              }}
              required
              autoFocus
              autoComplete="email"
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            style={{
              ...styles.button,
              opacity: isLoading ? 0.7 : 1,
              cursor: isLoading ? 'not-allowed' : 'pointer',
            }}
          >
            {isLoading ? "Sending reset link..." : "Send reset link"}
          </button>
        </form>

        <div style={{textAlign: 'center', marginTop: '24px', fontSize: '14px', color: '#6b7280'}}>
          Remember your password?{' '}
          <span
            style={styles.linkText}
            onClick={() => navigate("/login")}
          >
            Sign in
          </span>
        </div>
      </div>
    </div>
  );
};

export default SimpleForgotPassword;
