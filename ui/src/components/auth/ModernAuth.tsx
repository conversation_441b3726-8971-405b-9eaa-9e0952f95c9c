/**
 * Modern Authentication Components
 *
 * Clean, modern authentication forms with excellent UX
 */

import React, { useState, useCallback } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useUserActions } from "../../hooks/user.actions";
import {
  Eye,
  EyeOff,
  Mail,
  Lock,
  User,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Loader2,
  Sparkles
} from "lucide-react";
import colors from "../../styles/colors";

// Types
interface UserDetails {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

interface SignupData {
  username: string;
  email: string;
  password: string;
}

// Modern Auth Styles
const authStyles = {
  container: {
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
    padding: '20px',
    position: 'relative' as const,
    overflow: 'hidden',
  },
  backgroundElements: {
    position: 'absolute' as const,
    top: '-50%',
    right: '-10%',
    width: '400px',
    height: '400px',
    background: 'rgba(255, 255, 255, 0.1)',
    borderRadius: '50%',
    filter: 'blur(80px)',
  },
  backgroundElements2: {
    position: 'absolute' as const,
    bottom: '-30%',
    left: '-5%',
    width: '300px',
    height: '300px',
    background: 'rgba(255, 255, 255, 0.08)',
    borderRadius: '50%',
    filter: 'blur(60px)',
  },
  card: {
    background: colors.WHITE.primary,
    borderRadius: '24px',
    padding: '48px',
    width: '100%',
    maxWidth: '480px',
    boxShadow: '0 32px 80px rgba(0, 0, 0, 0.15)',
    backdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    position: 'relative' as const,
    zIndex: 2,
  },
  header: {
    textAlign: 'center' as const,
    marginBottom: '32px',
  },
  title: {
    fontSize: '32px',
    fontWeight: 700,
    color: '#1a1a1a',
    marginBottom: '8px',
  },
  subtitle: {
    fontSize: '16px',
    color: '#6b7280',
    marginBottom: '0',
  },
  form: {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '24px',
  },
  inputGroup: {
    position: 'relative' as const,
  },
  label: {
    display: 'block',
    fontSize: '14px',
    fontWeight: 600,
    color: '#374151',
    marginBottom: '8px',
  },
  inputWrapper: {
    position: 'relative' as const,
  },
  input: {
    width: '100%',
    padding: '16px 48px 16px 48px',
    border: '2px solid #e5e7eb',
    borderRadius: '12px',
    fontSize: '16px',
    transition: 'all 0.2s ease',
    outline: 'none',
    background: '#fafafa',
    boxSizing: 'border-box' as const,
  },
  inputIcon: {
    position: 'absolute' as const,
    left: '16px',
    top: '50%',
    transform: 'translateY(-50%)',
    color: '#9ca3af',
    pointerEvents: 'none' as const,
  },
  eyeIcon: {
    position: 'absolute' as const,
    right: '16px',
    top: '50%',
    transform: 'translateY(-50%)',
    color: '#9ca3af',
    cursor: 'pointer',
    background: 'none',
    border: 'none',
  },
  button: {
    width: '100%',
    padding: '16px',
    background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
    color: colors.WHITE.primary,
    border: 'none',
    borderRadius: '12px',
    fontSize: '16px',
    fontWeight: 600,
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
    marginTop: '8px',
  },
  switchText: {
    textAlign: 'center' as const,
    marginTop: '24px',
    fontSize: '14px',
    color: '#6b7280',
  },
  switchLink: {
    color: colors.PRIMARY.primary,
    textDecoration: 'none',
    fontWeight: 600,
    cursor: 'pointer',
  },
  alert: {
    padding: '12px 16px',
    borderRadius: '8px',
    fontSize: '14px',
    marginBottom: '16px',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  },
  errorAlert: {
    background: '#fef2f2',
    color: '#dc2626',
    border: '1px solid #fecaca',
  },
  successAlert: {
    background: '#f0fdf4',
    color: '#16a34a',
    border: '1px solid #bbf7d0',
  },
  helpText: {
    fontSize: '12px',
    color: '#6b7280',
    marginTop: '4px',
  },
  strengthIndicator: {
    height: '4px',
    borderRadius: '2px',
    marginTop: '8px',
    transition: 'all 0.3s ease',
  },
};

const ModernAuth: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const isLogin = location.pathname.includes("/login");
  const { login, signup } = useUserActions(navigate);

  const [userDetails, setUserDetails] = useState<UserDetails>({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  // Password strength calculation
  const getPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;
    if (/[^A-Za-z0-9]/.test(password)) strength += 25;
    return strength;
  };

  const passwordStrength = getPasswordStrength(userDetails.password);

  const getStrengthColor = (strength: number) => {
    if (strength < 25) return '#ef4444';
    if (strength < 50) return '#f59e0b';
    if (strength < 75) return '#eab308';
    return '#10b981';
  };

  const getStrengthText = (strength: number) => {
    if (strength < 25) return 'Weak';
    if (strength < 50) return 'Fair';
    if (strength < 75) return 'Good';
    return 'Strong';
  };

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUserDetails(prev => ({ ...prev, [name]: value }));
    setErrorMessage("");
  }, []);

  const validateForm = () => {
    const { email, password, name, confirmPassword } = userDetails;

    if (!email || !password) {
      setErrorMessage("Email and password are required.");
      return false;
    }

    if (!email.includes("@")) {
      setErrorMessage("Please enter a valid email address.");
      return false;
    }

    if (!isLogin) {
      if (!name) {
        setErrorMessage("Name is required for signup.");
        return false;
      }
      if (!confirmPassword) {
        setErrorMessage("Please confirm your password.");
        return false;
      }
      if (password !== confirmPassword) {
        setErrorMessage("Passwords do not match.");
        return false;
      }
      if (password.length < 8) {
        setErrorMessage("Password must be at least 8 characters long.");
        return false;
      }
    }

    return true;
  };

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");

    try {
      if (isLogin) {
        const loginData = {
          email: userDetails.email,
          password: userDetails.password,
        };
        await login(loginData);
        setSuccessMessage("Login successful! Redirecting...");
        // Navigation is now handled in the login function based on redirect parameter
      } else {
        const signupData: SignupData = {
          username: userDetails.name,
          email: userDetails.email,
          password: userDetails.password,
        };
        await signup(signupData);
        // Success message and navigation are now handled in the signup function
      }
    } catch (err) {
      console.error("Auth action failed:", err);
      setErrorMessage(err instanceof Error ? err.message : "An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [isLogin, userDetails, login, signup, navigate]);

  return (
    <div style={authStyles.container}>
      {/* Background Elements */}
      <div style={authStyles.backgroundElements} />
      <div style={authStyles.backgroundElements2} />

      <div style={authStyles.card}>
        <div style={authStyles.header}>
          {/* Brand Icon */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            marginBottom: '24px'
          }}>
            <div style={{
              padding: '16px',
              borderRadius: '20px',
              background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
              color: colors.WHITE.primary,
              boxShadow: '0 8px 32px rgba(67, 0, 255, 0.2)'
            }}>
              <Sparkles size={32} />
            </div>
          </div>

          <h1 style={authStyles.title}>
            {isLogin ? "Welcome back" : "Create account"}
          </h1>
          <p style={authStyles.subtitle}>
            {isLogin
              ? "Sign in to your account to continue your hair care journey"
              : "Join us and start your personalized hair care journey"
            }
          </p>
        </div>

        {errorMessage && (
          <div style={{ ...authStyles.alert, ...authStyles.errorAlert }}>
            <AlertCircle size={16} />
            {errorMessage}
          </div>
        )}

        {successMessage && (
          <div style={{ ...authStyles.alert, ...authStyles.successAlert }}>
            <CheckCircle size={16} />
            {successMessage}
          </div>
        )}

        <form style={authStyles.form} onSubmit={handleSubmit}>
          {/* Email Field */}
          <div style={authStyles.inputGroup}>
            <label style={authStyles.label}>Email</label>
            <div style={authStyles.inputWrapper}>
              <Mail size={20} style={authStyles.inputIcon} />
              <input
                type="email"
                name="email"
                value={userDetails.email}
                onChange={handleInputChange}
                placeholder="Enter your email"
                style={{
                  ...authStyles.input,
                  borderColor: userDetails.email ? '#10b981' : '#e5e7eb',
                }}
                required
              />
            </div>
          </div>

          {/* Name Field (Signup only) */}
          {!isLogin && (
            <div style={authStyles.inputGroup}>
              <label style={authStyles.label}>Full Name</label>
              <div style={authStyles.inputWrapper}>
                <User size={20} style={authStyles.inputIcon} />
                <input
                  type="text"
                  name="name"
                  value={userDetails.name}
                  onChange={handleInputChange}
                  placeholder="Enter your full name"
                  style={{
                    ...authStyles.input,
                    borderColor: userDetails.name ? '#10b981' : '#e5e7eb',
                  }}
                  required
                />
              </div>
            </div>
          )}

          {/* Password Field */}
          <div style={authStyles.inputGroup}>
            <label style={authStyles.label}>Password</label>
            <div style={authStyles.inputWrapper}>
              <Lock size={20} style={authStyles.inputIcon} />
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                value={userDetails.password}
                onChange={handleInputChange}
                placeholder="Enter your password"
                style={{
                  ...authStyles.input,
                  borderColor: userDetails.password ? '#10b981' : '#e5e7eb',
                }}
                required
              />
              <button
                type="button"
                style={authStyles.eyeIcon}
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>

            {!isLogin && userDetails.password && (
              <>
                <div
                  style={{
                    ...authStyles.strengthIndicator,
                    background: getStrengthColor(passwordStrength),
                    width: `${passwordStrength}%`,
                  }}
                />
                <div style={authStyles.helpText}>
                  Password strength: {getStrengthText(passwordStrength)}
                </div>
              </>
            )}
          </div>

          {/* Confirm Password Field (Signup only) */}
          {!isLogin && (
            <div style={authStyles.inputGroup}>
              <label style={authStyles.label}>Confirm Password</label>
              <div style={authStyles.inputWrapper}>
                <Lock size={20} style={authStyles.inputIcon} />
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  name="confirmPassword"
                  value={userDetails.confirmPassword}
                  onChange={handleInputChange}
                  placeholder="Confirm your password"
                  style={{
                    ...authStyles.input,
                    borderColor: userDetails.confirmPassword && userDetails.password === userDetails.confirmPassword
                      ? '#10b981'
                      : userDetails.confirmPassword
                        ? '#ef4444'
                        : '#e5e7eb',
                  }}
                  required
                />
                <button
                  type="button"
                  style={authStyles.eyeIcon}
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
              {userDetails.confirmPassword && userDetails.password !== userDetails.confirmPassword && (
                <div style={{ ...authStyles.helpText, color: '#ef4444' }}>
                  Passwords do not match
                </div>
              )}
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            style={{
              ...authStyles.button,
              opacity: isLoading ? 0.7 : 1,
              cursor: isLoading ? 'not-allowed' : 'pointer',
            }}
            onMouseEnter={(e) => {
              if (!isLoading) {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.3)';
              }
            }}
            onMouseLeave={(e) => {
              if (!isLoading) {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }
            }}
          >
            {isLoading ? (
              <>
                <Loader2 size={20} className="animate-spin" />
                {isLogin ? "Signing in..." : "Creating account..."}
              </>
            ) : (
              <>
                {isLogin ? "Sign in" : "Create account"}
                <ArrowRight size={20} />
              </>
            )}
          </button>
        </form>

        {/* Switch between login/signup */}
        <div style={authStyles.switchText}>
          {isLogin ? "Don't have an account? " : "Already have an account? "}
          <a
            style={authStyles.switchLink}
            onClick={() => navigate(isLogin ? "/signup" : "/login")}
            onMouseEnter={(e) => {
              e.currentTarget.style.textDecoration = 'underline';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.textDecoration = 'none';
            }}
          >
            {isLogin ? "Sign up" : "Sign in"}
          </a>
        </div>

        {/* Forgot Password Link (Login only) */}
        {isLogin && (
          <div style={{ ...authStyles.switchText, marginTop: '16px' }}>
            <a
              style={authStyles.switchLink}
              onClick={() => navigate("/forgot")}
              onMouseEnter={(e) => {
                e.currentTarget.style.textDecoration = 'underline';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.textDecoration = 'none';
              }}
            >
              Forgot your password?
            </a>
          </div>
        )}
      </div>

      {/* Responsive styles */}
      <style>{`
        @media (max-width: 640px) {
          .auth-card {
            padding: 32px 24px !important;
            margin: 16px !important;
          }
        }
      `}</style>
    </div>
  );
};

export { ModernAuth };
export default ModernAuth;
