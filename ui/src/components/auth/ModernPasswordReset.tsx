/**
 * Modern Password Reset Components
 * 
 * Clean, modern password reset forms that match the design system
 */

import React, { useState, useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  Mail,
  Lock,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Loader2,
  Eye,
  EyeOff,
  ArrowLeft,
  Sparkles
} from "lucide-react";
import api from "../../api";
import { handleApiError, showSuccessMessage } from "../../services/errorHandler";
import colors from "../../styles/colors";

// Modern Password Reset Styles
const resetStyles = {
  container: {
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
    position: 'relative' as const,
    overflow: 'hidden',
    padding: '20px',
  },
  backgroundElements: {
    position: 'absolute' as const,
    top: '-50%',
    right: '-10%',
    width: '400px',
    height: '400px',
    background: 'rgba(255, 255, 255, 0.1)',
    borderRadius: '50%',
    filter: 'blur(80px)',
  },
  backgroundElements2: {
    position: 'absolute' as const,
    bottom: '-30%',
    left: '-5%',
    width: '300px',
    height: '300px',
    background: 'rgba(255, 255, 255, 0.08)',
    borderRadius: '50%',
    filter: 'blur(60px)',
  },
  card: {
    background: colors.WHITE.primary,
    borderRadius: '24px',
    padding: '48px',
    width: '100%',
    maxWidth: '480px',
    boxShadow: '0 32px 80px rgba(0, 0, 0, 0.15)',
    backdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    position: 'relative' as const,
    zIndex: 2,
  },
  header: {
    textAlign: 'center' as const,
    marginBottom: '32px',
  },
  title: {
    fontSize: '32px',
    fontWeight: 700,
    color: '#1a1a1a',
    marginBottom: '8px',
  },
  subtitle: {
    fontSize: '16px',
    color: '#6b7280',
    marginBottom: '0',
  },
  form: {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '24px',
  },
  inputGroup: {
    position: 'relative' as const,
  },
  label: {
    display: 'block',
    fontSize: '14px',
    fontWeight: 600,
    color: '#374151',
    marginBottom: '8px',
  },
  inputWrapper: {
    position: 'relative' as const,
  },
  input: {
    width: '100%',
    padding: '16px 48px 16px 48px',
    border: '2px solid #e5e7eb',
    borderRadius: '12px',
    fontSize: '16px',
    transition: 'all 0.2s ease',
    outline: 'none',
    background: '#fafafa',
    boxSizing: 'border-box' as const,
  },
  inputIcon: {
    position: 'absolute' as const,
    left: '16px',
    top: '50%',
    transform: 'translateY(-50%)',
    color: '#9ca3af',
    pointerEvents: 'none' as const,
  },
  button: {
    width: '100%',
    padding: '16px',
    background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
    color: colors.WHITE.primary,
    border: 'none',
    borderRadius: '12px',
    fontSize: '16px',
    fontWeight: 600,
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
    marginTop: '8px',
  },
  backButton: {
    background: 'transparent',
    color: '#6b7280',
    border: 'none',
    padding: '8px',
    borderRadius: '8px',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    fontSize: '14px',
    marginBottom: '16px',
    transition: 'all 0.2s ease',
  },
  alert: {
    padding: '12px 16px',
    borderRadius: '8px',
    fontSize: '14px',
    marginBottom: '16px',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  },
  errorAlert: {
    background: '#fef2f2',
    color: '#dc2626',
    border: '1px solid #fecaca',
  },
  successAlert: {
    background: '#f0fdf4',
    color: '#16a34a',
    border: '1px solid #bbf7d0',
  },
  linkText: {
    color: '#667eea',
    textDecoration: 'none',
    fontWeight: 600,
    cursor: 'pointer',
  },
};

// Forgot Password Component
const ModernForgotPassword: React.FC = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      setErrorMessage("Email is required.");
      return;
    }

    if (!email.includes("@")) {
      setErrorMessage("Please enter a valid email address.");
      return;
    }

    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");

    try {
      const { data } = await api.post(`/users/password-reset`, { email });
      setSuccessMessage("Check your inbox for the reset link! We've sent you an email with instructions.");

      // Try to show success message, but don't fail if it doesn't work
      try {
        showSuccessMessage("Check your inbox for the reset link ✉️");
      } catch (toastError) {
        console.warn("Could not show toast message:", toastError);
      }

      // Navigate to reset page if token is provided
      if (data?.token && data?.uid) {
        setTimeout(() => navigate(`/reset/${data.token}/${data.uid}`), 2000);
      }
    } catch (err) {
      console.error("Password reset error:", err);
      setErrorMessage("Failed to send reset email. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [email, navigate]);

  return (
    <div style={resetStyles.container}>
      {/* Background Elements */}
      <div style={resetStyles.backgroundElements} />
      <div style={resetStyles.backgroundElements2} />

      <div style={resetStyles.card}>
        <button
          style={resetStyles.backButton}
          onClick={() => navigate("/login")}
        >
          <ArrowLeft size={16} />
          Back to login
        </button>

        <div style={resetStyles.header}>
          {/* Brand Icon */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            marginBottom: '24px'
          }}>
            <div style={{
              padding: '16px',
              borderRadius: '20px',
              background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
              color: colors.WHITE.primary,
              boxShadow: '0 8px 32px rgba(67, 0, 255, 0.2)'
            }}>
              <Sparkles size={32} />
            </div>
          </div>

          <h1 style={resetStyles.title}>Forgot password?</h1>
          <p style={resetStyles.subtitle}>
            No worries! Enter your email and we'll send you a reset link.
          </p>
        </div>

        {errorMessage && (
          <div style={{ ...resetStyles.alert, ...resetStyles.errorAlert }}>
            <AlertCircle size={16} />
            {errorMessage}
          </div>
        )}

        {successMessage && (
          <div style={{ ...resetStyles.alert, ...resetStyles.successAlert }}>
            <CheckCircle size={16} />
            {successMessage}
          </div>
        )}

        <form style={resetStyles.form} onSubmit={handleSubmit}>
          <div style={resetStyles.inputGroup}>
            <label style={resetStyles.label}>Email address</label>
            <div style={resetStyles.inputWrapper}>
              <Mail size={20} style={resetStyles.inputIcon} />
              <input
                type="email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  setErrorMessage("");
                }}
                placeholder="Enter your email"
                style={{
                  ...resetStyles.input,
                  borderColor: email ? '#10b981' : '#e5e7eb',
                }}
                required
                autoFocus
                autoComplete="email"
              />
            </div>
          </div>

          <button
            type="submit"
            disabled={isLoading}
            style={{
              ...resetStyles.button,
              opacity: isLoading ? 0.7 : 1,
              cursor: isLoading ? 'not-allowed' : 'pointer',
            }}
          >
            {isLoading ? (
              <>
                <Loader2 size={20} style={{ animation: 'spin 1s linear infinite' }} />
                Sending reset link...
              </>
            ) : (
              <>
                Send reset link
                <ArrowRight size={20} />
              </>
            )}
          </button>
        </form>

        <div style={{ textAlign: 'center', marginTop: '24px', fontSize: '14px', color: '#6b7280' }}>
          Remember your password?{' '}
          <a
            style={resetStyles.linkText}
            onClick={() => navigate("/login")}
          >
            Sign in
          </a>
        </div>
      </div>

      <style>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

// Reset Password Component
const ModernResetPassword: React.FC = () => {
  const navigate = useNavigate();
  const { token, uid } = useParams();
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  // Password strength calculation
  const getPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;
    if (/[^A-Za-z0-9]/.test(password)) strength += 25;
    return strength;
  };

  const passwordStrength = getPasswordStrength(password);

  const getStrengthColor = (strength: number) => {
    if (strength < 25) return '#ef4444';
    if (strength < 50) return '#f59e0b';
    if (strength < 75) return '#eab308';
    return '#10b981';
  };

  const getStrengthText = (strength: number) => {
    if (strength < 25) return 'Weak';
    if (strength < 50) return 'Fair';
    if (strength < 75) return 'Good';
    return 'Strong';
  };

  const validateForm = () => {
    if (!password) {
      setErrorMessage("Password is required.");
      return false;
    }

    if (password.length < 8) {
      setErrorMessage("Password must be at least 8 characters long.");
      return false;
    }

    if (!confirmPassword) {
      setErrorMessage("Please confirm your password.");
      return false;
    }

    if (password !== confirmPassword) {
      setErrorMessage("Passwords do not match.");
      return false;
    }

    return true;
  };

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");

    try {
      await api.post(`/users/password-reset/confirm`, {
        token,
        password,
        uid,
      });

      setSuccessMessage("Password reset successful! Redirecting to login...");

      // Try to show success message, but don't fail if it doesn't work
      try {
        showSuccessMessage("Password updated successfully ✔️");
      } catch (toastError) {
        console.warn("Could not show toast message:", toastError);
      }

      setTimeout(() => navigate("/login"), 2000);
    } catch (err) {
      console.error("Password reset failed:", err);
      setErrorMessage("Failed to reset password. The link may be expired or invalid.");
    } finally {
      setIsLoading(false);
    }
  }, [password, confirmPassword, token, uid, navigate]);

  return (
    <div style={resetStyles.container}>
      <div style={resetStyles.card}>
        <button
          style={resetStyles.backButton}
          onClick={() => navigate("/forgot")}
        >
          <ArrowLeft size={16} />
          Back to forgot password
        </button>

        <div style={resetStyles.header}>
          <h1 style={resetStyles.title}>Reset password</h1>
          <p style={resetStyles.subtitle}>
            Enter your new password below to complete the reset process.
          </p>
        </div>

        {errorMessage && (
          <div style={{ ...resetStyles.alert, ...resetStyles.errorAlert }}>
            <AlertCircle size={16} />
            {errorMessage}
          </div>
        )}

        {successMessage && (
          <div style={{ ...resetStyles.alert, ...resetStyles.successAlert }}>
            <CheckCircle size={16} />
            {successMessage}
          </div>
        )}

        <form style={resetStyles.form} onSubmit={handleSubmit}>
          {/* New Password Field */}
          <div style={resetStyles.inputGroup}>
            <label style={resetStyles.label}>New Password</label>
            <div style={resetStyles.inputWrapper}>
              <Lock size={20} style={resetStyles.inputIcon} />
              <input
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  setErrorMessage("");
                }}
                placeholder="Enter your new password"
                style={{
                  ...resetStyles.input,
                  borderColor: password ? '#10b981' : '#e5e7eb',
                }}
                required
                autoFocus
                autoComplete="new-password"
              />
              <button
                type="button"
                style={{
                  position: 'absolute',
                  right: '16px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  color: '#9ca3af',
                  cursor: 'pointer',
                  background: 'none',
                  border: 'none',
                }}
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>

            {password && (
              <>
                <div
                  style={{
                    height: '4px',
                    borderRadius: '2px',
                    marginTop: '8px',
                    transition: 'all 0.3s ease',
                    background: getStrengthColor(passwordStrength),
                    width: `${passwordStrength}%`,
                  }}
                />
                <div style={{
                  fontSize: '12px',
                  color: '#6b7280',
                  marginTop: '4px',
                }}>
                  Password strength: {getStrengthText(passwordStrength)}
                </div>
              </>
            )}
          </div>

          {/* Confirm Password Field */}
          <div style={resetStyles.inputGroup}>
            <label style={resetStyles.label}>Confirm Password</label>
            <div style={resetStyles.inputWrapper}>
              <Lock size={20} style={resetStyles.inputIcon} />
              <input
                type={showConfirmPassword ? "text" : "password"}
                value={confirmPassword}
                onChange={(e) => {
                  setConfirmPassword(e.target.value);
                  setErrorMessage("");
                }}
                placeholder="Confirm your new password"
                style={{
                  ...resetStyles.input,
                  borderColor: confirmPassword && password === confirmPassword
                    ? '#10b981'
                    : confirmPassword
                      ? '#ef4444'
                      : '#e5e7eb',
                }}
                required
                autoComplete="new-password"
              />
              <button
                type="button"
                style={{
                  position: 'absolute',
                  right: '16px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  color: '#9ca3af',
                  cursor: 'pointer',
                  background: 'none',
                  border: 'none',
                }}
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
            {confirmPassword && password !== confirmPassword && (
              <div style={{
                fontSize: '12px',
                color: '#ef4444',
                marginTop: '4px',
              }}>
                Passwords do not match
              </div>
            )}
          </div>

          <button
            type="submit"
            disabled={isLoading}
            style={{
              ...resetStyles.button,
              opacity: isLoading ? 0.7 : 1,
              cursor: isLoading ? 'not-allowed' : 'pointer',
            }}
          >
            {isLoading ? (
              <>
                <Loader2 size={20} style={{ animation: 'spin 1s linear infinite' }} />
                Resetting password...
              </>
            ) : (
              <>
                Reset password
                <CheckCircle size={20} />
              </>
            )}
          </button>
        </form>

        <div style={{ textAlign: 'center', marginTop: '24px', fontSize: '14px', color: '#6b7280' }}>
          Remember your password?{' '}
          <a
            style={resetStyles.linkText}
            onClick={() => navigate("/login")}
          >
            Sign in
          </a>
        </div>
      </div>

      <style>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export { ModernForgotPassword, ModernResetPassword };
export default ModernForgotPassword;
