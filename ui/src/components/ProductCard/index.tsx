/**
 * Unified Product Card Component
 * 
 * Consistent product card design used across all recommendation views
 * with proper default image handling and responsive design.
 */

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, Badge } from 'react-bootstrap';
import { ShoppingCart, Star, ExternalLink, Package } from 'lucide-react';
import colors from '../../styles/colors';
import { buildImageSrc } from '../../util/functions';
import {
  ProductCardData,
  BaseProductCardProps,
  PRODUCT_CARD_VARIANTS,
  DEFAULT_PRODUCT_CARD_CONFIG
} from './types';

// Default product images
import defaultImage1 from '../../assets/images/default_product_image_1.jpg';
import defaultImage2 from '../../assets/images/default_product_image_2.jpg';
import defaultImage3 from '../../assets/images/default_product_image_3.jpg';
import defaultImage4 from '../../assets/images/default_product_image_4.jpg';
import defaultImage5 from '../../assets/images/default_product_image_5.jpg';
import defaultImage6 from '../../assets/images/default_product_image_6.jpg';
import defaultImage7 from '../../assets/images/default_product_image_7.jpg';
import defaultImage8 from '../../assets/images/default_product_image_8.jpg';

const DEFAULT_IMAGES = [
  defaultImage1,
  defaultImage2,
  defaultImage3,
  defaultImage4,
  defaultImage5,
  defaultImage6,
  defaultImage7,
  defaultImage8,
];

// Types imported above

// Re-export for backward compatibility
export type { ProductCardData } from './types';

interface ProductCardProps extends BaseProductCardProps<ProductCardData> {
  // Additional props specific to this implementation can be added here
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onSelect,
  onBuyClick,
  apiRoot = process.env.REACT_APP_BACKEND_API || "http://localhost:8000/api",
  variant = 'default',
  showBuyButton = true,
  showRating = true,
  showPrice = true,
  showTags = false,
  className = ''
}) => {
  const [imageSrc, setImageSrc] = useState<string>(() => {
    if (product.image_url) {
      return buildImageSrc(apiRoot, product.image_url);
    }
    // Use a consistent default image based on product ID
    return DEFAULT_IMAGES[product.id % DEFAULT_IMAGES.length];
  });

  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    if (!imageError) {
      setImageError(true);
      // Use a random default image on error
      const randomIndex = Math.floor(Math.random() * DEFAULT_IMAGES.length);
      setImageSrc(DEFAULT_IMAGES[randomIndex]);
    }
  };

  const handleCardClick = () => {
    if (onSelect) {
      onSelect(product);
    } else if (product.link) {
      window.open(product.link, '_blank');
    }
  };

  const handleBuyClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onBuyClick) {
      onBuyClick(product);
    } else if (product.link) {
      window.open(product.link, '_blank');
    }
  };

  const getCardHeight = () => {
    switch (variant) {
      case 'compact':
        return '300px';
      case 'detailed':
        return '450px';
      default:
        return '380px';
    }
  };

  const getImageHeight = () => {
    switch (variant) {
      case 'compact':
        return '150px';
      case 'detailed':
        return '220px';
      default:
        return '180px';
    }
  };

  return (
    <Card
      className={`h-100 border-0 shadow-sm product-card ${className}`}
      style={{
        borderRadius: '16px',
        transition: 'all 0.3s ease',
        overflow: 'hidden',
        cursor: onSelect || product.link ? 'pointer' : 'default',
        height: getCardHeight()
      }}
      onClick={handleCardClick}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-4px)';
        e.currentTarget.style.boxShadow = '0 12px 32px rgba(0, 0, 0, 0.15)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.1)';
      }}
    >
      {/* Product Image */}
      <div className="position-relative">
        <div
          className="text-center p-3"
          style={{
            background: '#F8FAFC',
            height: getImageHeight()
          }}
        >
          {imageSrc ? (
            <Card.Img
              variant="top"
              src={imageSrc}
              alt={product.name}
              onError={handleImageError}
              style={{
                height: '100%',
                objectFit: 'contain',
                transition: 'transform 0.3s ease',
                maxWidth: '100%'
              }}
            />
          ) : (
            <div
              className="d-flex align-items-center justify-content-center h-100"
              style={{
                background: colors.PRIMARY.pastel,
                color: colors.PRIMARY.primary,
                borderRadius: '8px'
              }}
            >
              <div className="text-center">
                <Package size={32} className="mb-2" />
                <p className="mb-0 small">No Image</p>
              </div>
            </div>
          )}
        </div>

        {/* Rating Badge */}
        {showRating && product.rating && (
          <Badge
            bg="warning"
            text="dark"
            className="position-absolute top-0 end-0 m-2 px-2 py-1"
            style={{ borderRadius: '8px', fontSize: '0.75rem' }}
          >
            <Star size={12} className="me-1" fill="currentColor" />
            {product.rating}
          </Badge>
        )}
      </div>

      {/* Card Body */}
      <Card.Body className="p-3 d-flex flex-column">
        <div className="flex-grow-1">
          <Card.Title
            className="fs-6 fw-bold mb-2 text-truncate"
            style={{ color: '#1F2937' }}
            title={product.name}
          >
            {product.name}
          </Card.Title>

          {/* Tags */}
          {showTags && (product.hairtype || product.category || product.porosity) && (
            <div className="mb-2">
              {product.hairtype && (
                <Badge bg="secondary" className="me-1 mb-1" style={{ fontSize: '0.7rem' }}>
                  {product.hairtype}
                </Badge>
              )}
              {product.category && (
                <Badge bg="info" className="me-1 mb-1" style={{ fontSize: '0.7rem' }}>
                  {product.category}
                </Badge>
              )}
              {product.porosity && (
                <Badge bg="success" className="me-1 mb-1" style={{ fontSize: '0.7rem' }}>
                  {product.porosity}
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Price and Buy Button */}
        <div className="mt-auto">
          {showPrice && product.price && (
            <div className="mb-2">
              <span
                className="fs-6 fw-bold"
                style={{ color: colors.PRIMARY.primary }}
              >
                £{product.price}
              </span>
            </div>
          )}

          {showBuyButton && (
            <Button
              variant="primary"
              size="sm"
              className="w-100 rounded-pill border-0"
              style={{
                background: colors.PRIMARY.primary,
                boxShadow: '0 2px 8px rgba(67, 0, 255, 0.2)',
                transition: 'all 0.2s ease',
                fontSize: '0.875rem'
              }}
              onClick={handleBuyClick}
              onMouseEnter={(e) => {
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(67, 0, 255, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.boxShadow = '0 2px 8px rgba(67, 0, 255, 0.2)';
              }}
            >
              <ShoppingCart size={14} className="me-2" />
              Buy Now
            </Button>
          )}
        </div>
      </Card.Body>
    </Card>
  );
};

export default ProductCard;
