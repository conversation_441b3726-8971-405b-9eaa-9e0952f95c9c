/**
 * Type utilities for ProductCard component
 * 
 * This file contains type definitions and utility functions
 * for working with product data across different components.
 */

export interface ProductCardData {
  id: number;
  name: string;
  price?: string;
  rating?: string;
  image_url?: string | null;
  link?: string;
  hairtype?: string;
  category?: string;
  porosity?: string;
}

/**
 * Type guard to check if an object is a valid ProductCardData
 */
export function isProductCardData(obj: any): obj is ProductCardData {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'number' &&
    typeof obj.name === 'string'
  );
}

/**
 * Safely converts any product-like object to ProductCardData
 */
export function toProductCardData(product: any): ProductCardData {
  if (!isProductCardData(product)) {
    throw new Error('Invalid product data provided');
  }
  
  return {
    id: product.id,
    name: product.name,
    price: product.price || undefined,
    rating: product.rating || undefined,
    image_url: product.image_url || null,
    link: product.link || undefined,
    hairtype: product.hairtype || undefined,
    category: product.category || undefined,
    porosity: product.porosity || undefined,
  };
}

/**
 * Creates a ProductCardData object with default values
 */
export function createProductCardData(
  id: number,
  name: string,
  overrides: Partial<ProductCardData> = {}
): ProductCardData {
  return {
    id,
    name,
    price: undefined,
    rating: undefined,
    image_url: null,
    link: undefined,
    hairtype: undefined,
    category: undefined,
    porosity: undefined,
    ...overrides,
  };
}

/**
 * Type for product selection callbacks
 */
export type ProductSelectCallback<T = ProductCardData> = (product: T) => void;

/**
 * Type for product buy callbacks
 */
export type ProductBuyCallback<T = ProductCardData> = (product: T) => void;

/**
 * Generic product card props that can be extended by specific implementations
 */
export interface BaseProductCardProps<T = ProductCardData> {
  product: T;
  onSelect?: ProductSelectCallback<T>;
  onBuyClick?: ProductBuyCallback<T>;
  apiRoot?: string;
  variant?: 'default' | 'compact' | 'detailed';
  showBuyButton?: boolean;
  showRating?: boolean;
  showPrice?: boolean;
  showTags?: boolean;
  className?: string;
}

/**
 * Utility function to safely cast product data for callbacks
 */
export function createSafeCallback<TSource, TTarget>(
  callback: ((product: TTarget) => void) | undefined,
  converter?: (source: TSource) => TTarget
): ((product: TSource) => void) | undefined {
  if (!callback) return undefined;
  
  return (product: TSource) => {
    const convertedProduct = converter ? converter(product) : (product as unknown as TTarget);
    callback(convertedProduct);
  };
}

/**
 * Common product data validation
 */
export function validateProductData(product: any): string[] {
  const errors: string[] = [];
  
  if (!product) {
    errors.push('Product data is required');
    return errors;
  }
  
  if (typeof product.id !== 'number') {
    errors.push('Product ID must be a number');
  }
  
  if (!product.name || typeof product.name !== 'string') {
    errors.push('Product name is required and must be a string');
  }
  
  if (product.price && typeof product.price !== 'string') {
    errors.push('Product price must be a string');
  }
  
  if (product.rating && typeof product.rating !== 'string') {
    errors.push('Product rating must be a string');
  }
  
  if (product.image_url && typeof product.image_url !== 'string') {
    errors.push('Product image URL must be a string');
  }
  
  return errors;
}

/**
 * Default product card configuration
 */
export const DEFAULT_PRODUCT_CARD_CONFIG = {
  variant: 'default' as const,
  showBuyButton: true,
  showRating: true,
  showPrice: true,
  showTags: false,
};

/**
 * Product card variant configurations
 */
export const PRODUCT_CARD_VARIANTS = {
  compact: {
    height: '300px',
    imageHeight: '150px',
    showTags: false,
  },
  default: {
    height: '380px',
    imageHeight: '180px',
    showTags: false,
  },
  detailed: {
    height: '450px',
    imageHeight: '220px',
    showTags: true,
  },
} as const;

/**
 * Type for product card variant keys
 */
export type ProductCardVariant = keyof typeof PRODUCT_CARD_VARIANTS;
