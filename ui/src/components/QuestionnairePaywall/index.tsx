import React from 'react';
import { <PERSON>, Button } from 'react-bootstrap';
import {
  Lock,
  CheckCircle,
  ArrowRight,
  Target,
  Sparkles,
  TrendingUp,
  Award
} from 'lucide-react';
import colors from '../../styles/colors';

interface QuestionnairePaywallProps {
  onStartQuestionnaire: () => void;
  completedQuestions?: number;
  totalQuestions?: number;
  status?: 'NotStarted' | 'Started' | 'Completed';
}

const QuestionnairePaywall: React.FC<QuestionnairePaywallProps> = ({
  onStartQuestionnaire,
  completedQuestions = 0,
  totalQuestions = 0,
  status = 'NotStarted'
}) => {
  const progressPercentage = totalQuestions > 0 ? (completedQuestions / totalQuestions) * 100 : 0;

  const getStatusConfig = () => {
    switch (status) {
      case 'Started':
        return {
          title: 'Complete Your Hair Assessment',
          subtitle: 'You\'re almost there! Finish your questionnaire to unlock your personalized dashboard.',
          buttonText: 'Continue Assessment',
          buttonIcon: <ArrowRight size={20} />,
          iconColor: colors.SECONDARY.primary,
          iconBg: colors.SECONDARY.pastel,
        };
      case 'Completed':
        return {
          title: 'Assessment Complete!',
          subtitle: 'Your hair analysis is ready. Access your personalized dashboard now.',
          buttonText: 'View Dashboard',
          buttonIcon: <CheckCircle size={20} />,
          iconColor: colors.GREEN.primary,
          iconBg: colors.GREEN.pastel,
        };
      default:
        // Handle case where status might be undefined or 'NotStarted'
        return {
          title: 'Unlock Your Hair Dashboard',
          subtitle: 'Complete our comprehensive hair assessment to access personalized insights and recommendations.',
          buttonText: 'Start Assessment',
          buttonIcon: <Sparkles size={20} />,
          iconColor: colors.PRIMARY.primary,
          iconBg: colors.PRIMARY.pastel,
        };
    }
  };

  const statusConfig = getStatusConfig();

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        backdropFilter: 'blur(8px)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000,
        padding: '20px'
      }}
    >
      <Card
        className="border-0 shadow-lg"
        style={{
          borderRadius: '24px',
          maxWidth: '600px',
          width: '100%',
          background: colors.WHITE.primary,
          overflow: 'hidden'
        }}
      >
        {/* Header with gradient */}
        <div
          style={{
            background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
            padding: '2rem',
            textAlign: 'center',
            color: colors.WHITE.primary
          }}
        >
          <div
            className="p-3 rounded-4 d-inline-flex mb-3"
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              backdropFilter: 'blur(10px)'
            }}
          >
            <Lock size={32} />
          </div>
          <h2 className="fs-3 fw-bold mb-2">{statusConfig.title}</h2>
          <p className="fs-5 mb-0 opacity-90">{statusConfig.subtitle}</p>
        </div>

        <Card.Body className="p-4">
          {/* Progress Section (only show if started and we have progress data) */}
          {status === 'Started' && totalQuestions > 0 && (
            <div className="mb-4 p-3 rounded-3" style={{ backgroundColor: '#F8FAFC' }}>
              <div className="d-flex justify-content-between align-items-center mb-2">
                <span className="fw-semibold" style={{ color: '#1F2937' }}>Progress</span>
                <span className="fw-bold" style={{ color: colors.SECONDARY.primary }}>
                  {completedQuestions}/{totalQuestions} questions
                </span>
              </div>
              <div className="progress" style={{ height: '8px', borderRadius: '4px' }}>
                <div
                  className="progress-bar"
                  style={{
                    width: `${progressPercentage}%`,
                    backgroundColor: colors.SECONDARY.primary,
                    borderRadius: '4px'
                  }}
                />
              </div>
              <p className="fs-7 text-muted mt-2 mb-0">
                {Math.round(progressPercentage)}% complete
              </p>
            </div>
          )}

          {/* Show message if started but no progress data */}
          {status === 'Started' && totalQuestions === 0 && (
            <div className="mb-4 p-3 rounded-3" style={{ backgroundColor: colors.SECONDARY.pastel }}>
              <div className="text-center">
                <div className="p-2 rounded-3 d-inline-flex mb-2" style={{
                  backgroundColor: colors.WHITE.primary,
                  color: colors.SECONDARY.primary
                }}>
                  <ArrowRight size={20} />
                </div>
                <p className="fw-semibold mb-1" style={{ color: colors.SECONDARY.primary }}>
                  Continue Your Assessment
                </p>
                <p className="fs-7 text-muted mb-0">
                  You have a questionnaire in progress. Continue where you left off.
                </p>
              </div>
            </div>
          )}

          {/* Benefits Section */}
          <div className="mb-4">
            <h5 className="fw-bold mb-3" style={{ color: '#1F2937' }}>
              What you'll unlock:
            </h5>
            <div className="row g-3">
              <div className="col-md-6">
                <div className="d-flex align-items-start">
                  <div
                    className="p-2 rounded-3 me-3 flex-shrink-0"
                    style={{
                      backgroundColor: colors.PRIMARY.pastel,
                      color: colors.PRIMARY.primary
                    }}
                  >
                    <Target size={18} />
                  </div>
                  <div>
                    <h6 className="fw-semibold mb-1" style={{ color: '#1F2937' }}>
                      Personalized Hair Profile
                    </h6>
                    <p className="fs-7 text-muted mb-0">
                      Detailed analysis of your unique hair characteristics
                    </p>
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <div className="d-flex align-items-start">
                  <div
                    className="p-2 rounded-3 me-3 flex-shrink-0"
                    style={{
                      backgroundColor: colors.SECONDARY.pastel,
                      color: colors.SECONDARY.primary
                    }}
                  >
                    <TrendingUp size={18} />
                  </div>
                  <div>
                    <h6 className="fw-semibold mb-1" style={{ color: '#1F2937' }}>
                      Health Scores & Metrics
                    </h6>
                    <p className="fs-7 text-muted mb-0">
                      Comprehensive scalp and hair health assessment
                    </p>
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <div className="d-flex align-items-start">
                  <div
                    className="p-2 rounded-3 me-3 flex-shrink-0"
                    style={{
                      backgroundColor: colors.GREEN.pastel,
                      color: colors.GREEN.primary
                    }}
                  >
                    <Sparkles size={18} />
                  </div>
                  <div>
                    <h6 className="fw-semibold mb-1" style={{ color: '#1F2937' }}>
                      AI-Powered Recommendations
                    </h6>
                    <p className="fs-7 text-muted mb-0">
                      Custom product and care routine suggestions
                    </p>
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <div className="d-flex align-items-start">
                  <div
                    className="p-2 rounded-3 me-3 flex-shrink-0"
                    style={{
                      backgroundColor: '#FEF3E2',
                      color: '#F59E0B'
                    }}
                  >
                    <Award size={18} />
                  </div>
                  <div>
                    <h6 className="fw-semibold mb-1" style={{ color: '#1F2937' }}>
                      Progress Tracking
                    </h6>
                    <p className="fs-7 text-muted mb-0">
                      Monitor your hair health journey over time
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center">
            <Button
              onClick={onStartQuestionnaire}
              className="px-5 py-3 border-0 shadow-sm"
              style={{
                background: `linear-gradient(135deg, ${statusConfig.iconColor} 0%, ${colors.SECONDARY.primary} 100%)`,
                borderRadius: '16px',
                fontSize: '16px',
                fontWeight: '600',
                color: colors.WHITE.primary,
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = `0 8px 25px rgba(67, 0, 255, 0.3)`;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              {statusConfig.buttonIcon}
              <span className="ms-2">{statusConfig.buttonText}</span>
            </Button>

            <p className="fs-7 text-muted mt-3 mb-0">
              Takes only 5-7 minutes • Completely personalized • Science-backed insights
            </p>
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default QuestionnairePaywall;
