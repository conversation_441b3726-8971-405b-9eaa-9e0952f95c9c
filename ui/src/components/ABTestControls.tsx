import React from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON>, Card } from 'react-bootstrap';
import { clearABTestData } from '../hooks/useABTest';

const ControlsContainer = styled.div`
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
  
  @media (max-width: 768px) {
    bottom: 10px;
    right: 10px;
  }
`;

const ControlCard = styled(Card)`
  width: 280px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: none;
  
  .card-header {
    background: #1F2937;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
`;

const ControlButton = styled(Button)`
  width: 100%;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

/**
 * Development controls for A/B testing
 * Only shows in development mode
 */
const ABTestControls: React.FC = () => {
  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const handleForceVariant = (variant: 'A' | 'B') => {
    localStorage.setItem('ab_test_landing_page_redesign', variant);
    window.location.reload();
  };

  const handleClearData = () => {
    clearABTestData('landing_page_redesign');
    alert('A/B test data cleared!');
  };

  const handleViewDashboard = () => {
    window.open('/ab-test-dashboard', '_blank');
  };

  const currentVariant = localStorage.getItem('ab_test_landing_page_redesign');

  return (
    <ControlsContainer>
      <ControlCard>
        <Card.Header>
          🧪 A/B Test Controls
        </Card.Header>
        <Card.Body>
          <div style={{ marginBottom: '1rem', fontSize: '0.75rem', color: '#6B7280' }}>
            Current: <strong>{currentVariant || 'Not set'}</strong>
          </div>
          
          <ControlButton 
            variant="outline-primary" 
            size="sm"
            onClick={() => handleForceVariant('A')}
          >
            Force Variant A (Current)
          </ControlButton>
          
          <ControlButton 
            variant="outline-success" 
            size="sm"
            onClick={() => handleForceVariant('B')}
          >
            Force Variant B (Editorial)
          </ControlButton>
          
          <ControlButton 
            variant="outline-info" 
            size="sm"
            onClick={handleViewDashboard}
          >
            View Dashboard
          </ControlButton>
          
          <ControlButton 
            variant="outline-danger" 
            size="sm"
            onClick={handleClearData}
          >
            Clear Test Data
          </ControlButton>
        </Card.Body>
      </ControlCard>
    </ControlsContainer>
  );
};

export default ABTestControls;
