import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { BarChart3, X } from "lucide-react";
import ScoresDashboard from "../ScoresDashboard";
import colors from "../../styles/colors";

// Add CSS for high z-index modal
const modalStyles = `
  .modal-backdrop-high-z {
    z-index: 9998 !important;
  }
  .modern-modal {
    z-index: 9999 !important;
  }
  .modern-modal .modal-content {
    z-index: 10000 !important;
  }
`;

/* ------------------------------------------------------------------ */
/*  Props                                                              */
/* ------------------------------------------------------------------ */

/**
 * Props for ScoresModal
 * @param sessionGuid – GUID the <Scores /> component requires
 * @param buttonLabel – text that appears on the trigger button (default: "View Scores")
 * @param variant – Bootstrap button variant (default: "primary")
 */
interface Props {
  sessionGuid: string;
  userId: string;
  buttonLabel?: string;
  variant?: string;
}

/* ------------------------------------------------------------------ */
/*  Component                                                          */
/* ------------------------------------------------------------------ */

const ScoresModal: React.FC<Props> = ({
  sessionGuid,
  userId,
  buttonLabel = "View Scores",
  variant = "primary",
}) => {
  const [show, setShow] = useState(false);

  // Inject CSS for high z-index modal
  useEffect(() => {
    const styleElement = document.createElement("style");
    styleElement.innerHTML = modalStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  return (
    <>
      {/* Trigger button */}
      <Button
        variant={variant}
        onClick={() => setShow(true)}
        className="rounded-pill px-3 py-2"
        style={{
          fontSize: '0.875rem',
          fontWeight: '500',
          transition: 'all 0.2s ease'
        }}
        onMouseEnter={(e) => {
          if (variant === 'outline-primary') {
            e.currentTarget.style.transform = 'translateY(-1px)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(67, 0, 255, 0.2)';
          }
        }}
        onMouseLeave={(e) => {
          if (variant === 'outline-primary') {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = 'none';
          }
        }}
      >
        <BarChart3 className="me-2" size={16} />
        {buttonLabel}
      </Button>

      {/* Modern Modal */}
      <Modal
        show={show}
        onHide={() => setShow(false)}
        centered
        size="xl"
        backdrop="static"
        dialogClassName="modern-modal"
        style={{ zIndex: 9999 }}
        backdropClassName="modal-backdrop-high-z"
      >
        <div style={{ borderRadius: '20px', overflow: 'hidden' }}>
          <Modal.Header
            className="border-0 pb-0"
            style={{
              background: `linear-gradient(135deg, ${colors.PRIMARY.primary} 0%, ${colors.SECONDARY.primary} 100%)`,
              color: 'white',
              padding: '2rem 2rem 1rem 2rem'
            }}
          >
            <div className="d-flex align-items-center">
              <div className="p-2 rounded-3 me-3" style={{
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                backdropFilter: 'blur(10px)'
              }}>
                <BarChart3 size={24} style={{ color: 'white' }} />
              </div>
              <div>
                <Modal.Title className="fs-4 fw-bold mb-1">Hair Health Report</Modal.Title>
                <p className="mb-0 opacity-90 small">Detailed analysis of your hair diagnostic results</p>
              </div>
            </div>
            <Button
              variant="link"
              onClick={() => setShow(false)}
              className="p-2 text-white"
              style={{
                border: 'none',
                background: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '12px',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
              }}
            >
              <X size={20} />
            </Button>
          </Modal.Header>

          <Modal.Body className="p-4" style={{ backgroundColor: '#FAFBFC' }}>
            <ScoresDashboard sessionGuid={sessionGuid} userId={userId} />
          </Modal.Body>
        </div>
      </Modal>
    </>
  );
};

export default ScoresModal;
