#!/usr/bin/env python3
"""
Quiz Automation Test Script

This script automates the completion of the hair analysis quiz by:
1. Logging in with user credentials
2. Fetching all quiz questions
3. Automatically selecting random answers for multiple choice questions
4. Providing dummy text for open-ended questions
5. Uploading dummy hair images for image upload questions (positions 11, 12, 13)

Usage:
    python test_quiz_automation.py --email <EMAIL> --password password123
    python test_quiz_automation.py --interactive  # For interactive mode
"""

import argparse
import json
import random
import sys
import uuid
from pathlib import Path
from typing import Dict, List, Optional

import requests

# API Configuration
API_BASE_URL = "http://localhost:8000/api"
FRONTEND_URL = "http://localhost:3000"

# Dummy responses for different question types
DUMMY_TEXT_RESPONSES = [
    "I have normal hair that gets oily at the roots",
    "My hair is naturally curly and tends to be dry",
    "I use heat styling tools regularly",
    "I color my hair every 6 months",
    "I have sensitive scalp and use gentle products",
    "My hair breaks easily when wet",
    "I live in a humid climate",
    "I exercise regularly and wash my hair daily",
    "I have fine hair that lacks volume",
    "I use deep conditioning treatments weekly",
]


class QuizAutomator:
    """
    Automated quiz completion for testing purposes.

    API Usage Notes:
    - /quiz/questions: Returns questions with database IDs and positions
    - /quiz/answers/questions/{question_id}: Expects database ID
    - /quiz/replies: Expects database ID for question_id
    - /quiz/replies/images: Expects database ID for question_id
    - Image questions identified by position: 11=back, 12=front, 13=side
    """

    def __init__(self, base_url: str = API_BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update(
            {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "Origin": FRONTEND_URL,
                "Referer": f"{FRONTEND_URL}/quiz",
            }
        )
        self.access_token = None
        self.user_id = None
        self.session_guid = str(uuid.uuid4())

    def login(self, email: str, password: str) -> bool:
        """Login and get access token"""
        print(f"🔐 Logging in as {email}...")

        login_data = {"email": email, "password": password}

        try:
            response = self.session.post(
                f"{self.base_url}/users/login", json=login_data
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.access_token = data.get("access")
                    self.user_id = data.get("user", {}).get("id")

                    # Add authorization header for future requests
                    self.session.headers.update(
                        {"Authorization": f"Bearer {self.access_token}"}
                    )

                    print(f"✅ Login successful! User ID: {self.user_id}")
                    return True
                else:
                    print(f"❌ Login failed: {data.get('message', 'Unknown error')}")
                    return False
            else:
                print(f"❌ Login failed with status {response.status_code}")
                print(f"   Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Login error: {str(e)}")
            return False

    def get_questions(self) -> List[Dict]:
        """Fetch all quiz questions with pagination"""
        print("📋 Fetching quiz questions...")

        try:
            # Fetch all questions by setting a high page_size
            response = self.session.get(f"{self.base_url}/quiz/questions?page_size=100")

            if response.status_code == 200:
                questions = response.json()
                print(f"✅ Found {len(questions)} questions")

                # If we got exactly 100, there might be more pages
                if len(questions) == 100:
                    print("⚠️  Warning: There might be more than 100 questions.")
                    print("   Consider increasing page_size.")

                return questions
            else:
                print(f"❌ Failed to fetch questions: {response.status_code}")
                return []

        except Exception as e:
            print(f"❌ Error fetching questions: {str(e)}")
            return []

    def get_answers_for_question(self, question_id: int) -> List[Dict]:
        """Get available answers for a specific question using database ID"""
        try:
            response = self.session.get(
                f"{self.base_url}/quiz/answers/questions/{question_id}"
            )

            if response.status_code == 200:
                return response.json()
            else:
                return []

        except Exception as e:
            print(f"❌ Error fetching answers for question {question_id}: {str(e)}")
            return []

    def submit_text_reply(self, question_id: int, text: str) -> bool:
        """Submit a text reply for a question using database ID"""
        reply_data = {
            "text": text,
            "question_id": question_id,  # This endpoint expects database ID
            "user_id": str(self.user_id),
            "session_guid": self.session_guid,
        }

        try:
            response = self.session.post(
                f"{self.base_url}/quiz/replies", json=reply_data
            )

            if response.status_code == 201:
                return True
            else:
                print(f"❌ Failed to submit reply for question {question_id}")
                print(f"   Status: {response.status_code}, Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error submitting reply for question {question_id}: {str(e)}")
            return False

    def create_dummy_image(self, filename: str) -> Path:
        """Create a dummy image file for testing"""
        # Create a simple 1x1 pixel PNG image
        dummy_image_data = (
            b"\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01"
            b"\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13"
            b"\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8"
            b"\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82"
        )

        image_path = Path(f"/tmp/{filename}")
        with open(image_path, "wb") as f:
            f.write(dummy_image_data)

        return image_path

    def submit_image_reply(self, question_id: int, image_type: str) -> bool:
        """Submit an image reply for a question"""
        print(f"📸 Uploading dummy {image_type} image for question {question_id}...")

        # Create dummy image
        filename = f"dummy_{image_type}_hair_{self.user_id}_{question_id}.png"
        image_path = self.create_dummy_image(filename)

        try:
            # Use requests directly instead of session for file upload
            import requests

            # Prepare form data and files
            data = {
                "question_id": str(question_id),
                "user_id": str(self.user_id),
                "session_guid": self.session_guid,
            }

            with open(image_path, "rb") as f:
                files = {"file": (filename, f, "image/png")}

                # Use basic headers for file upload
                headers = {
                    "Accept": "application/json",
                    "Origin": FRONTEND_URL,
                    "Referer": f"{FRONTEND_URL}/quiz",
                }

                # Add authorization if we have it
                if self.access_token:
                    headers["Authorization"] = f"Bearer {self.access_token}"

                response = requests.post(
                    f"{self.base_url}/quiz/replies/images",
                    data=data,
                    files=files,
                    headers=headers,
                )

            # Clean up dummy image
            image_path.unlink()

            if response.status_code == 201:
                print(f"✅ Successfully uploaded {image_type} image")
                return True
            else:
                print(f"❌ Failed to upload {image_type} image")
                print(f"   Status: {response.status_code}, Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error uploading {image_type} image: {str(e)}")
            # Clean up dummy image on error
            if image_path.exists():
                image_path.unlink()
            return False

    def complete_quiz(self) -> bool:
        """Complete the entire quiz automatically"""
        print(f"🚀 Starting automated quiz completion for user {self.user_id}")
        print(f"📝 Session GUID: {self.session_guid}")

        questions = self.get_questions()
        if not questions:
            print("❌ No questions found!")
            return False

        success_count = 0
        total_questions = len(questions)

        # Image question mappings by position (11=back, 12=front, 13=side)
        # Note: We use position to identify which image type to upload,
        # but still use database ID for API calls as required by endpoints
        image_questions_by_position = {11: "back", 12: "front", 13: "side"}

        for question in questions:
            question_id = question["id"]
            question_type = question["type"]
            position = question["position"]
            title = question["title"]

            print(f"\n📝 Question {position}: {title}")
            print(f"   Type: {question_type}, ID: {question_id}")

            try:
                if question_type == "IU":  # Image Upload
                    if position in image_questions_by_position:
                        image_type = image_questions_by_position[position]
                        if self.submit_image_reply(question_id, image_type):
                            success_count += 1
                    else:
                        print(
                            f"⚠️  Skipping image question at position {position} "
                            f"(not in predefined list)"
                        )

                elif question_type in ["SC", "MC"]:  # Single Choice or Multiple Choice
                    answers = self.get_answers_for_question(question_id)
                    if answers:
                        # Select random answer
                        selected_answer = random.choice(answers)
                        answer_text = selected_answer["title"]

                        print(f"   Selected: {answer_text}")

                        if self.submit_text_reply(question_id, answer_text):
                            success_count += 1
                    else:
                        print(f"⚠️  No answers found for question {question_id}")

                elif question_type == "CR":  # Custom Response (text input)
                    dummy_text = random.choice(DUMMY_TEXT_RESPONSES)
                    print(f"   Response: {dummy_text}")

                    if self.submit_text_reply(question_id, dummy_text):
                        success_count += 1

                else:
                    print(f"⚠️  Unknown question type: {question_type}")

            except Exception as e:
                print(f"❌ Error processing question {question_id}: {str(e)}")

        print("\n🎉 Quiz completion finished!")
        print(f"✅ Successfully answered {success_count}/{total_questions} questions")
        print(f"📊 Success rate: {(success_count / total_questions) * 100:.1f}%")

        return success_count > 0


def main():
    parser = argparse.ArgumentParser(description="Automate quiz completion for testing")
    parser.add_argument(
        "--email", default="<EMAIL>", help="User email for login"
    )
    parser.add_argument(
        "--password", default="Password123!", help="User password for login"
    )
    parser.add_argument("--interactive", action="store_true", help="Interactive mode")
    parser.add_argument("--api-url", default=API_BASE_URL, help="API base URL")

    args = parser.parse_args()

    # Get credentials
    if args.interactive:
        email = input("Enter email: ").strip()
        password = input("Enter password: ").strip()
    elif args.email and args.password:
        email = args.email
        password = args.password
    else:
        print("❌ Please provide email and password via --email and --password,")
        print("   or use --interactive")
        sys.exit(1)

    # Create automator and run
    automator = QuizAutomator(args.api_url)

    if automator.login(email, password):
        success = automator.complete_quiz()
        if success:
            print("\n🎉 Quiz automation completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Quiz automation failed!")
            sys.exit(1)
    else:
        print("\n❌ Login failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
