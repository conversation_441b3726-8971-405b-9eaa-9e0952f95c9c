"""
Monitoring and observability infrastructure for Cosmetrics AI.

This module provides comprehensive monitoring capabilities including
metrics collection, health checks, alerting, and performance monitoring.
"""

import time
from collections.abc import Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from threading import Lock
from typing import Any

from django.conf import settings
from django.core.cache import cache
from django.db import connection
from loguru import logger


class MetricType(Enum):
    """Types of metrics."""

    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


class AlertSeverity(Enum):
    """Alert severity levels."""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class Metric:
    """Metric data structure."""

    name: str
    value: float
    metric_type: MetricType
    timestamp: datetime
    tags: dict[str, str]
    unit: str = ""

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "name": self.name,
            "value": self.value,
            "type": self.metric_type.value,
            "timestamp": self.timestamp.isoformat(),
            "tags": self.tags,
            "unit": self.unit,
        }


@dataclass
class Alert:
    """Alert data structure."""

    alert_id: str
    name: str
    severity: AlertSeverity
    message: str
    timestamp: datetime
    source: str
    resolved: bool = False
    resolved_at: datetime | None = None
    metadata: dict[str, Any] | None = None

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "alert_id": self.alert_id,
            "name": self.name,
            "severity": self.severity.value,
            "message": self.message,
            "timestamp": self.timestamp.isoformat(),
            "source": self.source,
            "resolved": self.resolved,
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
            "metadata": self.metadata or {},
        }


class MetricsCollector:
    """
    Metrics collection and storage system.

    Collects various application metrics for monitoring and alerting.
    """

    def __init__(self):
        self._metrics: list[Metric] = []
        self._lock = Lock()
        self._max_metrics = getattr(settings, "MAX_STORED_METRICS", 10000)

    def record_counter(
        self, name: str, value: float = 1.0, tags: dict[str, str] = None
    ) -> None:
        """Record a counter metric."""
        self._add_metric(name, value, MetricType.COUNTER, tags or {})

    def record_gauge(
        self, name: str, value: float, tags: dict[str, str] = None
    ) -> None:
        """Record a gauge metric."""
        self._add_metric(name, value, MetricType.GAUGE, tags or {})

    def record_timer(
        self, name: str, duration_seconds: float, tags: dict[str, str] = None
    ) -> None:
        """Record a timer metric."""
        self._add_metric(
            name, duration_seconds, MetricType.TIMER, tags or {}, unit="seconds"
        )

    def record_histogram(
        self, name: str, value: float, tags: dict[str, str] = None
    ) -> None:
        """Record a histogram metric."""
        self._add_metric(name, value, MetricType.HISTOGRAM, tags or {})

    def _add_metric(
        self,
        name: str,
        value: float,
        metric_type: MetricType,
        tags: dict[str, str],
        unit: str = "",
    ) -> None:
        """Add a metric to the collection."""
        metric = Metric(
            name=name,
            value=value,
            metric_type=metric_type,
            timestamp=datetime.utcnow(),
            tags=tags,
            unit=unit,
        )

        with self._lock:
            self._metrics.append(metric)

            # Keep only the most recent metrics
            if len(self._metrics) > self._max_metrics:
                self._metrics = self._metrics[-self._max_metrics :]

        logger.debug(f"Recorded metric: {name}={value} ({metric_type.value})")

    def get_metrics(
        self, name_pattern: str = None, since: datetime = None, limit: int = 1000
    ) -> list[Metric]:
        """Get collected metrics with optional filtering."""
        with self._lock:
            metrics = self._metrics.copy()

        # Filter by name pattern
        if name_pattern:
            metrics = [m for m in metrics if name_pattern in m.name]

        # Filter by timestamp
        if since:
            metrics = [m for m in metrics if m.timestamp >= since]

        # Apply limit
        return metrics[-limit:]

    def get_metric_summary(self, name: str, since: datetime = None) -> dict[str, Any]:
        """Get summary statistics for a metric."""
        metrics = [m for m in self.get_metrics(name, since) if m.name == name]

        if not metrics:
            return {"count": 0}

        values = [m.value for m in metrics]

        return {
            "count": len(values),
            "min": min(values),
            "max": max(values),
            "avg": sum(values) / len(values),
            "latest": values[-1],
            "latest_timestamp": metrics[-1].timestamp.isoformat(),
        }


class HealthChecker:
    """
    Health check system for monitoring application components.

    Provides health checks for database, cache, external services, etc.
    """

    def __init__(self):
        self._checks: dict[str, Callable[[], bool]] = {}
        self._check_results: dict[str, dict[str, Any]] = {}

    def register_check(self, name: str, check_func: Callable[[], bool]) -> None:
        """Register a health check function."""
        self._checks[name] = check_func
        logger.info(f"Registered health check: {name}")

    def run_check(self, name: str) -> dict[str, Any]:
        """Run a specific health check."""
        if name not in self._checks:
            return {"status": "unknown", "error": f"Check '{name}' not found"}

        start_time = time.time()

        try:
            result = self._checks[name]()
            duration = time.time() - start_time

            check_result = {
                "status": "healthy" if result else "unhealthy",
                "duration_ms": round(duration * 1000, 2),
                "timestamp": datetime.utcnow().isoformat(),
                "healthy": result,
            }

        except Exception as e:
            duration = time.time() - start_time
            check_result = {
                "status": "error",
                "duration_ms": round(duration * 1000, 2),
                "timestamp": datetime.utcnow().isoformat(),
                "healthy": False,
                "error": str(e),
            }

            logger.error(f"Health check '{name}' failed: {e}")

        self._check_results[name] = check_result
        return check_result

    def run_all_checks(self) -> dict[str, dict[str, Any]]:
        """Run all registered health checks."""
        results = {}

        for name in self._checks:
            results[name] = self.run_check(name)

        return results

    def get_overall_health(self) -> dict[str, Any]:
        """Get overall application health status."""
        results = self.run_all_checks()

        healthy_count = sum(1 for r in results.values() if r.get("healthy", False))
        total_count = len(results)

        overall_healthy = healthy_count == total_count

        return {
            "status": "healthy" if overall_healthy else "unhealthy",
            "healthy_checks": healthy_count,
            "total_checks": total_count,
            "timestamp": datetime.utcnow().isoformat(),
            "checks": results,
        }


class AlertManager:
    """
    Alert management system.

    Handles alert creation, notification, and resolution.
    """

    def __init__(self):
        self._alerts: list[Alert] = []
        self._alert_handlers: list[Callable[[Alert], None]] = []
        self._lock = Lock()

    def add_alert_handler(self, handler: Callable[[Alert], None]) -> None:
        """Add an alert handler function."""
        self._alert_handlers.append(handler)

    def create_alert(
        self,
        name: str,
        severity: AlertSeverity,
        message: str,
        source: str,
        metadata: dict[str, Any] = None,
    ) -> Alert:
        """Create a new alert."""
        import uuid

        alert = Alert(
            alert_id=str(uuid.uuid4()),
            name=name,
            severity=severity,
            message=message,
            timestamp=datetime.utcnow(),
            source=source,
            metadata=metadata or {},
        )

        with self._lock:
            self._alerts.append(alert)

        # Notify handlers
        for handler in self._alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                logger.error(f"Alert handler failed: {e}")

        logger.warning(f"Alert created: {name} ({severity.value}) - {message}")
        return alert

    def resolve_alert(self, alert_id: str) -> bool:
        """Resolve an alert by ID."""
        with self._lock:
            for alert in self._alerts:
                if alert.alert_id == alert_id and not alert.resolved:
                    alert.resolved = True
                    alert.resolved_at = datetime.utcnow()
                    logger.info(f"Alert resolved: {alert.name}")
                    return True

        return False

    def get_active_alerts(self) -> list[Alert]:
        """Get all active (unresolved) alerts."""
        with self._lock:
            return [alert for alert in self._alerts if not alert.resolved]

    def get_alerts(
        self, severity: AlertSeverity = None, limit: int = 100
    ) -> list[Alert]:
        """Get alerts with optional filtering."""
        with self._lock:
            alerts = self._alerts.copy()

        if severity:
            alerts = [alert for alert in alerts if alert.severity == severity]

        return sorted(alerts, key=lambda x: x.timestamp, reverse=True)[:limit]


class MonitoringSystem:
    """
    Comprehensive monitoring system.

    Combines metrics collection, health checking, and alerting.
    """

    def __init__(self):
        self.metrics = MetricsCollector()
        self.health = HealthChecker()
        self.alerts = AlertManager()

        # Initialize default health checks
        self._setup_default_health_checks()

        # Initialize default alert handlers
        self._setup_default_alert_handlers()

    def _setup_default_health_checks(self):
        """Set up default health checks."""

        def database_check() -> bool:
            """Check database connectivity."""
            try:
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    return True
            except Exception:
                return False

        def cache_check() -> bool:
            """Check cache connectivity."""
            try:
                cache.set("health_check", "ok", 10)
                return cache.get("health_check") == "ok"
            except Exception:
                return False

        def disk_space_check() -> bool:
            """Check available disk space."""
            try:
                import shutil

                total, used, free = shutil.disk_usage("/")
                free_percent = (free / total) * 100
                return free_percent > 10  # Alert if less than 10% free
            except Exception:
                return False

        self.health.register_check("database", database_check)
        self.health.register_check("cache", cache_check)
        self.health.register_check("disk_space", disk_space_check)

    def _setup_default_alert_handlers(self):
        """Set up default alert handlers."""

        def log_alert_handler(alert: Alert) -> None:
            """Log alerts to the application log."""
            log_level = {
                AlertSeverity.INFO: logger.info,
                AlertSeverity.WARNING: logger.warning,
                AlertSeverity.ERROR: logger.error,
                AlertSeverity.CRITICAL: logger.critical,
            }

            log_func = log_level.get(alert.severity, logger.info)
            log_func(f"ALERT: {alert.name} - {alert.message}")

        self.alerts.add_alert_handler(log_alert_handler)

    def record_request_metrics(
        self,
        method: str,
        path: str,
        status_code: int,
        duration_seconds: float,
        query_count: int,
    ) -> None:
        """Record metrics for HTTP requests."""
        tags = {"method": method, "path": path, "status_code": str(status_code)}

        self.metrics.record_counter("http_requests_total", 1.0, tags)
        self.metrics.record_timer("http_request_duration", duration_seconds, tags)
        self.metrics.record_gauge("http_request_queries", query_count, tags)

        # Create alerts for slow requests or high query counts
        if duration_seconds > 5.0:
            self.alerts.create_alert(
                "slow_request",
                AlertSeverity.WARNING,
                f"Slow request: {method} {path} took {duration_seconds:.2f}s",
                "monitoring_system",
                {"method": method, "path": path, "duration": duration_seconds},
            )

        if query_count > 20:
            self.alerts.create_alert(
                "high_query_count",
                AlertSeverity.WARNING,
                f"High query count: {method} {path} executed {query_count} queries",
                "monitoring_system",
                {"method": method, "path": path, "query_count": query_count},
            )

    def get_system_status(self) -> dict[str, Any]:
        """Get comprehensive system status."""
        health_status = self.health.get_overall_health()
        active_alerts = self.alerts.get_active_alerts()

        # Get recent metrics summary
        since = datetime.utcnow() - timedelta(minutes=5)
        request_metrics = self.metrics.get_metric_summary("http_requests_total", since)

        return {
            "timestamp": datetime.utcnow().isoformat(),
            "health": health_status,
            "active_alerts": len(active_alerts),
            "critical_alerts": len(
                [a for a in active_alerts if a.severity == AlertSeverity.CRITICAL]
            ),
            "recent_requests": request_metrics.get("count", 0),
            "system_load": self._get_system_load(),
        }

    def _get_system_load(self) -> dict[str, Any]:
        """Get system load information."""
        try:
            import psutil

            return {
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage("/").percent,
            }
        except ImportError:
            return {"error": "psutil not available"}


# Global monitoring instance
_monitoring_system = MonitoringSystem()


def get_monitoring_system() -> MonitoringSystem:
    """Get the global monitoring system instance."""
    return _monitoring_system
