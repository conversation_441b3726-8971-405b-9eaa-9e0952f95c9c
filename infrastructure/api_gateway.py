"""
API Gateway pattern implementation for Cosmetrics AI.

This module provides API gateway functionality including request routing,
authentication, rate limiting, and service orchestration.
"""

import time
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any

from django.http import HttpRequest, HttpResponse, JsonResponse
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from loguru import logger

from helpers.performance import monitor_performance

from .scaling import CircuitBreaker, get_service_registry


class RouteType(Enum):
    """Types of API routes."""

    PROXY = "proxy"
    AGGREGATE = "aggregate"
    TRANSFORM = "transform"
    CACHE = "cache"


@dataclass
class Route:
    """API Gateway route configuration."""

    path_pattern: str
    service_name: str
    route_type: RouteType
    target_path: str = None
    auth_required: bool = True
    rate_limit: int | None = None
    cache_ttl: int | None = None
    timeout: int = 30
    retry_count: int = 3
    circuit_breaker: bool = True
    metadata: dict[str, Any] | None = None

    def matches_path(self, path: str) -> bool:
        """Check if this route matches the given path."""
        # Simple pattern matching - in production, use regex or more sophisticated matching
        if self.path_pattern.endswith("*"):
            prefix = self.path_pattern[:-1]
            return path.startswith(prefix)
        return path == self.path_pattern


class RequestContext:
    """Context object for API Gateway requests."""

    def __init__(self, request: HttpRequest, route: Route):
        self.request = request
        self.route = route
        self.start_time = time.time()
        self.user_id = (
            getattr(request.user, "id", None) if hasattr(request, "user") else None
        )
        self.request_id = self._generate_request_id()
        self.metadata = {}

    def _generate_request_id(self) -> str:
        """Generate a unique request ID."""
        import uuid

        return str(uuid.uuid4())

    @property
    def elapsed_time(self) -> float:
        """Get elapsed time since request start."""
        return time.time() - self.start_time


class APIGatewayMiddleware:
    """
    Middleware for API Gateway functionality.

    Handles request routing, authentication, rate limiting, and monitoring.
    """

    def __init__(self):
        self.routes: list[Route] = []
        self.circuit_breakers: dict[str, CircuitBreaker] = {}
        self.rate_limiters: dict[str, dict[str, Any]] = {}
        self._setup_default_routes()

    def _setup_default_routes(self):
        """Set up default API routes."""
        default_routes = [
            Route(
                path_pattern="/api/recommendations/*",
                service_name="recommendation",
                route_type=RouteType.PROXY,
                rate_limit=100,  # 100 requests per minute
                cache_ttl=300,  # 5 minutes
            ),
            Route(
                path_pattern="/api/analytics/*",
                service_name="analytics",
                route_type=RouteType.AGGREGATE,
                rate_limit=50,
                auth_required=True,
            ),
            Route(
                path_pattern="/api/users/*",
                service_name="user",
                route_type=RouteType.PROXY,
                rate_limit=200,
            ),
            Route(
                path_pattern="/api/health",
                service_name="health",
                route_type=RouteType.PROXY,
                auth_required=False,
                rate_limit=1000,
            ),
        ]

        for route in default_routes:
            self.add_route(route)

    def add_route(self, route: Route) -> None:
        """Add a route to the gateway."""
        self.routes.append(route)

        # Initialize circuit breaker if needed
        if route.circuit_breaker and route.service_name not in self.circuit_breakers:
            self.circuit_breakers[route.service_name] = CircuitBreaker()

        logger.info(
            f"Added API Gateway route: {route.path_pattern} -> {route.service_name}"
        )

    def find_route(self, path: str) -> Route | None:
        """Find a matching route for the given path."""
        for route in self.routes:
            if route.matches_path(path):
                return route
        return None

    @monitor_performance
    def process_request(self, request: HttpRequest) -> HttpResponse:
        """Process an incoming request through the API Gateway."""
        path = request.path

        # Find matching route
        route = self.find_route(path)
        if not route:
            return JsonResponse({"error": "Route not found"}, status=404)

        # Create request context
        context = RequestContext(request, route)

        try:
            # Authentication check
            if route.auth_required and not self._is_authenticated(request):
                return JsonResponse({"error": "Authentication required"}, status=401)

            # Rate limiting
            if route.rate_limit and not self._check_rate_limit(request, route):
                return JsonResponse({"error": "Rate limit exceeded"}, status=429)

            # Route the request
            response = self._route_request(context)

            # Log metrics
            self._log_request_metrics(context, response)

            return response

        except Exception as e:
            logger.error(f"API Gateway error for {path}: {e}")
            return JsonResponse({"error": "Internal server error"}, status=500)

    def _is_authenticated(self, request: HttpRequest) -> bool:
        """Check if the request is authenticated."""
        # Simple authentication check - in production, use proper JWT/OAuth
        return hasattr(request, "user") and request.user.is_authenticated

    def _check_rate_limit(self, request: HttpRequest, route: Route) -> bool:
        """Check rate limiting for the request."""
        if not route.rate_limit:
            return True

        # Get client identifier (IP or user ID)
        client_id = self._get_client_id(request)
        rate_key = f"rate_limit:{route.service_name}:{client_id}"

        # Simple in-memory rate limiting - in production, use Redis
        now = datetime.utcnow()
        window_start = now - timedelta(minutes=1)

        if rate_key not in self.rate_limiters:
            self.rate_limiters[rate_key] = {"requests": [], "count": 0}

        rate_data = self.rate_limiters[rate_key]

        # Clean old requests
        rate_data["requests"] = [
            req_time for req_time in rate_data["requests"] if req_time > window_start
        ]

        # Check limit
        if len(rate_data["requests"]) >= route.rate_limit:
            return False

        # Add current request
        rate_data["requests"].append(now)
        return True

    def _get_client_id(self, request: HttpRequest) -> str:
        """Get client identifier for rate limiting."""
        if hasattr(request, "user") and request.user.is_authenticated:
            return f"user:{request.user.id}"

        # Get IP address
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0].strip()
        else:
            ip = request.META.get("REMOTE_ADDR", "unknown")

        return f"ip:{ip}"

    def _route_request(self, context: RequestContext) -> HttpResponse:
        """Route the request to the appropriate service."""
        route = context.route

        if route.route_type == RouteType.PROXY:
            return self._proxy_request(context)
        elif route.route_type == RouteType.AGGREGATE:
            return self._aggregate_request(context)
        elif route.route_type == RouteType.TRANSFORM:
            return self._transform_request(context)
        elif route.route_type == RouteType.CACHE:
            return self._cached_request(context)
        else:
            return JsonResponse({"error": "Unknown route type"}, status=500)

    def _proxy_request(self, context: RequestContext) -> HttpResponse:
        """Proxy request to a service instance."""
        route = context.route
        service_registry = get_service_registry()

        # Get service instance
        instance = service_registry.get_instance(route.service_name)
        if not instance:
            return JsonResponse({"error": "Service unavailable"}, status=503)

        try:
            # Use circuit breaker if configured
            if route.circuit_breaker and route.service_name in self.circuit_breakers:
                circuit_breaker = self.circuit_breakers[route.service_name]
                response_data = circuit_breaker.call(
                    self._make_service_request, context, instance
                )
            else:
                response_data = self._make_service_request(context, instance)

            return JsonResponse(response_data)

        except Exception as e:
            logger.error(f"Service request failed: {e}")
            return JsonResponse({"error": "Service error"}, status=502)

    def _make_service_request(
        self, context: RequestContext, instance
    ) -> dict[str, Any]:
        """Make HTTP request to service instance."""
        # This is a simplified implementation
        # In production, use proper HTTP client with connection pooling

        # For now, return mock data
        return {
            "message": f"Response from {context.route.service_name}",
            "instance_id": instance.instance_id,
            "request_id": context.request_id,
            "path": context.request.path,
        }

    def _aggregate_request(self, context: RequestContext) -> HttpResponse:
        """Aggregate data from multiple services."""
        # Simplified aggregation - in production, make parallel requests
        aggregated_data = {
            "aggregated": True,
            "service": context.route.service_name,
            "request_id": context.request_id,
            "timestamp": datetime.utcnow().isoformat(),
        }

        return JsonResponse(aggregated_data)

    def _transform_request(self, context: RequestContext) -> HttpResponse:
        """Transform request/response data."""
        # Simplified transformation
        transformed_data = {
            "transformed": True,
            "original_path": context.request.path,
            "service": context.route.service_name,
            "request_id": context.request_id,
        }

        return JsonResponse(transformed_data)

    def _cached_request(self, context: RequestContext) -> HttpResponse:
        """Handle cached requests."""
        from django.core.cache import cache

        cache_key = f"gateway_cache:{context.route.service_name}:{context.request.path}"
        cached_response = cache.get(cache_key)

        if cached_response:
            logger.debug(f"Cache hit for {context.request.path}")
            return JsonResponse(cached_response)

        # Make request and cache result
        response_data = self._make_service_request(context, None)

        if context.route.cache_ttl:
            cache.set(cache_key, response_data, context.route.cache_ttl)

        return JsonResponse(response_data)

    def _log_request_metrics(
        self, context: RequestContext, response: HttpResponse
    ) -> None:
        """Log request metrics for monitoring."""
        from infrastructure.monitoring import get_monitoring_system

        monitoring = get_monitoring_system()

        monitoring.record_request_metrics(
            method=context.request.method,
            path=context.request.path,
            status_code=response.status_code,
            duration_seconds=context.elapsed_time,
            query_count=0,  # Would need to track actual query count
        )


@method_decorator(csrf_exempt, name="dispatch")
class APIGatewayView(View):
    """
    Django view for API Gateway functionality.

    Handles all API requests through the gateway.
    """

    def __init__(self):
        super().__init__()
        self.gateway = APIGatewayMiddleware()

    def dispatch(self, request, *args, **kwargs):
        """Dispatch request through the API Gateway."""
        return self.gateway.process_request(request)


# Global API Gateway instance
_api_gateway = APIGatewayMiddleware()


def get_api_gateway() -> APIGatewayMiddleware:
    """Get the global API Gateway instance."""
    return _api_gateway


# Utility functions for gateway configuration
def configure_service_routes():
    """Configure routes for microservices."""
    gateway = get_api_gateway()

    # Add custom routes as needed
    custom_routes = [
        Route(
            path_pattern="/api/v2/recommendations/*",
            service_name="recommendation_v2",
            route_type=RouteType.PROXY,
            rate_limit=150,
            cache_ttl=600,
        ),
        Route(
            path_pattern="/api/admin/*",
            service_name="admin",
            route_type=RouteType.PROXY,
            auth_required=True,
            rate_limit=50,
        ),
    ]

    for route in custom_routes:
        gateway.add_route(route)
