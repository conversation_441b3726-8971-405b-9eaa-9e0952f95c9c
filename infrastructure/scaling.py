"""
Horizontal scaling and load balancing infrastructure for Cosmetrics AI.

This module provides patterns and utilities for horizontal scaling,
load balancing, and distributed system considerations.
"""

import time
from abc import ABC, abstractmethod
from collections.abc import Callable
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any

import requests
from loguru import logger


class ServiceStatus(Enum):
    """Service instance status."""

    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"
    MAINTENANCE = "maintenance"


class LoadBalancingStrategy(Enum):
    """Load balancing strategies."""

    ROUND_ROBIN = "round_robin"
    LEAST_CONNECTIONS = "least_connections"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    HEALTH_BASED = "health_based"


@dataclass
class ServiceInstance:
    """Represents a service instance in the cluster."""

    instance_id: str
    host: str
    port: int
    weight: float = 1.0
    status: ServiceStatus = ServiceStatus.UNKNOWN
    last_health_check: datetime | None = None
    connection_count: int = 0
    response_time_ms: float = 0.0
    metadata: dict[str, Any] | None = None

    @property
    def url(self) -> str:
        """Get the full URL for this instance."""
        return f"http://{self.host}:{self.port}"

    @property
    def is_healthy(self) -> bool:
        """Check if the instance is healthy."""
        return self.status == ServiceStatus.HEALTHY

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "instance_id": self.instance_id,
            "host": self.host,
            "port": self.port,
            "weight": self.weight,
            "status": self.status.value,
            "last_health_check": self.last_health_check.isoformat()
            if self.last_health_check
            else None,
            "connection_count": self.connection_count,
            "response_time_ms": self.response_time_ms,
            "url": self.url,
            "is_healthy": self.is_healthy,
            "metadata": self.metadata or {},
        }


class LoadBalancer(ABC):
    """Abstract base class for load balancers."""

    def __init__(self, service_name: str):
        self.service_name = service_name
        self.instances: list[ServiceInstance] = []
        self._current_index = 0

    def add_instance(self, instance: ServiceInstance) -> None:
        """Add a service instance."""
        self.instances.append(instance)
        logger.info(f"Added instance {instance.instance_id} to {self.service_name}")

    def remove_instance(self, instance_id: str) -> bool:
        """Remove a service instance."""
        for i, instance in enumerate(self.instances):
            if instance.instance_id == instance_id:
                del self.instances[i]
                logger.info(f"Removed instance {instance_id} from {self.service_name}")
                return True
        return False

    def get_healthy_instances(self) -> list[ServiceInstance]:
        """Get all healthy instances."""
        return [instance for instance in self.instances if instance.is_healthy]

    @abstractmethod
    def select_instance(self) -> ServiceInstance | None:
        """Select an instance based on the load balancing strategy."""
        pass

    def update_instance_status(self, instance_id: str, status: ServiceStatus) -> None:
        """Update the status of an instance."""
        for instance in self.instances:
            if instance.instance_id == instance_id:
                instance.status = status
                instance.last_health_check = datetime.utcnow()
                break


class RoundRobinLoadBalancer(LoadBalancer):
    """Round-robin load balancer implementation."""

    def select_instance(self) -> ServiceInstance | None:
        """Select instance using round-robin strategy."""
        healthy_instances = self.get_healthy_instances()

        if not healthy_instances:
            return None

        # Simple round-robin
        instance = healthy_instances[self._current_index % len(healthy_instances)]
        self._current_index += 1

        return instance


class WeightedRoundRobinLoadBalancer(LoadBalancer):
    """Weighted round-robin load balancer implementation."""

    def __init__(self, service_name: str):
        super().__init__(service_name)
        self._weight_counters: dict[str, int] = {}

    def select_instance(self) -> ServiceInstance | None:
        """Select instance using weighted round-robin strategy."""
        healthy_instances = self.get_healthy_instances()

        if not healthy_instances:
            return None

        # Find instance with highest weight ratio
        best_instance = None
        best_ratio = -1

        for instance in healthy_instances:
            current_count = self._weight_counters.get(instance.instance_id, 0)
            ratio = (
                current_count / instance.weight if instance.weight > 0 else float("inf")
            )

            if ratio < best_ratio or best_instance is None:
                best_ratio = ratio
                best_instance = instance

        if best_instance:
            self._weight_counters[best_instance.instance_id] = (
                self._weight_counters.get(best_instance.instance_id, 0) + 1
            )

        return best_instance


class LeastConnectionsLoadBalancer(LoadBalancer):
    """Least connections load balancer implementation."""

    def select_instance(self) -> ServiceInstance | None:
        """Select instance with least connections."""
        healthy_instances = self.get_healthy_instances()

        if not healthy_instances:
            return None

        # Find instance with minimum connections
        return min(healthy_instances, key=lambda x: x.connection_count)


class HealthBasedLoadBalancer(LoadBalancer):
    """Health and performance based load balancer."""

    def select_instance(self) -> ServiceInstance | None:
        """Select instance based on health and performance metrics."""
        healthy_instances = self.get_healthy_instances()

        if not healthy_instances:
            return None

        # Score instances based on response time and connection count
        def calculate_score(instance: ServiceInstance) -> float:
            # Lower score is better
            response_score = instance.response_time_ms / 1000.0  # Convert to seconds
            connection_score = instance.connection_count * 0.1
            return response_score + connection_score

        return min(healthy_instances, key=calculate_score)


class ServiceRegistry:
    """
    Service registry for service discovery and load balancing.

    Manages service instances and provides load balancing capabilities.
    """

    def __init__(self):
        self._services: dict[str, LoadBalancer] = {}
        self._health_check_interval = 30  # seconds
        self._last_health_check = {}

    def register_service(
        self,
        service_name: str,
        strategy: LoadBalancingStrategy = LoadBalancingStrategy.ROUND_ROBIN,
    ) -> None:
        """Register a new service with load balancing strategy."""
        if strategy == LoadBalancingStrategy.ROUND_ROBIN:
            load_balancer = RoundRobinLoadBalancer(service_name)
        elif strategy == LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN:
            load_balancer = WeightedRoundRobinLoadBalancer(service_name)
        elif strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
            load_balancer = LeastConnectionsLoadBalancer(service_name)
        elif strategy == LoadBalancingStrategy.HEALTH_BASED:
            load_balancer = HealthBasedLoadBalancer(service_name)
        else:
            load_balancer = RoundRobinLoadBalancer(service_name)

        self._services[service_name] = load_balancer
        logger.info(f"Registered service {service_name} with {strategy.value} strategy")

    def add_instance(self, service_name: str, instance: ServiceInstance) -> None:
        """Add an instance to a service."""
        if service_name not in self._services:
            self.register_service(service_name)

        self._services[service_name].add_instance(instance)

    def remove_instance(self, service_name: str, instance_id: str) -> bool:
        """Remove an instance from a service."""
        if service_name in self._services:
            return self._services[service_name].remove_instance(instance_id)
        return False

    def get_instance(self, service_name: str) -> ServiceInstance | None:
        """Get an available instance for a service."""
        if service_name not in self._services:
            return None

        # Perform health checks if needed
        self._maybe_health_check(service_name)

        return self._services[service_name].select_instance()

    def get_service_status(self, service_name: str) -> dict[str, Any]:
        """Get status information for a service."""
        if service_name not in self._services:
            return {"error": f"Service {service_name} not found"}

        load_balancer = self._services[service_name]
        healthy_instances = load_balancer.get_healthy_instances()

        return {
            "service_name": service_name,
            "total_instances": len(load_balancer.instances),
            "healthy_instances": len(healthy_instances),
            "strategy": load_balancer.__class__.__name__,
            "instances": [instance.to_dict() for instance in load_balancer.instances],
        }

    def _maybe_health_check(self, service_name: str) -> None:
        """Perform health checks if interval has passed."""
        now = datetime.utcnow()
        last_check = self._last_health_check.get(service_name)

        if (
            not last_check
            or (now - last_check).total_seconds() >= self._health_check_interval
        ):
            self._perform_health_checks(service_name)
            self._last_health_check[service_name] = now

    def _perform_health_checks(self, service_name: str) -> None:
        """Perform health checks on all instances of a service."""
        if service_name not in self._services:
            return

        load_balancer = self._services[service_name]

        for instance in load_balancer.instances:
            try:
                # Perform HTTP health check
                start_time = time.time()
                response = requests.get(f"{instance.url}/health/", timeout=5)
                response_time = (time.time() - start_time) * 1000

                if response.status_code == 200:
                    instance.status = ServiceStatus.HEALTHY
                    instance.response_time_ms = response_time
                else:
                    instance.status = ServiceStatus.UNHEALTHY

                instance.last_health_check = datetime.utcnow()

            except Exception as e:
                instance.status = ServiceStatus.UNHEALTHY
                instance.last_health_check = datetime.utcnow()
                logger.warning(f"Health check failed for {instance.instance_id}: {e}")


class CircuitBreaker:
    """
    Circuit breaker pattern implementation for fault tolerance.

    Prevents cascading failures by temporarily stopping requests to failing services.
    """

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute a function with circuit breaker protection."""
        if self.state == "OPEN":
            if self._should_attempt_reset():
                self.state = "HALF_OPEN"
            else:
                raise Exception("Circuit breaker is OPEN")

        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception:
            self._on_failure()
            raise

    def _should_attempt_reset(self) -> bool:
        """Check if we should attempt to reset the circuit breaker."""
        if self.last_failure_time is None:
            return False

        return (
            datetime.utcnow() - self.last_failure_time
        ).total_seconds() >= self.recovery_timeout

    def _on_success(self) -> None:
        """Handle successful operation."""
        self.failure_count = 0
        self.state = "CLOSED"

    def _on_failure(self) -> None:
        """Handle failed operation."""
        self.failure_count += 1
        self.last_failure_time = datetime.utcnow()

        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"


# Global service registry
_service_registry = ServiceRegistry()


def get_service_registry() -> ServiceRegistry:
    """Get the global service registry instance."""
    return _service_registry


# Utility functions for common scaling patterns
def setup_recommendation_service_cluster():
    """Set up a cluster for the recommendation service."""
    registry = get_service_registry()
    registry.register_service("recommendation", LoadBalancingStrategy.HEALTH_BASED)

    # Add instances (these would typically come from configuration)
    instances = [
        ServiceInstance("rec-1", "localhost", 8001, weight=1.0),
        ServiceInstance("rec-2", "localhost", 8002, weight=1.0),
        ServiceInstance(
            "rec-3", "localhost", 8003, weight=0.5
        ),  # Lower weight for smaller instance
    ]

    for instance in instances:
        registry.add_instance("recommendation", instance)


def setup_analytics_service_cluster():
    """Set up a cluster for the analytics service."""
    registry = get_service_registry()
    registry.register_service("analytics", LoadBalancingStrategy.LEAST_CONNECTIONS)

    # Add instances
    instances = [
        ServiceInstance("analytics-1", "localhost", 8011),
        ServiceInstance("analytics-2", "localhost", 8012),
    ]

    for instance in instances:
        registry.add_instance("analytics", instance)
