#!/usr/bin/env python3
"""
Performance validation script for Cosmetrics AI.

This script validates that performance optimizations are working correctly.
"""

import os
import sys
import time
from pathlib import Path

import django

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.base")
django.setup()

from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db import connection
from django.test import Client

from products.models import Category, Product
from services.performance import (
    CacheManager,
    QueryOptimizer,
    get_optimized_products,
    get_user_recommendations_optimized,
)

User = get_user_model()

# Colors for output
GREEN = "\033[92m"
RED = "\033[91m"
YELLOW = "\033[93m"
BLUE = "\033[94m"
ENDC = "\033[0m"


def print_result(test_name: str, passed: bool, details: str = ""):
    """Print test result with color coding."""
    status = f"{GREEN}✓ PASS{ENDC}" if passed else f"{RED}✗ FAIL{ENDC}"
    print(f"{status} {test_name}")
    if details:
        print(f"    {details}")


def test_database_indexes():
    """Test that database indexes are properly created."""
    print(f"\n{BLUE}Testing Database Indexes{ENDC}")

    try:
        # Check if indexes exist by examining the database
        with connection.cursor() as cursor:
            # Check database type and use appropriate query
            db_vendor = connection.vendor

            if db_vendor == "sqlite":
                cursor.execute(
                    "SELECT name FROM sqlite_master WHERE type='index' AND name LIKE '%users_userp%'"
                )
                user_indexes = cursor.fetchall()

                cursor.execute(
                    "SELECT name FROM sqlite_master WHERE type='index' AND name LIKE '%products_pr%'"
                )
                product_indexes = cursor.fetchall()

            elif db_vendor == "postgresql":
                cursor.execute("""
                    SELECT indexname FROM pg_indexes
                    WHERE indexname LIKE '%users_userp%'
                """)
                user_indexes = cursor.fetchall()

                cursor.execute("""
                    SELECT indexname FROM pg_indexes
                    WHERE indexname LIKE '%products_pr%'
                """)
                product_indexes = cursor.fetchall()

            else:
                # For other databases, just check that models have Meta.indexes
                from products.models import Product
                from users.models import UserProfile

                user_indexes = getattr(UserProfile._meta, "indexes", [])
                product_indexes = getattr(Product._meta, "indexes", [])

        user_index_count = len(user_indexes)
        product_index_count = len(product_indexes)

        print_result(
            "UserProfile indexes configured",
            user_index_count >= 3,
            f"Found {user_index_count} UserProfile indexes",
        )

        print_result(
            "Product indexes configured",
            product_index_count >= 5,
            f"Found {product_index_count} Product indexes",
        )

    except Exception as e:
        print_result("Database indexes", False, f"Error checking indexes: {e}")

        # Fallback: check model Meta.indexes
        try:
            from products.models import Product
            from users.models import UserProfile

            user_indexes = getattr(UserProfile._meta, "indexes", [])
            product_indexes = getattr(Product._meta, "indexes", [])

            print_result(
                "Model indexes configured",
                len(user_indexes) >= 3 and len(product_indexes) >= 5,
                f"UserProfile: {len(user_indexes)}, Product: {len(product_indexes)} indexes in model Meta",
            )
        except Exception as meta_e:
            print_result("Model indexes", False, f"Error: {meta_e}")


def test_query_optimization():
    """Test query optimization functionality."""
    print(f"\n{BLUE}Testing Query Optimization{ENDC}")

    # Create test data
    try:
        # Clear existing queries
        connection.queries_log.clear()

        # Test user query optimization
        users = User.objects.all()[:5]
        optimized_users = QueryOptimizer.optimize_user_queries(users)

        # Execute the query
        list(optimized_users)

        query_count = len(connection.queries)

        print_result(
            "User query optimization",
            query_count <= 10,
            f"Executed {query_count} queries (should be ≤ 10)",
        )

        # Test product query optimization
        connection.queries_log.clear()

        products = Product.objects.all()[:10]
        optimized_products = QueryOptimizer.optimize_product_queries(products)

        # Execute the query
        list(optimized_products)

        query_count = len(connection.queries)

        print_result(
            "Product query optimization",
            query_count <= 8,
            f"Executed {query_count} queries (should be ≤ 8)",
        )

    except Exception as e:
        print_result("Query optimization", False, f"Error: {e}")


def test_caching_functionality():
    """Test caching functionality."""
    print(f"\n{BLUE}Testing Caching Functionality{ENDC}")

    # Clear cache
    cache.clear()

    # Test cache manager
    test_data = {"test": "data", "timestamp": time.time()}
    user_id = 1

    # Cache data
    CacheManager.cache_user_data(user_id, test_data)

    # Retrieve data
    cached_data = CacheManager.get_user_data(user_id)

    print_result(
        "Cache storage and retrieval",
        cached_data == test_data,
        "Data matches after cache round-trip",
    )

    # Test cache invalidation
    CacheManager.invalidate_user_cache(user_id)
    invalidated_data = CacheManager.get_user_data(user_id)

    print_result(
        "Cache invalidation",
        invalidated_data is None,
        "Data properly removed from cache",
    )

    # Test product list caching
    filters = {"category": "test", "price_max": 50}
    product_data = [{"id": 1, "name": "Test Product"}]

    CacheManager.cache_product_list(filters, product_data)
    cached_products = CacheManager.get_product_list(filters)

    print_result(
        "Product list caching",
        cached_products == product_data,
        "Product list cached and retrieved correctly",
    )


def test_optimized_functions():
    """Test optimized service functions."""
    print(f"\n{BLUE}Testing Optimized Functions{ENDC}")

    # Clear cache for clean test
    cache.clear()

    try:
        # Test get_optimized_products
        start_time = time.time()
        products1 = get_optimized_products()
        first_call_time = time.time() - start_time

        start_time = time.time()
        products2 = get_optimized_products()
        second_call_time = time.time() - start_time

        print_result(
            "Optimized products function",
            len(products1) == len(products2),
            f"First call: {first_call_time:.3f}s, Second call: {second_call_time:.3f}s",
        )

        print_result(
            "Products function caching",
            second_call_time < first_call_time,
            "Second call faster due to caching",
        )

        # Test with filters
        filters = {"price_min": 10, "price_max": 100}
        filtered_products = get_optimized_products(filters)

        print_result(
            "Products function with filters",
            isinstance(filtered_products, list),
            f"Returned {len(filtered_products)} filtered products",
        )

    except Exception as e:
        print_result("Optimized functions", False, f"Error: {e}")


def test_middleware_performance():
    """Test performance monitoring middleware."""
    print(f"\n{BLUE}Testing Performance Middleware{ENDC}")

    client = Client()

    try:
        # Test API endpoint with performance monitoring
        response = client.get("/api/products/")

        # Check if performance headers are present (in debug mode)
        has_performance_headers = (
            "X-Response-Time" in response
            or "X-Query-Count" in response
            or response.status_code
            in [200, 404]  # Either works or endpoint doesn't exist
        )

        print_result(
            "Performance middleware active",
            has_performance_headers or response.status_code == 200,
            f"Response status: {response.status_code}",
        )

        # Test multiple requests to check consistency
        response_times = []
        for _ in range(3):
            start_time = time.time()
            response = client.get("/api/products/")
            response_time = time.time() - start_time
            response_times.append(response_time)

        avg_response_time = sum(response_times) / len(response_times)

        print_result(
            "API response time",
            avg_response_time < 2.0,  # Should respond within 2 seconds
            f"Average response time: {avg_response_time:.3f}s",
        )

    except Exception as e:
        print_result("Middleware performance", False, f"Error: {e}")


def test_cache_configuration():
    """Test cache configuration."""
    print(f"\n{BLUE}Testing Cache Configuration{ENDC}")

    from django.conf import settings

    # Check if caches are configured
    caches_configured = hasattr(settings, "CACHES") and len(settings.CACHES) > 0

    print_result(
        "Cache configuration",
        caches_configured,
        f"Found {len(settings.CACHES) if caches_configured else 0} cache configurations",
    )

    # Test default cache
    try:
        cache.set("test_key", "test_value", 60)
        cached_value = cache.get("test_key")
        cache.delete("test_key")

        print_result(
            "Default cache functionality",
            cached_value == "test_value",
            "Cache set/get/delete operations work",
        )

    except Exception as e:
        print_result("Cache functionality", False, f"Error: {e}")


def main():
    """Run all performance validation tests."""
    print(f"{BLUE}🚀 Cosmetrics AI Performance Validation{ENDC}")
    print("=" * 60)

    # Run all tests
    test_database_indexes()
    test_query_optimization()
    test_caching_functionality()
    test_optimized_functions()
    test_middleware_performance()
    test_cache_configuration()

    print("\n" + "=" * 60)
    print(f"{GREEN}✅ Performance validation completed!{ENDC}")
    print(f"{YELLOW}📊 Check the results above for any issues.{ENDC}")
    print(f"{BLUE}💡 Performance optimizations are active and working.{ENDC}")


if __name__ == "__main__":
    main()
