#!/usr/bin/env python3
"""
Test script to verify API CSRF and CORS configuration.

This script tests that the API endpoints are accessible without CSRF issues
and that CORS is properly configured for frontend access.
"""

import json
import os
import signal
import subprocess
import time
from pathlib import Path

import requests

# Colors for output
GREEN = "\033[92m"
RED = "\033[91m"
YELLOW = "\033[93m"
BLUE = "\033[94m"
ENDC = "\033[0m"


def print_status(message: str, status: str = "INFO"):
    """Print colored status message."""
    colors = {"ERROR": RED, "SUCCESS": GREEN, "WARNING": YELLOW, "INFO": BLUE}
    color = colors.get(status, BLUE)
    print(f"{color}[{status}]{ENDC} {message}")


def start_server():
    """Start Django development server."""
    print_status("Starting Django development server...", "INFO")

    # Change to project directory
    os.chdir(Path(__file__).parent.parent)

    # Start server in background
    process = subprocess.Popen(
        ["python", "manage.py", "runserver", "127.0.0.1:8000"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        preexec_fn=os.setsid,
    )

    # Wait for server to start
    time.sleep(5)

    return process


def stop_server(process):
    """Stop Django development server."""
    print_status("Stopping Django development server...", "INFO")
    try:
        os.killpg(os.getpgid(process.pid), signal.SIGTERM)
        process.wait(timeout=5)
    except:
        process.kill()


def test_api_health():
    """Test API health endpoint."""
    print_status("Testing API health endpoint...", "INFO")

    try:
        response = requests.get("http://127.0.0.1:8000/api/health", timeout=10)

        if response.status_code == 200:
            print_status("✅ API health endpoint working", "SUCCESS")
            return True
        else:
            print_status(
                f"❌ API health endpoint failed: {response.status_code}", "ERROR"
            )
            return False

    except requests.exceptions.RequestException as e:
        print_status(f"❌ API health endpoint error: {e}", "ERROR")
        return False


def test_csrf_token():
    """Test CSRF token endpoint."""
    print_status("Testing CSRF token endpoint...", "INFO")

    try:
        response = requests.get("http://127.0.0.1:8000/api/csrf", timeout=10)

        if response.status_code == 200:
            data = response.json()
            if "csrfToken" in data:
                print_status("✅ CSRF token endpoint working", "SUCCESS")
                return True, data["csrfToken"]
            else:
                print_status("❌ CSRF token not found in response", "ERROR")
                return False, None
        else:
            print_status(
                f"❌ CSRF token endpoint failed: {response.status_code}", "ERROR"
            )
            return False, None

    except requests.exceptions.RequestException as e:
        print_status(f"❌ CSRF token endpoint error: {e}", "ERROR")
        return False, None


def test_cors_headers():
    """Test CORS headers."""
    print_status("Testing CORS headers...", "INFO")

    try:
        # Test preflight request
        headers = {
            "Origin": "http://localhost:3000",
            "Access-Control-Request-Method": "POST",
            "Access-Control-Request-Headers": "Content-Type",
        }

        response = requests.options(
            "http://127.0.0.1:8000/api/health", headers=headers, timeout=10
        )

        cors_headers = {
            "Access-Control-Allow-Origin": response.headers.get(
                "Access-Control-Allow-Origin"
            ),
            "Access-Control-Allow-Methods": response.headers.get(
                "Access-Control-Allow-Methods"
            ),
            "Access-Control-Allow-Headers": response.headers.get(
                "Access-Control-Allow-Headers"
            ),
        }

        if any(cors_headers.values()):
            print_status("✅ CORS headers present", "SUCCESS")
            for header, value in cors_headers.items():
                if value:
                    print_status(f"  {header}: {value}", "INFO")
            return True
        else:
            print_status("❌ CORS headers missing", "ERROR")
            return False

    except requests.exceptions.RequestException as e:
        print_status(f"❌ CORS test error: {e}", "ERROR")
        return False


def test_api_without_csrf():
    """Test API endpoint without CSRF token."""
    print_status("Testing API without CSRF token...", "INFO")

    try:
        # Test GET request (should work without CSRF)
        response = requests.get("http://127.0.0.1:8000/api/users/", timeout=10)

        if response.status_code in [
            200,
            401,
            403,
        ]:  # 401/403 are OK, means auth is working
            print_status("✅ API accessible without CSRF token", "SUCCESS")
            return True
        else:
            print_status(f"❌ API failed without CSRF: {response.status_code}", "ERROR")
            print_status(f"Response: {response.text[:200]}", "ERROR")
            return False

    except requests.exceptions.RequestException as e:
        print_status(f"❌ API test error: {e}", "ERROR")
        return False


def test_api_docs():
    """Test API documentation endpoint."""
    print_status("Testing API documentation...", "INFO")

    try:
        response = requests.get("http://127.0.0.1:8000/api/docs", timeout=10)

        if response.status_code == 200:
            print_status("✅ API documentation accessible", "SUCCESS")
            return True
        else:
            print_status(f"❌ API docs failed: {response.status_code}", "ERROR")
            return False

    except requests.exceptions.RequestException as e:
        print_status(f"❌ API docs error: {e}", "ERROR")
        return False


def main():
    """Run all API tests."""
    print_status("🚀 Starting API CSRF/CORS Tests", "INFO")
    print("=" * 60)

    # Start server
    server_process = start_server()

    try:
        # Run tests
        tests = [
            test_api_health,
            test_csrf_token,
            test_cors_headers,
            test_api_without_csrf,
            test_api_docs,
        ]

        results = []
        for test in tests:
            if callable(test):
                if test.__name__ == "test_csrf_token":
                    success, token = test()
                    results.append(success)
                else:
                    results.append(test())
            print()

        # Summary
        print("=" * 60)
        passed = sum(results)
        total = len(results)

        if passed == total:
            print_status(f"🎉 All tests passed! ({passed}/{total})", "SUCCESS")
            print_status("API is properly configured for frontend access", "SUCCESS")
        else:
            print_status(f"❌ Some tests failed ({passed}/{total})", "ERROR")
            print_status("Please check the configuration", "WARNING")

    finally:
        # Stop server
        stop_server(server_process)


if __name__ == "__main__":
    main()
