#!/usr/bin/env python3
"""
Security validation script for Cosmetrics AI.

This script validates that all critical security configurations are properly set
and no hardcoded secrets are present in the codebase.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Tuple

# Colors for output
RED = "\033[91m"
GREEN = "\033[92m"
YELLOW = "\033[93m"
BLUE = "\033[94m"
ENDC = "\033[0m"


def print_status(message: str, status: str = "INFO"):
    """Print colored status message."""
    colors = {"ERROR": RED, "SUCCESS": GREEN, "WARNING": YELLOW, "INFO": BLUE}
    color = colors.get(status, BLUE)
    print(f"{color}[{status}]{ENDC} {message}")


def check_environment_variables() -> List[str]:
    """Check that required environment variables are set."""
    required_vars = [
        "SECRET_KEY",
        "CLOUDINARY_CLOUD_NAME",
        "CLOUDINARY_API_KEY",
        "CLOUDINARY_API_SECRET",
    ]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    return missing_vars


def scan_for_hardcoded_secrets() -> List[Tuple[str, str, int]]:
    """Scan codebase for potential hardcoded secrets."""
    secret_patterns = [
        r'SECRET_KEY\s*=\s*["\'][^"\']{20,}["\']',
        r'PASSWORD\s*=\s*["\'][^"\']+["\']',
        r'API_KEY\s*=\s*["\'][^"\']+["\']',
        r'API_SECRET\s*=\s*["\'][^"\']+["\']',
        r'["\'][A-Za-z0-9+/]{40,}={0,2}["\']',  # Base64-like strings
        r'["\'][0-9a-f]{32,}["\']',  # Hex strings
    ]

    findings = []
    exclude_patterns = [
        r"\.git/",
        r"node_modules/",
        r"__pycache__/",
        r"\.pyc$",
        r"\.env\.example$",
        r"\.env\.development$",
        r"validate_security\.py$",
        r"staticfiles/",
        r"media/",
    ]

    base_dir = Path(__file__).parent.parent

    for file_path in base_dir.rglob("*"):
        if file_path.is_file() and file_path.suffix in [
            ".py",
            ".js",
            ".ts",
            ".tsx",
            ".json",
            ".yaml",
            ".yml",
        ]:
            # Skip excluded files
            if any(re.search(pattern, str(file_path)) for pattern in exclude_patterns):
                continue

            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                for line_num, line in enumerate(content.splitlines(), 1):
                    for pattern in secret_patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            # Skip comments and obvious examples
                            if not (
                                line.strip().startswith("#")
                                or "example" in line.lower()
                                or "template" in line.lower()
                                or "your-" in line.lower()
                                or "REQUIRED-" in line
                            ):
                                findings.append(
                                    (str(file_path), line.strip(), line_num)
                                )
            except (UnicodeDecodeError, PermissionError):
                continue

    return findings


def check_settings_security() -> List[str]:
    """Check Django settings for security issues."""
    issues = []

    settings_files = [
        "cosmetrics_ai/settings.py",
        "cosmetrics_ai/settings/base.py",
        "cosmetrics_ai/settings/production.py",
    ]

    for settings_file in settings_files:
        if os.path.exists(settings_file):
            with open(settings_file, "r") as f:
                content = f.read()

                # Check for DEBUG=True in production files
                if "production" in settings_file and "DEBUG = True" in content:
                    issues.append(
                        f"{settings_file}: DEBUG=True found in production settings"
                    )

                # Check for ALLOWED_HOSTS = ['*']
                if (
                    "ALLOWED_HOSTS = ['*']" in content
                    or 'ALLOWED_HOSTS = ["*"]' in content
                ):
                    issues.append(
                        f"{settings_file}: ALLOWED_HOSTS allows all hosts (*)"
                    )

                # Check for hardcoded secret keys
                if re.search(r'SECRET_KEY\s*=\s*["\'][^"\']{20,}["\']', content):
                    issues.append(f"{settings_file}: Hardcoded SECRET_KEY found")

    return issues


def main():
    """Main validation function."""
    print_status("🔒 Starting Security Validation for Cosmetrics AI", "INFO")
    print()

    all_passed = True

    # Check 1: Environment Variables
    print_status("Checking required environment variables...", "INFO")
    missing_vars = check_environment_variables()
    if missing_vars:
        print_status(
            f"Missing required environment variables: {', '.join(missing_vars)}",
            "ERROR",
        )
        all_passed = False
    else:
        print_status("All required environment variables are set", "SUCCESS")
    print()

    # Check 2: Hardcoded Secrets
    print_status("Scanning for hardcoded secrets...", "INFO")
    secret_findings = scan_for_hardcoded_secrets()
    if secret_findings:
        print_status(
            f"Found {len(secret_findings)} potential hardcoded secrets:", "ERROR"
        )
        for file_path, line, line_num in secret_findings[:10]:  # Show first 10
            print(f"  {file_path}:{line_num} - {line[:80]}...")
        if len(secret_findings) > 10:
            print(f"  ... and {len(secret_findings) - 10} more")
        all_passed = False
    else:
        print_status("No hardcoded secrets detected", "SUCCESS")
    print()

    # Check 3: Settings Security
    print_status("Checking Django settings security...", "INFO")
    settings_issues = check_settings_security()
    if settings_issues:
        print_status("Settings security issues found:", "ERROR")
        for issue in settings_issues:
            print(f"  - {issue}")
        all_passed = False
    else:
        print_status("Django settings security checks passed", "SUCCESS")
    print()

    # Final Result
    if all_passed:
        print_status("🎉 All security checks passed!", "SUCCESS")
        sys.exit(0)
    else:
        print_status(
            "❌ Security validation failed. Please fix the issues above.", "ERROR"
        )
        sys.exit(1)


if __name__ == "__main__":
    main()
