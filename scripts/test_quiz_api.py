#!/usr/bin/env python3
"""
Test script for Quiz API endpoints to verify CSRF exemption and CORS functionality.

This script tests the specific quiz endpoints that were having CSRF issues.
"""

import json
import time

import requests

# Colors for output
GREEN = "\033[92m"
RED = "\033[91m"
YELLOW = "\033[93m"
BLUE = "\033[94m"
ENDC = "\033[0m"


def print_status(message: str, status: str = "INFO"):
    """Print colored status message."""
    colors = {"ERROR": RED, "SUCCESS": GREEN, "WARNING": YELLOW, "INFO": BLUE}
    color = colors.get(status, BLUE)
    print(f"{color}[{status}]{ENDC} {message}")


def test_quiz_endpoint_basic():
    """Test basic quiz endpoint access."""
    print_status("Testing quiz endpoint basic access...", "INFO")

    try:
        response = requests.get(
            "http://localhost:8000/api/quiz/replies/sessions/user/10", timeout=10
        )

        if response.status_code == 200:
            data = response.json()
            print_status(
                f"✅ Quiz endpoint working: {len(data)} sessions found", "SUCCESS"
            )
            return True
        elif response.status_code == 404:
            print_status(
                "✅ Quiz endpoint working (user not found - expected)", "SUCCESS"
            )
            return True
        else:
            print_status(f"❌ Quiz endpoint failed: {response.status_code}", "ERROR")
            print_status(f"Response: {response.text[:200]}", "ERROR")
            return False

    except requests.exceptions.RequestException as e:
        print_status(f"❌ Quiz endpoint error: {e}", "ERROR")
        return False


def test_quiz_endpoint_cors():
    """Test CORS headers on quiz endpoint."""
    print_status("Testing CORS headers on quiz endpoint...", "INFO")

    try:
        # Test preflight request
        headers = {
            "Origin": "http://localhost:3000",
            "Access-Control-Request-Method": "GET",
            "Access-Control-Request-Headers": "Content-Type",
        }

        response = requests.options(
            "http://localhost:8000/api/quiz/replies/sessions/user/10",
            headers=headers,
            timeout=10,
        )

        cors_headers = {
            "Access-Control-Allow-Origin": response.headers.get(
                "Access-Control-Allow-Origin"
            ),
            "Access-Control-Allow-Methods": response.headers.get(
                "Access-Control-Allow-Methods"
            ),
            "Access-Control-Allow-Headers": response.headers.get(
                "Access-Control-Allow-Headers"
            ),
        }

        if cors_headers["Access-Control-Allow-Origin"] == "http://localhost:3000":
            print_status("✅ CORS headers working for quiz endpoint", "SUCCESS")
            return True
        else:
            print_status("❌ CORS headers missing or incorrect", "ERROR")
            return False

    except requests.exceptions.RequestException as e:
        print_status(f"❌ CORS test error: {e}", "ERROR")
        return False


def test_quiz_endpoint_frontend_style():
    """Test quiz endpoint with frontend-style request."""
    print_status("Testing frontend-style request to quiz endpoint...", "INFO")

    try:
        headers = {
            "Origin": "http://localhost:3000",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        response = requests.get(
            "http://localhost:8000/api/quiz/replies/sessions/user/10",
            headers=headers,
            timeout=10,
        )

        if response.status_code in [200, 404]:  # Both are valid responses
            print_status("✅ Frontend-style request working", "SUCCESS")

            # Check if CORS headers are in response
            if "Access-Control-Allow-Origin" in response.headers:
                print_status("✅ CORS headers present in response", "SUCCESS")
            else:
                print_status("⚠️ CORS headers missing in response", "WARNING")

            return True
        else:
            print_status(f"❌ Frontend request failed: {response.status_code}", "ERROR")
            return False

    except requests.exceptions.RequestException as e:
        print_status(f"❌ Frontend request error: {e}", "ERROR")
        return False


def test_quiz_endpoint_without_csrf():
    """Test that quiz endpoint works without CSRF token."""
    print_status("Testing quiz endpoint without CSRF token...", "INFO")

    try:
        # Make request without any CSRF token
        response = requests.get(
            "http://localhost:8000/api/quiz/replies/sessions/user/10", timeout=10
        )

        # Should work without CSRF token (not return 403 Forbidden)
        if response.status_code != 403:
            print_status("✅ Quiz endpoint accessible without CSRF token", "SUCCESS")
            return True
        else:
            print_status("❌ Quiz endpoint blocked by CSRF protection", "ERROR")
            return False

    except requests.exceptions.RequestException as e:
        print_status(f"❌ CSRF test error: {e}", "ERROR")
        return False


def test_other_quiz_endpoints():
    """Test other quiz-related endpoints."""
    print_status("Testing other quiz endpoints...", "INFO")

    endpoints = [
        "/api/quiz/",
        "/api/quiz/questions/",
        "/api/quiz/replies/",
    ]

    results = []
    for endpoint in endpoints:
        try:
            response = requests.get(f"http://localhost:8000{endpoint}", timeout=5)
            if response.status_code in [200, 404, 405]:  # Valid responses
                print_status(f"✅ {endpoint} accessible", "SUCCESS")
                results.append(True)
            else:
                print_status(f"❌ {endpoint} failed: {response.status_code}", "ERROR")
                results.append(False)
        except requests.exceptions.RequestException as e:
            print_status(f"❌ {endpoint} error: {e}", "ERROR")
            results.append(False)

    return all(results)


def main():
    """Run all quiz API tests."""
    print_status("🚀 Starting Quiz API CSRF/CORS Tests", "INFO")
    print("=" * 60)

    # Run tests
    tests = [
        ("Basic Access", test_quiz_endpoint_basic),
        ("CORS Headers", test_quiz_endpoint_cors),
        ("Frontend Style", test_quiz_endpoint_frontend_style),
        ("CSRF Exemption", test_quiz_endpoint_without_csrf),
        ("Other Endpoints", test_other_quiz_endpoints),
    ]

    results = []
    for test_name, test_func in tests:
        print_status(f"\n--- {test_name} Test ---", "INFO")
        results.append(test_func())
        time.sleep(0.5)  # Small delay between tests

    # Summary
    print("\n" + "=" * 60)
    passed = sum(results)
    total = len(results)

    if passed == total:
        print_status(f"🎉 All tests passed! ({passed}/{total})", "SUCCESS")
        print_status("Quiz API is properly configured for frontend access", "SUCCESS")
        print_status("CSRF exemption and CORS are working correctly", "SUCCESS")
    else:
        print_status(f"❌ Some tests failed ({passed}/{total})", "ERROR")
        print_status("Please check the configuration", "WARNING")


if __name__ == "__main__":
    main()
