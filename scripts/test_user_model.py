#!/usr/bin/env python3
"""
User Model Migration Test Script

This script tests the new custom User model implementation to ensure
all functionality works correctly after the migration.
"""

import os
import sys
from pathlib import Path

import django

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings")
django.setup()

from django.contrib.auth import authenticate, get_user_model
from django.core.exceptions import ValidationError
from django.db import IntegrityError

# Colors for output
RED = "\033[91m"
GREEN = "\033[92m"
YELLOW = "\033[93m"
BLUE = "\033[94m"
ENDC = "\033[0m"


def print_status(message: str, status: str = "INFO"):
    """Print colored status message."""
    colors = {"ERROR": RED, "SUCCESS": GREEN, "WARNING": YELLOW, "INFO": BLUE}
    color = colors.get(status, BLUE)
    print(f"{color}[{status}]{ENDC} {message}")


def test_user_model():
    """Test the custom User model functionality."""
    User = get_user_model()

    print_status("Testing Custom User Model", "INFO")
    print()

    # Test 1: User Creation
    print_status("Test 1: Creating user with email", "INFO")
    try:
        test_email = "<EMAIL>"
        test_password = "testpassword123"

        # Clean up any existing test user
        User.objects.filter(email=test_email).delete()

        user = User.objects.create_user(
            email=test_email,
            password=test_password,
            first_name="Test",
            last_name="User",
        )

        assert user.email == test_email
        assert user.first_name == "Test"
        assert user.last_name == "User"
        assert user.username is None
        assert user.check_password(test_password)

        print_status("✓ User creation successful", "SUCCESS")

    except Exception as e:
        print_status(f"✗ User creation failed: {e}", "ERROR")
        return False

    # Test 2: Email Uniqueness
    print_status("Test 2: Testing email uniqueness constraint", "INFO")
    try:
        User.objects.create_user(
            email=test_email,  # Same email
            password="anotherpassword",
        )
        print_status("✗ Email uniqueness constraint failed", "ERROR")
        return False
    except IntegrityError:
        print_status("✓ Email uniqueness constraint working", "SUCCESS")
    except Exception as e:
        print_status(f"✗ Unexpected error: {e}", "ERROR")
        return False

    # Test 3: Authentication
    print_status("Test 3: Testing email-based authentication", "INFO")
    try:
        authenticated_user = authenticate(email=test_email, password=test_password)

        if authenticated_user and authenticated_user.email == test_email:
            print_status("✓ Email-based authentication working", "SUCCESS")
        else:
            print_status("✗ Email-based authentication failed", "ERROR")
            return False

    except Exception as e:
        print_status(f"✗ Authentication error: {e}", "ERROR")
        return False

    # Test 4: Superuser Creation
    print_status("Test 4: Testing superuser creation", "INFO")
    try:
        admin_email = "<EMAIL>"

        # Clean up any existing admin user
        User.objects.filter(email=admin_email).delete()

        admin_user = User.objects.create_superuser(
            email=admin_email, password="adminpassword123"
        )

        assert admin_user.is_staff is True
        assert admin_user.is_superuser is True
        assert admin_user.email == admin_email

        print_status("✓ Superuser creation successful", "SUCCESS")

    except Exception as e:
        print_status(f"✗ Superuser creation failed: {e}", "ERROR")
        return False

    # Test 5: User Manager Methods
    print_status("Test 5: Testing UserManager methods", "INFO")
    try:
        # Test create_user without email
        try:
            User.objects.create_user(email="", password="test")
            print_status("✗ Empty email validation failed", "ERROR")
            return False
        except ValueError:
            print_status("✓ Empty email validation working", "SUCCESS")

        # Test user string representation
        user_str = str(user)
        if user_str == test_email:
            print_status("✓ User string representation correct", "SUCCESS")
        else:
            print_status(f"✗ User string representation incorrect: {user_str}", "ERROR")
            return False

        # Test full_name property
        full_name = user.full_name
        expected_name = "Test User"
        if full_name == expected_name:
            print_status("✓ Full name property working", "SUCCESS")
        else:
            print_status(f"✗ Full name property incorrect: {full_name}", "ERROR")
            return False

    except Exception as e:
        print_status(f"✗ UserManager methods error: {e}", "ERROR")
        return False

    # Test 6: Model Meta Configuration
    print_status("Test 6: Testing model meta configuration", "INFO")
    try:
        assert User._meta.db_table == "auth_user"
        assert User.USERNAME_FIELD == "email"
        assert User.REQUIRED_FIELDS == []

        print_status("✓ Model meta configuration correct", "SUCCESS")

    except Exception as e:
        print_status(f"✗ Model meta configuration error: {e}", "ERROR")
        return False

    # Cleanup
    print_status("Cleaning up test data...", "INFO")
    try:
        User.objects.filter(email__in=[test_email, admin_email]).delete()
        print_status("✓ Test data cleaned up", "SUCCESS")
    except Exception as e:
        print_status(f"Warning: Cleanup failed: {e}", "WARNING")

    return True


def test_user_profile_integration():
    """Test UserProfile integration with custom User model."""
    from users.models import UserProfile

    User = get_user_model()

    print_status("Testing UserProfile Integration", "INFO")
    print()

    try:
        # Create test user
        test_email = "<EMAIL>"
        User.objects.filter(email=test_email).delete()

        user = User.objects.create_user(
            email=test_email,
            password="testpass123",
            first_name="Profile",
            last_name="Test",
        )

        # Get the automatically created profile (via signal)
        profile = user.userprofile

        # Update profile with test data
        profile.first_name = "Profile"
        profile.last_name = "Test"
        profile.completed_questions = 5
        profile.questionaire_completed = True
        profile.save()

        # Test profile relationship
        assert profile.user.email == test_email
        assert str(profile) == f"{test_email} - Profile"
        assert profile.completed_questions == 5
        assert profile.questionaire_completed is True

        print_status("✓ UserProfile integration working", "SUCCESS")
        print_status("✓ UserProfile signal auto-creation working", "SUCCESS")

        # Cleanup
        user.delete()  # This will cascade delete the profile

        return True

    except Exception as e:
        print_status(f"✗ UserProfile integration failed: {e}", "ERROR")
        return False


def main():
    """Main test function."""
    print_status("🧪 Starting User Model Migration Tests", "INFO")
    print("=" * 60)
    print()

    all_passed = True

    # Test custom user model
    if not test_user_model():
        all_passed = False

    print()

    # Test user profile integration
    if not test_user_profile_integration():
        all_passed = False

    print()
    print("=" * 60)

    if all_passed:
        print_status("🎉 All tests passed! User model migration successful.", "SUCCESS")
        print_status("The custom User model is ready for production use.", "INFO")
        sys.exit(0)
    else:
        print_status("❌ Some tests failed. Please review the errors above.", "ERROR")
        print_status("Do not deploy until all tests pass.", "WARNING")
        sys.exit(1)


if __name__ == "__main__":
    main()
