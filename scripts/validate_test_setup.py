#!/usr/bin/env python3
"""
Validation script for Users API test setup.

This script validates that all test files, fixtures, and configurations
are properly set up for achieving 100% code coverage.
"""

import os
import sys
from pathlib import Path
import importlib.util
import ast


def check_file_exists(file_path, description):
    """Check if a file exists and report status."""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (MISSING)")
        return False


def check_directory_structure():
    """Check if the test directory structure is correct."""
    print("🔍 Checking test directory structure...")
    
    required_files = [
        ("tests/users/__init__.py", "Test package init"),
        ("tests/users/conftest.py", "Pytest configuration"),
        ("tests/users/test_models.py", "Model tests"),
        ("tests/users/test_api_endpoints.py", "API endpoint tests"),
        ("tests/users/test_schemas.py", "Schema tests"),
        ("tests/users/test_backends.py", "Backend tests"),
        ("tests/users/test_utils.py", "Utility tests"),
    ]
    
    all_exist = True
    for file_path, description in required_files:
        if not check_file_exists(file_path, description):
            all_exist = False
    
    return all_exist


def check_pytest_configuration():
    """Check pytest configuration files."""
    print("\n🔍 Checking pytest configuration...")
    
    config_files = [
        ("pytest.ini", "Pytest configuration"),
        ("requirements-test.txt", "Test requirements"),
    ]
    
    all_exist = True
    for file_path, description in config_files:
        if not check_file_exists(file_path, description):
            all_exist = False
    
    return all_exist


def check_github_workflow():
    """Check GitHub Actions workflow."""
    print("\n🔍 Checking GitHub Actions workflow...")
    
    workflow_file = ".github/workflows/users-unit-tests.yml"
    return check_file_exists(workflow_file, "GitHub Actions workflow")


def analyze_test_file(file_path):
    """Analyze a test file for proper structure."""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        # Count test classes and functions
        test_classes = 0
        test_functions = 0
        fixtures = 0
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name.startswith('Test'):
                test_classes += 1
            elif isinstance(node, ast.FunctionDef):
                if node.name.startswith('test_'):
                    test_functions += 1
                elif any(decorator.id == 'pytest.fixture' for decorator in node.decorator_list 
                        if isinstance(decorator, ast.Attribute)):
                    fixtures += 1
        
        return {
            'test_classes': test_classes,
            'test_functions': test_functions,
            'fixtures': fixtures,
            'lines': len(content.splitlines())
        }
    
    except Exception as e:
        print(f"⚠️  Error analyzing {file_path}: {e}")
        return None


def check_test_coverage():
    """Check test file coverage and structure."""
    print("\n🔍 Analyzing test file structure...")
    
    test_files = [
        "tests/users/test_models.py",
        "tests/users/test_api_endpoints.py",
        "tests/users/test_schemas.py",
        "tests/users/test_backends.py",
        "tests/users/test_utils.py",
    ]
    
    total_classes = 0
    total_functions = 0
    total_lines = 0
    
    for test_file in test_files:
        if os.path.exists(test_file):
            analysis = analyze_test_file(test_file)
            if analysis:
                print(f"📊 {test_file}:")
                print(f"   - Test classes: {analysis['test_classes']}")
                print(f"   - Test functions: {analysis['test_functions']}")
                print(f"   - Lines of code: {analysis['lines']}")
                
                total_classes += analysis['test_classes']
                total_functions += analysis['test_functions']
                total_lines += analysis['lines']
    
    print(f"\n📈 Total test coverage:")
    print(f"   - Test classes: {total_classes}")
    print(f"   - Test functions: {total_functions}")
    print(f"   - Total test lines: {total_lines}")
    
    return total_functions > 0


def check_fixtures():
    """Check if conftest.py has proper fixtures."""
    print("\n🔍 Checking test fixtures...")
    
    conftest_path = "tests/users/conftest.py"
    if not os.path.exists(conftest_path):
        print("❌ conftest.py not found")
        return False
    
    analysis = analyze_test_file(conftest_path)
    if analysis and analysis['fixtures'] > 0:
        print(f"✅ Found {analysis['fixtures']} fixtures in conftest.py")
        return True
    else:
        print("❌ No fixtures found in conftest.py")
        return False


def check_imports():
    """Check if test files have proper imports."""
    print("\n🔍 Checking test imports...")
    
    required_imports = {
        "tests/users/test_models.py": ["pytest", "django.contrib.auth"],
        "tests/users/test_api_endpoints.py": ["pytest", "django.test"],
        "tests/users/test_schemas.py": ["pytest", "pydantic"],
        "tests/users/test_backends.py": ["pytest", "unittest.mock"],
        "tests/users/test_utils.py": ["pytest", "json"],
    }
    
    all_good = True
    for test_file, imports in required_imports.items():
        if os.path.exists(test_file):
            with open(test_file, 'r') as f:
                content = f.read()
            
            missing_imports = []
            for imp in imports:
                if imp not in content:
                    missing_imports.append(imp)
            
            if missing_imports:
                print(f"⚠️  {test_file} missing imports: {missing_imports}")
                all_good = False
            else:
                print(f"✅ {test_file} has required imports")
    
    return all_good


def check_markers():
    """Check if test files use proper pytest markers."""
    print("\n🔍 Checking pytest markers...")
    
    test_files = [
        "tests/users/test_models.py",
        "tests/users/test_api_endpoints.py",
        "tests/users/test_schemas.py",
        "tests/users/test_backends.py",
        "tests/users/test_utils.py",
    ]
    
    markers_found = False
    for test_file in test_files:
        if os.path.exists(test_file):
            with open(test_file, 'r') as f:
                content = f.read()
            
            if "@pytest.mark." in content:
                markers_found = True
                print(f"✅ {test_file} uses pytest markers")
            else:
                print(f"⚠️  {test_file} no pytest markers found")
    
    return markers_found


def check_django_db_decorator():
    """Check if Django database tests use @pytest.mark.django_db."""
    print("\n🔍 Checking Django database decorators...")
    
    test_files = [
        "tests/users/test_models.py",
        "tests/users/test_api_endpoints.py",
        "tests/users/test_backends.py",
    ]
    
    all_good = True
    for test_file in test_files:
        if os.path.exists(test_file):
            with open(test_file, 'r') as f:
                content = f.read()
            
            if "@pytest.mark.django_db" in content:
                print(f"✅ {test_file} uses @pytest.mark.django_db")
            else:
                print(f"❌ {test_file} missing @pytest.mark.django_db")
                all_good = False
    
    return all_good


def check_test_runner_script():
    """Check if test runner script exists and is executable."""
    print("\n🔍 Checking test runner script...")
    
    script_path = "scripts/run_users_tests.py"
    if check_file_exists(script_path, "Test runner script"):
        # Check if script is executable
        if os.access(script_path, os.X_OK):
            print("✅ Test runner script is executable")
        else:
            print("⚠️  Test runner script is not executable")
            print("   Run: chmod +x scripts/run_users_tests.py")
        return True
    
    return False


def main():
    """Main validation function."""
    print("🧪 Users API Test Setup Validation")
    print("=" * 50)
    
    checks = [
        ("Directory Structure", check_directory_structure),
        ("Pytest Configuration", check_pytest_configuration),
        ("GitHub Workflow", check_github_workflow),
        ("Test Coverage", check_test_coverage),
        ("Test Fixtures", check_fixtures),
        ("Test Imports", check_imports),
        ("Pytest Markers", check_markers),
        ("Django DB Decorators", check_django_db_decorator),
        ("Test Runner Script", check_test_runner_script),
    ]
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, check_function in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            if check_function():
                passed_checks += 1
                print(f"✅ {check_name} - PASSED")
            else:
                print(f"❌ {check_name} - FAILED")
        except Exception as e:
            print(f"❌ {check_name} - ERROR: {e}")
    
    print("\n" + "="*60)
    print(f"📊 Validation Summary: {passed_checks}/{total_checks} checks passed")
    
    if passed_checks == total_checks:
        print("🎉 All validation checks passed!")
        print("✅ Test setup is ready for 100% coverage testing")
        return 0
    else:
        print("⚠️  Some validation checks failed")
        print("❌ Please fix the issues above before running tests")
        return 1


if __name__ == "__main__":
    sys.exit(main())
