#!/usr/bin/env python
"""
Comprehensive test runner for Cosmetrics AI.

This script provides various testing options including unit tests, integration tests,
performance tests, and code coverage reporting.
"""

import argparse
import os
import subprocess
import sys


def run_command(command: str, description: str = None) -> int:
    """Run a shell command and return the exit code."""
    if description:
        print(f"\n🔄 {description}")

    print(f"Running: {command}")
    result = subprocess.run(command, shell=True)

    if result.returncode == 0:
        print(f"✅ Success: {description or command}")
    else:
        print(f"❌ Failed: {description or command}")

    return result.returncode


def run_unit_tests(verbose: bool = False, coverage: bool = True) -> int:
    """Run unit tests."""
    cmd = "python -m pytest tests/ -m 'unit or not integration'"

    if verbose:
        cmd += " -v"

    if coverage:
        cmd += " --cov=. --cov-report=html --cov-report=term-missing"

    return run_command(cmd, "Running unit tests")


def run_integration_tests(verbose: bool = False) -> int:
    """Run integration tests."""
    cmd = "python -m pytest tests/ -m 'integration'"

    if verbose:
        cmd += " -v"

    return run_command(cmd, "Running integration tests")


def run_api_tests(verbose: bool = False) -> int:
    """Run API tests."""
    cmd = "python -m pytest tests/ -m 'api'"

    if verbose:
        cmd += " -v"

    return run_command(cmd, "Running API tests")


def run_performance_tests(verbose: bool = False) -> int:
    """Run performance tests."""
    cmd = "python -m pytest tests/ -m 'performance'"

    if verbose:
        cmd += " -v"

    return run_command(cmd, "Running performance tests")


def run_security_tests(verbose: bool = False) -> int:
    """Run security tests."""
    cmd = "python -m pytest tests/ -m 'security'"

    if verbose:
        cmd += " -v"

    return run_command(cmd, "Running security tests")


def run_smoke_tests(verbose: bool = False) -> int:
    """Run smoke tests."""
    cmd = "python -m pytest tests/ -m 'smoke'"

    if verbose:
        cmd += " -v"

    return run_command(cmd, "Running smoke tests")


def run_all_tests(verbose: bool = False, coverage: bool = True) -> int:
    """Run all tests."""
    cmd = "python -m pytest tests/"

    if verbose:
        cmd += " -v"

    if coverage:
        cmd += " --cov=. --cov-report=html --cov-report=term-missing --cov-report=xml"

    return run_command(cmd, "Running all tests")


def run_django_tests(verbose: bool = False) -> int:
    """Run Django's built-in tests."""
    cmd = "python manage.py test"

    if verbose:
        cmd += " --verbosity=2"

    return run_command(cmd, "Running Django tests")


def run_linting() -> int:
    """Run code linting."""
    commands = [
        ("ruff check .", "Running Ruff linting"),
        ("ruff format --check .", "Checking code formatting"),
        ("pyright", "Running type checking with Pyright"),
        ("bandit -r . -f json -o bandit-report.json", "Running security analysis"),
    ]

    exit_codes = []
    for cmd, desc in commands:
        exit_codes.append(run_command(cmd, desc))

    return max(exit_codes) if exit_codes else 0


def run_django_checks() -> int:
    """Run Django system checks."""
    commands = [
        ("python manage.py check", "Running Django system checks"),
        ("python manage.py check --deploy", "Running Django deployment checks"),
        (
            "python manage.py makemigrations --check --dry-run",
            "Checking for missing migrations",
        ),
    ]

    exit_codes = []
    for cmd, desc in commands:
        exit_codes.append(run_command(cmd, desc))

    return max(exit_codes) if exit_codes else 0


def generate_coverage_report() -> int:
    """Generate detailed coverage report."""
    commands = [
        ("coverage html", "Generating HTML coverage report"),
        ("coverage xml", "Generating XML coverage report"),
        ("coverage report", "Displaying coverage summary"),
    ]

    exit_codes = []
    for cmd, desc in commands:
        exit_codes.append(run_command(cmd, desc))

    if exit_codes and max(exit_codes) == 0:
        print("\n📊 Coverage reports generated:")
        print("  - HTML: htmlcov/index.html")
        print("  - XML: coverage.xml")

    return max(exit_codes) if exit_codes else 0


def setup_test_environment() -> int:
    """Set up the test environment."""
    commands = [
        ("python manage.py collectstatic --noinput", "Collecting static files"),
        ("python manage.py migrate", "Running database migrations"),
    ]

    exit_codes = []
    for cmd, desc in commands:
        exit_codes.append(run_command(cmd, desc))

    return max(exit_codes) if exit_codes else 0


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="Cosmetrics AI Test Runner")

    # Test type options
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument(
        "--integration", action="store_true", help="Run integration tests only"
    )
    parser.add_argument("--api", action="store_true", help="Run API tests only")
    parser.add_argument(
        "--performance", action="store_true", help="Run performance tests only"
    )
    parser.add_argument(
        "--security", action="store_true", help="Run security tests only"
    )
    parser.add_argument("--smoke", action="store_true", help="Run smoke tests only")
    parser.add_argument(
        "--django", action="store_true", help="Run Django built-in tests"
    )
    parser.add_argument("--all", action="store_true", help="Run all tests (default)")

    # Additional options
    parser.add_argument(
        "--lint", action="store_true", help="Run linting and code quality checks"
    )
    parser.add_argument("--check", action="store_true", help="Run Django system checks")
    parser.add_argument(
        "--coverage", action="store_true", help="Generate coverage report"
    )
    parser.add_argument("--setup", action="store_true", help="Set up test environment")

    # Modifiers
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    parser.add_argument(
        "--no-coverage", action="store_true", help="Disable coverage reporting"
    )
    parser.add_argument(
        "--fast", action="store_true", help="Run tests without coverage for speed"
    )

    args = parser.parse_args()

    # Set environment variables for testing
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.testing")

    exit_codes = []

    # Setup environment if requested
    if args.setup:
        exit_codes.append(setup_test_environment())

    # Run linting if requested
    if args.lint:
        exit_codes.append(run_linting())

    # Run Django checks if requested
    if args.check:
        exit_codes.append(run_django_checks())

    # Determine coverage setting
    use_coverage = not (args.no_coverage or args.fast)

    # Run specific test types
    if args.unit:
        exit_codes.append(run_unit_tests(args.verbose, use_coverage))
    elif args.integration:
        exit_codes.append(run_integration_tests(args.verbose))
    elif args.api:
        exit_codes.append(run_api_tests(args.verbose))
    elif args.performance:
        exit_codes.append(run_performance_tests(args.verbose))
    elif args.security:
        exit_codes.append(run_security_tests(args.verbose))
    elif args.smoke:
        exit_codes.append(run_smoke_tests(args.verbose))
    elif args.django:
        exit_codes.append(run_django_tests(args.verbose))
    elif args.all or not any(
        [
            args.unit,
            args.integration,
            args.api,
            args.performance,
            args.security,
            args.smoke,
            args.django,
        ]
    ):
        # Run all tests if no specific type is specified
        exit_codes.append(run_all_tests(args.verbose, use_coverage))

    # Generate coverage report if requested
    if args.coverage:
        exit_codes.append(generate_coverage_report())

    # Final summary
    if exit_codes:
        max_exit_code = max(exit_codes)
        if max_exit_code == 0:
            print("\n🎉 All tests and checks passed!")
        else:
            print(f"\n💥 Some tests or checks failed (exit code: {max_exit_code})")

        return max_exit_code
    else:
        print("\n⚠️  No tests were run. Use --help for options.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
