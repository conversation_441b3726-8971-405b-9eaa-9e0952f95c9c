#!/usr/bin/env python3
"""
Frontend testing script for Cosmetrics AI.

This script validates that the frontend components are properly configured
and can be accessed without errors.
"""

import json
import os
import subprocess
import sys
from pathlib import Path

# Colors for output
GREEN = "\033[92m"
RED = "\033[91m"
YELLOW = "\033[93m"
BLUE = "\033[94m"
ENDC = "\033[0m"


def print_status(message: str, status: str = "INFO"):
    """Print colored status message."""
    colors = {"ERROR": RED, "SUCCESS": GREEN, "WARNING": YELLOW, "INFO": BLUE}
    color = colors.get(status, BLUE)
    print(f"{color}[{status}]{ENDC} {message}")


def check_file_exists(file_path: str) -> bool:
    """Check if a file exists."""
    return Path(file_path).exists()


def check_package_json():
    """Check if package.json has required dependencies."""
    print_status("Checking package.json dependencies...", "INFO")

    package_json_path = "ui/package.json"
    if not check_file_exists(package_json_path):
        print_status("package.json not found!", "ERROR")
        return False

    try:
        with open(package_json_path, "r") as f:
            package_data = json.load(f)

        required_deps = [
            "react",
            "react-dom",
            "react-bootstrap",
            "lucide-react",
            "@tanstack/react-query",
            "react-router-dom",
        ]

        dependencies = package_data.get("dependencies", {})
        missing_deps = []

        for dep in required_deps:
            if dep not in dependencies:
                missing_deps.append(dep)

        if missing_deps:
            print_status(f"Missing dependencies: {', '.join(missing_deps)}", "ERROR")
            return False
        else:
            print_status("All required dependencies found", "SUCCESS")
            return True

    except Exception as e:
        print_status(f"Error reading package.json: {e}", "ERROR")
        return False


def check_modern_components():
    """Check if modern components exist."""
    print_status("Checking modern components...", "INFO")

    components = [
        "ui/src/screens/ModernDashboard/index.tsx",
        "ui/src/screens/ModernHome/index.tsx",
        "ui/src/components/auth/ModernAuth.tsx",
        "ui/src/components/navigation/ModernNavbar.tsx",
        "ui/src/components/layout/ModernLayout.tsx",
        "ui/src/services/modernAPI.ts",
        "ui/src/hooks/api/useModernAPI.ts",
    ]

    missing_components = []
    for component in components:
        if not check_file_exists(component):
            missing_components.append(component)

    if missing_components:
        print_status(f"Missing components: {', '.join(missing_components)}", "ERROR")
        return False
    else:
        print_status("All modern components found", "SUCCESS")
        return True


def check_app_tsx():
    """Check if App.tsx has modern routes."""
    print_status("Checking App.tsx routes...", "INFO")

    app_tsx_path = "ui/src/App.tsx"
    if not check_file_exists(app_tsx_path):
        print_status("App.tsx not found!", "ERROR")
        return False

    try:
        with open(app_tsx_path, "r") as f:
            content = f.read()

        required_routes = [
            "/modern",
            "/moderndashboard",
            "/modern-login",
            "/modern-register",
        ]

        missing_routes = []
        for route in required_routes:
            if route not in content:
                missing_routes.append(route)

        if missing_routes:
            print_status(f"Missing routes: {', '.join(missing_routes)}", "WARNING")
            return False
        else:
            print_status("All modern routes found in App.tsx", "SUCCESS")
            return True

    except Exception as e:
        print_status(f"Error reading App.tsx: {e}", "ERROR")
        return False


def run_npm_install():
    """Run npm install if needed."""
    print_status("Checking if npm install is needed...", "INFO")

    if not check_file_exists("ui/node_modules"):
        print_status("Running npm install...", "INFO")
        try:
            result = subprocess.run(
                ["npm", "install"],
                cwd="ui",
                capture_output=True,
                text=True,
                timeout=300,  # 5 minutes timeout
            )

            if result.returncode == 0:
                print_status("npm install completed successfully", "SUCCESS")
                return True
            else:
                print_status(f"npm install failed: {result.stderr}", "ERROR")
                return False

        except subprocess.TimeoutExpired:
            print_status("npm install timed out", "ERROR")
            return False
        except Exception as e:
            print_status(f"Error running npm install: {e}", "ERROR")
            return False
    else:
        print_status("node_modules exists, skipping npm install", "SUCCESS")
        return True


def main():
    """Run all frontend tests."""
    print_status("🚀 Starting Frontend Validation", "INFO")
    print("=" * 60)

    # Change to project root
    os.chdir(Path(__file__).parent.parent)

    all_passed = True

    # Check package.json
    if not check_package_json():
        all_passed = False
    print()

    # Check modern components
    if not check_modern_components():
        all_passed = False
    print()

    # Check App.tsx
    if not check_app_tsx():
        all_passed = False
    print()

    # Run npm install if needed
    if not run_npm_install():
        all_passed = False
    print()

    print("=" * 60)

    if all_passed:
        print_status("🎉 All frontend checks passed!", "SUCCESS")
        print_status("The modern dashboard should be accessible at:", "INFO")
        print_status("http://localhost:3000/moderndashboard", "INFO")
        print()
        print_status("To start the development server:", "INFO")
        print_status("cd ui && npm start", "INFO")
        sys.exit(0)
    else:
        print_status("❌ Some frontend checks failed.", "ERROR")
        print_status("Please review the errors above and fix them.", "WARNING")
        sys.exit(1)


if __name__ == "__main__":
    main()
