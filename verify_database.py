#!/usr/bin/env python
"""
Database verification script for Django project.
This script verifies that the database has been populated correctly.
"""

import os
import sys

import django

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings")
django.setup()

from django.contrib.auth import get_user_model

from products.models import Product
from questionaire.models import Question, Questionaire


def verify_database():
    """Verify that the database was populated correctly."""
    User = get_user_model()

    # Count records
    user_count = User.objects.filter(is_superuser=False).count()
    product_count = Product.objects.count()
    question_count = Question.objects.count()
    questionaire_count = Questionaire.objects.count()

    print(f"📊 Database Population Summary:")
    print(f"👥 Users: {user_count}")
    print(f"🛍️  Products: {product_count}")
    print(f"❓ Questions: {question_count}")
    print(f"📋 Questionnaires: {questionaire_count}")

    # Verify minimum expected counts
    success = True
    if user_count < 20:
        print(f"⚠️  Warning: Expected at least 20 users, got {user_count}")
        success = False
    if product_count < 40:
        print(f"⚠️  Warning: Expected at least 40 products, got {product_count}")
        success = False
    if question_count < 40:
        print(f"⚠️  Warning: Expected at least 40 questions, got {question_count}")
        success = False
    if questionaire_count < 1:
        print(f"❌ Error: Expected at least 1 questionnaire, got {questionaire_count}")
        success = False

    if success:
        print("✅ Database population verification successful!")
    else:
        print("❌ Database population verification failed!")

    return success


if __name__ == "__main__":
    success = verify_database()
    sys.exit(0 if success else 1)
