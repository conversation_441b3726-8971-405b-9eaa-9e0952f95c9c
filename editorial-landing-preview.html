<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editorial Landing Page - Hair Care AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            line-height: 1.6;
            color: #1F2937;
            background: #FAFAFA;
        }

        /* Header */
        .header {
            background: white;
            padding: 1rem 0;
            border-bottom: 1px solid #E5E7EB;
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1F2937;
            text-decoration: none;
        }

        .logo::before {
            content: '✨';
            margin-right: 0.5rem;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: #6B7280;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s;
        }

        .nav-links a:hover {
            color: #1F2937;
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #1F2937;
            color: white;
        }

        .btn-primary:hover {
            background: #111827;
            transform: translateY(-1px);
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: transparent;
            border: 2px solid #E5E7EB;
            color: #6B7280;
        }

        .btn-secondary:hover {
            border-color: #1F2937;
            color: #1F2937;
            text-decoration: none;
        }

        /* Hero Section */
        .hero-section {
            background: white;
            padding: 4rem 0;
        }

        .hero-grid {
            display: grid;
            grid-template-columns: 1fr 1.2fr;
            gap: 4rem;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .hero-content h1 {
            font-size: 3.5rem;
            font-weight: 800;
            color: #1F2937;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            letter-spacing: -0.02em;
        }

        .subtitle {
            font-size: 1.25rem;
            color: #6B7280;
            line-height: 1.6;
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .stats {
            display: flex;
            gap: 2rem;
            margin: 2rem 0;
        }

        .stat-item .number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1F2937;
            display: block;
        }

        .stat-item .label {
            font-size: 0.875rem;
            color: #6B7280;
            font-weight: 500;
        }

        .hero-image {
            position: relative;
            border-radius: 24px;
            overflow: hidden;
            aspect-ratio: 4/5;
            background: linear-gradient(135deg, #F3F4F6 0%, #E5E7EB 100%);
        }

        .hero-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .overlay-card {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1.5rem;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .overlay-card .metric {
            font-size: 2rem;
            font-weight: 800;
            color: #4300FF;
            display: block;
        }

        .overlay-card .label {
            font-size: 0.875rem;
            color: #6B7280;
            font-weight: 500;
        }

        /* Features Section */
        .features-section {
            padding: 6rem 0;
            background: white;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 800;
            color: #1F2937;
            margin-bottom: 1rem;
            letter-spacing: -0.02em;
        }

        .section-header p {
            font-size: 1.125rem;
            color: #6B7280;
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .feature-card {
            background: #FAFAFA;
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
            border: 1px solid #F3F4F6;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.08);
            background: white;
        }

        .feature-card .icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: rgba(67, 0, 255, 0.08);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            color: #4300FF;
            font-size: 1.5rem;
        }

        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 0.75rem;
        }

        .feature-card p {
            color: #6B7280;
            line-height: 1.6;
            margin: 0;
        }

        /* Testimonial Section */
        .testimonial-section {
            padding: 6rem 0;
            background: #F9FAFB;
        }

        .testimonial-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            align-items: center;
        }

        .testimonial-image {
            border-radius: 20px;
            overflow: hidden;
            aspect-ratio: 4/5;
            background: #E5E7EB;
        }

        .testimonial-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .testimonial-content blockquote {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1F2937;
            line-height: 1.4;
            margin-bottom: 2rem;
            font-style: italic;
        }

        .author {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .author .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #E5E7EB;
            overflow: hidden;
        }

        .author .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .author .name {
            font-weight: 600;
            color: #1F2937;
            display: block;
        }

        .author .title {
            color: #6B7280;
            font-size: 0.875rem;
        }

        /* Responsive */
        @media (max-width: 968px) {
            .hero-grid,
            .testimonial-grid {
                grid-template-columns: 1fr;
                gap: 3rem;
            }
            
            .hero-content {
                text-align: center;
            }
            
            .hero-content h1 {
                font-size: 2.5rem;
            }
            
            .stats {
                justify-content: center;
            }
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .navbar {
                padding: 0 1rem;
            }
            
            .hero-grid,
            .features-grid,
            .testimonial-grid {
                padding: 0 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="navbar">
            <a href="/" class="logo">Cosmetrics</a>
            
            <div class="nav-links">
                <a href="#how-it-works">How it works</a>
                <a href="#features">Features</a>
                <a href="#about">About</a>
            </div>
            
            <div class="auth-buttons">
                <a href="/login" style="color: #6B7280; text-decoration: none; font-weight: 500;">
                    Login
                </a>
                <a href="/register" class="btn btn-secondary">
                    Register
                </a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-grid">
            <div class="hero-content">
                <h1>
                    WHERE YOUR HAIR<br>
                    HEALTH FLOURISHES
                </h1>
                <p class="subtitle">
                    Discover personalized hair care through AI-powered analysis, expert recommendations, and progress tracking.
                </p>
                
                <div class="stats">
                    <div class="stat-item">
                        <span class="number">24k</span>
                        <span class="label">Users helped</span>
                    </div>
                    <div class="stat-item">
                        <span class="number">95%</span>
                        <span class="label">Accuracy rate</span>
                    </div>
                    <div class="stat-item">
                        <span class="number">1000+</span>
                        <span class="label">Products</span>
                    </div>
                </div>
                
                <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                    <a href="/quiz" class="btn btn-primary">
                        Start Analysis
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <a href="#demo" class="btn btn-secondary">
                        <i class="fas fa-play"></i>
                        Watch Demo
                    </a>
                </div>
            </div>
            
            <div class="hero-image">
                <img 
                    src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=750&fit=crop&crop=face" 
                    alt="Beautiful hair"
                />
                <div class="overlay-card">
                    <span class="metric">17</span>
                    <span class="label">Hair metrics analyzed</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section" id="how-it-works">
        <div class="section-header">
            <h2>How It Works</h2>
            <p>
                Our technology combines computer vision, machine learning, and expert knowledge 
                to give you personalized hair care insights.
            </p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3>AI Analysis</h3>
                <p>Advanced algorithms analyze your hair condition with professional-grade accuracy.</p>
            </div>
            
            <div class="feature-card">
                <div class="icon">
                    <i class="fas fa-camera"></i>
                </div>
                <h3>Photo Assessment</h3>
                <p>Upload 3 photos and get instant insights into your hair's health and needs.</p>
            </div>
            
            <div class="feature-card">
                <div class="icon">
                    <i class="fas fa-award"></i>
                </div>
                <h3>Expert Curation</h3>
                <p>Personalized product recommendations from our database of premium hair care.</p>
            </div>
            
            <div class="feature-card">
                <div class="icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3>Progress Tracking</h3>
                <p>Monitor your hair journey with detailed reports and improvement metrics.</p>
            </div>
        </div>
    </section>

    <!-- Testimonial Section -->
    <section class="testimonial-section">
        <div class="testimonial-grid">
            <div class="testimonial-image">
                <img 
                    src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=600&h=750&fit=crop&crop=face" 
                    alt="Happy customer"
                />
            </div>
            
            <div class="testimonial-content">
                <blockquote>
                    "Finally found products that actually work for my hair type. The AI analysis was spot-on and the recommendations transformed my routine."
                </blockquote>
                
                <div class="author">
                    <div class="avatar">
                        <img 
                            src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=96&h=96&fit=crop&crop=face" 
                            alt="Sarah Chen"
                        />
                    </div>
                    <div class="info">
                        <span class="name">Sarah Chen</span>
                        <span class="title">Verified User</span>
                    </div>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
