"""
Data validation pipeline for Cosmetrics AI.

This module provides comprehensive data validation capabilities
for ensuring data quality and integrity throughout the system.
"""

import re
from abc import ABC, abstractmethod
from collections.abc import Callable
from dataclasses import dataclass
from enum import Enum
from typing import Any

from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from loguru import logger


class ValidationSeverity(Enum):
    """Severity levels for validation issues."""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ValidationResult:
    """Result of a validation operation."""

    is_valid: bool
    errors: list[dict[str, Any]]
    warnings: list[dict[str, Any]]
    info: list[dict[str, Any]]

    @property
    def has_errors(self) -> bool:
        """Check if there are any errors."""
        return len(self.errors) > 0

    @property
    def has_warnings(self) -> bool:
        """Check if there are any warnings."""
        return len(self.warnings) > 0

    def add_issue(
        self, severity: ValidationSeverity, field: str, message: str, code: str = None
    ) -> None:
        """Add a validation issue."""
        issue = {
            "field": field,
            "message": message,
            "code": code or f"{severity.value}_validation",
        }

        if (
            severity == ValidationSeverity.ERROR
            or severity == ValidationSeverity.CRITICAL
        ):
            self.errors.append(issue)
            self.is_valid = False
        elif severity == ValidationSeverity.WARNING:
            self.warnings.append(issue)
        else:
            self.info.append(issue)


class ValidationRule(ABC):
    """Abstract base class for validation rules."""

    def __init__(
        self, field_name: str, severity: ValidationSeverity = ValidationSeverity.ERROR
    ):
        self.field_name = field_name
        self.severity = severity

    @abstractmethod
    def validate(self, value: Any, context: dict[str, Any] = None) -> str | None:
        """
        Validate a value.

        Returns None if valid, error message if invalid.
        """
        pass


class RequiredFieldRule(ValidationRule):
    """Validation rule for required fields."""

    def validate(self, value: Any, context: dict[str, Any] = None) -> str | None:
        if value is None or value == "":
            return f"{self.field_name} is required"
        return None


class EmailValidationRule(ValidationRule):
    """Validation rule for email addresses."""

    def validate(self, value: Any, context: dict[str, Any] = None) -> str | None:
        if not value:
            return None  # Let RequiredFieldRule handle empty values

        try:
            validate_email(value)
            return None
        except ValidationError:
            return f"{self.field_name} must be a valid email address"


class LengthValidationRule(ValidationRule):
    """Validation rule for string length."""

    def __init__(
        self,
        field_name: str,
        min_length: int = None,
        max_length: int = None,
        severity: ValidationSeverity = ValidationSeverity.ERROR,
    ):
        super().__init__(field_name, severity)
        self.min_length = min_length
        self.max_length = max_length

    def validate(self, value: Any, context: dict[str, Any] = None) -> str | None:
        if not isinstance(value, str):
            return f"{self.field_name} must be a string"

        length = len(value)

        if self.min_length is not None and length < self.min_length:
            return (
                f"{self.field_name} must be at least {self.min_length} characters long"
            )

        if self.max_length is not None and length > self.max_length:
            return f"{self.field_name} must be no more than {self.max_length} characters long"

        return None


class RegexValidationRule(ValidationRule):
    """Validation rule using regular expressions."""

    def __init__(
        self,
        field_name: str,
        pattern: str,
        message: str = None,
        severity: ValidationSeverity = ValidationSeverity.ERROR,
    ):
        super().__init__(field_name, severity)
        self.pattern = re.compile(pattern)
        self.message = message or f"{field_name} format is invalid"

    def validate(self, value: Any, context: dict[str, Any] = None) -> str | None:
        if not isinstance(value, str):
            return f"{self.field_name} must be a string"

        if not self.pattern.match(value):
            return self.message

        return None


class NumericRangeRule(ValidationRule):
    """Validation rule for numeric ranges."""

    def __init__(
        self,
        field_name: str,
        min_value: int | float = None,
        max_value: int | float = None,
        severity: ValidationSeverity = ValidationSeverity.ERROR,
    ):
        super().__init__(field_name, severity)
        self.min_value = min_value
        self.max_value = max_value

    def validate(self, value: Any, context: dict[str, Any] = None) -> str | None:
        if not isinstance(value, (int, float)):
            return f"{self.field_name} must be a number"

        if self.min_value is not None and value < self.min_value:
            return f"{self.field_name} must be at least {self.min_value}"

        if self.max_value is not None and value > self.max_value:
            return f"{self.field_name} must be no more than {self.max_value}"

        return None


class CustomValidationRule(ValidationRule):
    """Validation rule using a custom function."""

    def __init__(
        self,
        field_name: str,
        validator_func: Callable[[Any, dict[str, Any]], str | None],
        severity: ValidationSeverity = ValidationSeverity.ERROR,
    ):
        super().__init__(field_name, severity)
        self.validator_func = validator_func

    def validate(self, value: Any, context: dict[str, Any] = None) -> str | None:
        return self.validator_func(value, context or {})


class DataValidator:
    """
    Comprehensive data validator for the application.

    Supports multiple validation rules and provides detailed validation results.
    """

    def __init__(self):
        self.rules: dict[str, list[ValidationRule]] = {}

    def add_rule(self, rule: ValidationRule) -> None:
        """Add a validation rule."""
        if rule.field_name not in self.rules:
            self.rules[rule.field_name] = []
        self.rules[rule.field_name].append(rule)

    def add_rules(self, rules: list[ValidationRule]) -> None:
        """Add multiple validation rules."""
        for rule in rules:
            self.add_rule(rule)

    def validate(
        self, data: dict[str, Any], context: dict[str, Any] = None
    ) -> ValidationResult:
        """
        Validate data against all configured rules.

        Args:
            data: The data to validate
            context: Additional context for validation

        Returns:
            ValidationResult with detailed validation information
        """
        result = ValidationResult(is_valid=True, errors=[], warnings=[], info=[])
        context = context or {}

        # Validate each field that has rules
        for field_name, field_rules in self.rules.items():
            field_value = data.get(field_name)

            for rule in field_rules:
                try:
                    error_message = rule.validate(field_value, context)
                    if error_message:
                        result.add_issue(rule.severity, field_name, error_message)
                except Exception as e:
                    logger.error(f"Validation rule error for field {field_name}: {e}")
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        field_name,
                        f"Validation error: {str(e)}",
                    )

        return result

    def validate_field(
        self, field_name: str, value: Any, context: dict[str, Any] = None
    ) -> ValidationResult:
        """
        Validate a single field.

        Args:
            field_name: The field name to validate
            value: The field value
            context: Additional context for validation

        Returns:
            ValidationResult for the field
        """
        result = ValidationResult(is_valid=True, errors=[], warnings=[], info=[])
        context = context or {}

        if field_name in self.rules:
            for rule in self.rules[field_name]:
                try:
                    error_message = rule.validate(value, context)
                    if error_message:
                        result.add_issue(rule.severity, field_name, error_message)
                except Exception as e:
                    logger.error(f"Validation rule error for field {field_name}: {e}")
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        field_name,
                        f"Validation error: {str(e)}",
                    )

        return result


# Pre-configured validators for common use cases
class UserDataValidator(DataValidator):
    """Validator for user registration and profile data."""

    def __init__(self):
        super().__init__()
        self.add_rules(
            [
                RequiredFieldRule("username"),
                LengthValidationRule("username", min_length=3, max_length=30),
                RegexValidationRule(
                    "username",
                    r"^[a-zA-Z0-9_]+$",
                    "Username can only contain letters, numbers, and underscores",
                ),
                RequiredFieldRule("email"),
                EmailValidationRule("email"),
                RequiredFieldRule("password"),
                LengthValidationRule("password", min_length=8),
                CustomValidationRule("password", self._validate_password_strength),
                LengthValidationRule("first_name", max_length=50),
                LengthValidationRule("last_name", max_length=50),
            ]
        )

    def _validate_password_strength(
        self, password: str, context: dict[str, Any]
    ) -> str | None:
        """Validate password strength."""
        if not password:
            return None

        if not re.search(r"[A-Z]", password):
            return "Password must contain at least one uppercase letter"

        if not re.search(r"[a-z]", password):
            return "Password must contain at least one lowercase letter"

        if not re.search(r"\d", password):
            return "Password must contain at least one number"

        return None


class QuestionnaireDataValidator(DataValidator):
    """Validator for questionnaire response data."""

    def __init__(self):
        super().__init__()
        self.add_rules(
            [
                RequiredFieldRule("question_id"),
                NumericRangeRule("question_id", min_value=1),
                RequiredFieldRule("session_guid"),
                RegexValidationRule(
                    "session_guid",
                    r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
                    "Session GUID must be a valid UUID",
                ),
                LengthValidationRule("text", max_length=1000),
            ]
        )


class ProductDataValidator(DataValidator):
    """Validator for product data."""

    def __init__(self):
        super().__init__()
        self.add_rules(
            [
                RequiredFieldRule("name"),
                LengthValidationRule("name", min_length=1, max_length=255),
                LengthValidationRule("ingredients", max_length=2000),
                LengthValidationRule("insights", max_length=1000),
                LengthValidationRule("special_instruction", max_length=500),
            ]
        )


# Factory function for getting validators
def get_validator(validator_type: str) -> DataValidator:
    """
    Factory function to get pre-configured validators.

    Args:
        validator_type: Type of validator ('user', 'questionnaire', 'product')

    Returns:
        Configured DataValidator instance
    """
    validators = {
        "user": UserDataValidator,
        "questionnaire": QuestionnaireDataValidator,
        "product": ProductDataValidator,
    }

    validator_class = validators.get(validator_type)
    if not validator_class:
        raise ValueError(f"Unknown validator type: {validator_type}")

    return validator_class()
