"""
Backup and recovery system for Cosmetrics AI.

This module provides comprehensive backup strategies, automated backup scheduling,
and recovery procedures for data protection and disaster recovery.
"""

import json
import os
import tarfile
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any

from django.conf import settings
from django.core.management import call_command
from loguru import logger


class BackupType(Enum):
    """Types of backups."""

    FULL = "full"
    INCREMENTAL = "incremental"
    DIFFERENTIAL = "differential"


class BackupStatus(Enum):
    """Backup operation status."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class BackupMetadata:
    """Metadata for backup operations."""

    backup_id: str
    backup_type: BackupType
    timestamp: datetime
    status: BackupStatus
    file_path: str
    size_bytes: int = 0
    duration_seconds: float = 0.0
    error_message: str = ""
    checksum: str = ""

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "backup_id": self.backup_id,
            "backup_type": self.backup_type.value,
            "timestamp": self.timestamp.isoformat(),
            "status": self.status.value,
            "file_path": self.file_path,
            "size_bytes": self.size_bytes,
            "duration_seconds": self.duration_seconds,
            "error_message": self.error_message,
            "checksum": self.checksum,
        }


class BackupStrategy(ABC):
    """Abstract base class for backup strategies."""

    def __init__(self, name: str, backup_dir: str):
        self.name = name
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(parents=True, exist_ok=True)

    @abstractmethod
    def create_backup(self, backup_id: str) -> BackupMetadata:
        """Create a backup and return metadata."""
        pass

    @abstractmethod
    def restore_backup(self, backup_metadata: BackupMetadata) -> bool:
        """Restore from a backup."""
        pass

    def _calculate_checksum(self, file_path: str) -> str:
        """Calculate MD5 checksum of a file."""
        import hashlib

        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()


class DatabaseBackupStrategy(BackupStrategy):
    """Backup strategy for database data."""

    def create_backup(self, backup_id: str) -> BackupMetadata:
        """Create a database backup."""
        start_time = datetime.now()
        backup_file = self.backup_dir / f"db_backup_{backup_id}.sql"

        metadata = BackupMetadata(
            backup_id=backup_id,
            backup_type=BackupType.FULL,
            timestamp=start_time,
            status=BackupStatus.RUNNING,
            file_path=str(backup_file),
        )

        try:
            # Use Django's dumpdata command for database backup
            with open(backup_file, "w") as f:
                call_command("dumpdata", stdout=f, format="json", indent=2)

            # Calculate file size and checksum
            metadata.size_bytes = backup_file.stat().st_size
            metadata.checksum = self._calculate_checksum(str(backup_file))
            metadata.duration_seconds = (datetime.now() - start_time).total_seconds()
            metadata.status = BackupStatus.COMPLETED

            logger.info(f"Database backup completed: {backup_file}")

        except Exception as e:
            metadata.status = BackupStatus.FAILED
            metadata.error_message = str(e)
            logger.error(f"Database backup failed: {e}")

        return metadata

    def restore_backup(self, backup_metadata: BackupMetadata) -> bool:
        """Restore database from backup."""
        try:
            # Verify checksum
            current_checksum = self._calculate_checksum(backup_metadata.file_path)
            if current_checksum != backup_metadata.checksum:
                logger.error("Backup file checksum mismatch")
                return False

            # Use Django's loaddata command for restoration
            call_command("loaddata", backup_metadata.file_path)

            logger.info(f"Database restored from: {backup_metadata.file_path}")
            return True

        except Exception as e:
            logger.error(f"Database restore failed: {e}")
            return False


class MediaBackupStrategy(BackupStrategy):
    """Backup strategy for media files."""

    def create_backup(self, backup_id: str) -> BackupMetadata:
        """Create a media files backup."""
        start_time = datetime.now()
        backup_file = self.backup_dir / f"media_backup_{backup_id}.tar.gz"

        metadata = BackupMetadata(
            backup_id=backup_id,
            backup_type=BackupType.FULL,
            timestamp=start_time,
            status=BackupStatus.RUNNING,
            file_path=str(backup_file),
        )

        try:
            media_root = Path(settings.MEDIA_ROOT)

            if media_root.exists():
                # Create tar.gz archive of media files
                with tarfile.open(backup_file, "w:gz") as tar:
                    tar.add(media_root, arcname="media")

                # Calculate file size and checksum
                metadata.size_bytes = backup_file.stat().st_size
                metadata.checksum = self._calculate_checksum(str(backup_file))
                metadata.duration_seconds = (
                    datetime.now() - start_time
                ).total_seconds()
                metadata.status = BackupStatus.COMPLETED

                logger.info(f"Media backup completed: {backup_file}")
            else:
                metadata.status = BackupStatus.COMPLETED
                metadata.size_bytes = 0
                logger.info("No media files to backup")

        except Exception as e:
            metadata.status = BackupStatus.FAILED
            metadata.error_message = str(e)
            logger.error(f"Media backup failed: {e}")

        return metadata

    def restore_backup(self, backup_metadata: BackupMetadata) -> bool:
        """Restore media files from backup."""
        try:
            # Verify checksum
            current_checksum = self._calculate_checksum(backup_metadata.file_path)
            if current_checksum != backup_metadata.checksum:
                logger.error("Backup file checksum mismatch")
                return False

            # Extract tar.gz archive
            with tarfile.open(backup_metadata.file_path, "r:gz") as tar:
                tar.extractall(path=settings.BASE_DIR)

            logger.info(f"Media files restored from: {backup_metadata.file_path}")
            return True

        except Exception as e:
            logger.error(f"Media restore failed: {e}")
            return False


class ConfigBackupStrategy(BackupStrategy):
    """Backup strategy for configuration files."""

    def create_backup(self, backup_id: str) -> BackupMetadata:
        """Create a configuration backup."""
        start_time = datetime.now()
        backup_file = self.backup_dir / f"config_backup_{backup_id}.tar.gz"

        metadata = BackupMetadata(
            backup_id=backup_id,
            backup_type=BackupType.FULL,
            timestamp=start_time,
            status=BackupStatus.RUNNING,
            file_path=str(backup_file),
        )

        try:
            # Files to backup
            config_files = [
                "requirements.txt",
                "pyproject.toml",
                "pytest.ini",
                ".pre-commit-config.yaml",
                "docker-compose.yml",
                "Dockerfile",
                "cosmetrics_ai/settings/",
                ".env.example",
                "env.sample",
            ]

            # Create tar.gz archive of configuration files
            with tarfile.open(backup_file, "w:gz") as tar:
                for config_file in config_files:
                    file_path = Path(settings.BASE_DIR) / config_file
                    if file_path.exists():
                        tar.add(file_path, arcname=config_file)

            # Calculate file size and checksum
            metadata.size_bytes = backup_file.stat().st_size
            metadata.checksum = self._calculate_checksum(str(backup_file))
            metadata.duration_seconds = (datetime.now() - start_time).total_seconds()
            metadata.status = BackupStatus.COMPLETED

            logger.info(f"Configuration backup completed: {backup_file}")

        except Exception as e:
            metadata.status = BackupStatus.FAILED
            metadata.error_message = str(e)
            logger.error(f"Configuration backup failed: {e}")

        return metadata

    def restore_backup(self, backup_metadata: BackupMetadata) -> bool:
        """Restore configuration from backup."""
        try:
            # Verify checksum
            current_checksum = self._calculate_checksum(backup_metadata.file_path)
            if current_checksum != backup_metadata.checksum:
                logger.error("Backup file checksum mismatch")
                return False

            # Extract tar.gz archive
            with tarfile.open(backup_metadata.file_path, "r:gz") as tar:
                tar.extractall(path=settings.BASE_DIR)

            logger.info(f"Configuration restored from: {backup_metadata.file_path}")
            return True

        except Exception as e:
            logger.error(f"Configuration restore failed: {e}")
            return False


class BackupManager:
    """
    Comprehensive backup manager.

    Coordinates multiple backup strategies and manages backup lifecycle.
    """

    def __init__(self, backup_dir: str = None):
        self.backup_dir = backup_dir or getattr(
            settings, "BACKUP_DIR", "/tmp/cosmetrics_backups"
        )
        self.strategies: dict[str, BackupStrategy] = {}
        self.metadata_file = Path(self.backup_dir) / "backup_metadata.json"

        # Initialize default strategies
        self._initialize_default_strategies()

    def _initialize_default_strategies(self):
        """Initialize default backup strategies."""
        self.add_strategy(
            "database", DatabaseBackupStrategy("database", self.backup_dir)
        )
        self.add_strategy("media", MediaBackupStrategy("media", self.backup_dir))
        self.add_strategy("config", ConfigBackupStrategy("config", self.backup_dir))

    def add_strategy(self, name: str, strategy: BackupStrategy):
        """Add a backup strategy."""
        self.strategies[name] = strategy
        logger.info(f"Added backup strategy: {name}")

    def create_full_backup(self) -> dict[str, BackupMetadata]:
        """Create a full backup using all strategies."""
        backup_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        results = {}

        logger.info(f"Starting full backup: {backup_id}")

        for strategy_name, strategy in self.strategies.items():
            logger.info(f"Running backup strategy: {strategy_name}")
            metadata = strategy.create_backup(f"{backup_id}_{strategy_name}")
            results[strategy_name] = metadata

        # Save metadata
        self._save_backup_metadata(results)

        logger.info(f"Full backup completed: {backup_id}")
        return results

    def restore_backup(self, backup_id: str) -> dict[str, bool]:
        """Restore from a specific backup."""
        metadata_list = self._load_backup_metadata()
        results = {}

        for strategy_name, strategy in self.strategies.items():
            # Find metadata for this strategy and backup ID
            strategy_metadata = None
            for metadata in metadata_list:
                if (
                    metadata.backup_id.startswith(backup_id)
                    and strategy_name in metadata.backup_id
                ):
                    strategy_metadata = metadata
                    break

            if strategy_metadata:
                logger.info(f"Restoring {strategy_name} from backup {backup_id}")
                results[strategy_name] = strategy.restore_backup(strategy_metadata)
            else:
                logger.warning(
                    f"No backup found for strategy {strategy_name} and ID {backup_id}"
                )
                results[strategy_name] = False

        return results

    def list_backups(self, limit: int = 50) -> list[BackupMetadata]:
        """List available backups."""
        metadata_list = self._load_backup_metadata()
        return sorted(metadata_list, key=lambda x: x.timestamp, reverse=True)[:limit]

    def cleanup_old_backups(self, retention_days: int = 30):
        """Clean up backups older than retention period."""
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        metadata_list = self._load_backup_metadata()

        removed_count = 0
        updated_metadata = []

        for metadata in metadata_list:
            if metadata.timestamp < cutoff_date:
                # Remove backup file
                try:
                    if os.path.exists(metadata.file_path):
                        os.remove(metadata.file_path)
                        logger.info(f"Removed old backup: {metadata.file_path}")
                        removed_count += 1
                except Exception as e:
                    logger.error(f"Failed to remove backup {metadata.file_path}: {e}")
                    updated_metadata.append(
                        metadata
                    )  # Keep metadata if file removal failed
            else:
                updated_metadata.append(metadata)

        # Update metadata file
        self._save_backup_metadata_list(updated_metadata)

        logger.info(f"Cleaned up {removed_count} old backups")
        return removed_count

    def _save_backup_metadata(self, metadata_dict: dict[str, BackupMetadata]):
        """Save backup metadata to file."""
        existing_metadata = self._load_backup_metadata()

        # Add new metadata
        for metadata in metadata_dict.values():
            existing_metadata.append(metadata)

        self._save_backup_metadata_list(existing_metadata)

    def _save_backup_metadata_list(self, metadata_list: list[BackupMetadata]):
        """Save metadata list to file."""
        try:
            self.metadata_file.parent.mkdir(parents=True, exist_ok=True)

            with open(self.metadata_file, "w") as f:
                json.dump([m.to_dict() for m in metadata_list], f, indent=2)

        except Exception as e:
            logger.error(f"Failed to save backup metadata: {e}")

    def _load_backup_metadata(self) -> list[BackupMetadata]:
        """Load backup metadata from file."""
        if not self.metadata_file.exists():
            return []

        try:
            with open(self.metadata_file) as f:
                data = json.load(f)

            metadata_list = []
            for item in data:
                metadata = BackupMetadata(
                    backup_id=item["backup_id"],
                    backup_type=BackupType(item["backup_type"]),
                    timestamp=datetime.fromisoformat(item["timestamp"]),
                    status=BackupStatus(item["status"]),
                    file_path=item["file_path"],
                    size_bytes=item.get("size_bytes", 0),
                    duration_seconds=item.get("duration_seconds", 0.0),
                    error_message=item.get("error_message", ""),
                    checksum=item.get("checksum", ""),
                )
                metadata_list.append(metadata)

            return metadata_list

        except Exception as e:
            logger.error(f"Failed to load backup metadata: {e}")
            return []
