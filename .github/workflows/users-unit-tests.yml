name: Users API Unit Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'users/**'
      - 'tests/users/**'
      - 'requirements-test.txt'
      - 'pytest.ini'
      - '.github/workflows/users-unit-tests.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'users/**'
      - 'tests/users/**'
      - 'requirements-test.txt'
      - 'pytest.ini'
      - '.github/workflows/users-unit-tests.yml'
  workflow_dispatch:

env:
  DJANGO_SETTINGS_MODULE: cosmetrics_ai.settings.testing
  DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
  REDIS_URL: redis://localhost:6379/0

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]
        django-version: [4.2, 5.0]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ matrix.python-version }}-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-${{ matrix.python-version }}-
          ${{ runner.os }}-pip-

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libpq-dev \
          postgresql-client \
          gettext \
          libffi-dev \
          libjpeg-dev \
          libpng-dev \
          libwebp-dev \
          zlib1g-dev

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip setuptools wheel
        pip install Django==${{ matrix.django-version }}
        pip install -r requirements.txt
        pip install -r requirements-test.txt

    - name: Verify database connection
      run: |
        python -c "
        import psycopg2
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password='postgres',
            database='test_db'
        )
        print('Database connection successful')
        conn.close()
        "

    - name: Run Django system checks
      run: |
        python manage.py check --deploy

    - name: Create test database
      run: |
        python manage.py migrate --run-syncdb

    - name: Run Users Unit Tests with Coverage
      run: |
        pytest tests/users/ \
          --cov=users \
          --cov-report=html:htmlcov \
          --cov-report=term-missing \
          --cov-report=xml \
          --cov-fail-under=100 \
          --cov-branch \
          --verbose \
          --tb=short \
          --junit-xml=test-results.xml \
          -m "not slow"

    - name: Run Slow Tests (if any)
      run: |
        pytest tests/users/ \
          --cov=users \
          --cov-append \
          --cov-report=html:htmlcov \
          --cov-report=term-missing \
          --cov-report=xml \
          --verbose \
          --tb=short \
          -m "slow" || true

    - name: Generate Coverage Badge
      if: matrix.python-version == '3.11' && matrix.django-version == '5.0'
      run: |
        pip install coverage-badge
        coverage-badge -o coverage.svg

    - name: Upload Coverage Reports
      if: matrix.python-version == '3.11' && matrix.django-version == '5.0'
      uses: actions/upload-artifact@v3
      with:
        name: coverage-reports
        path: |
          htmlcov/
          coverage.xml
          coverage.svg
          test-results.xml

    - name: Upload Coverage to Codecov
      if: matrix.python-version == '3.11' && matrix.django-version == '5.0'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: users-api
        name: users-api-coverage
        fail_ci_if_error: true

    - name: Comment Coverage on PR
      if: github.event_name == 'pull_request' && matrix.python-version == '3.11' && matrix.django-version == '5.0'
      uses: py-cov-action/python-coverage-comment-action@v3
      with:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        MINIMUM_GREEN: 95
        MINIMUM_ORANGE: 90

    - name: Security Scan with Bandit
      if: matrix.python-version == '3.11' && matrix.django-version == '5.0'
      run: |
        pip install bandit[toml]
        bandit -r users/ -f json -o bandit-report.json || true
        bandit -r users/ -f txt

    - name: Safety Check
      if: matrix.python-version == '3.11' && matrix.django-version == '5.0'
      run: |
        pip install safety
        safety check --json --output safety-report.json || true
        safety check

    - name: Upload Security Reports
      if: matrix.python-version == '3.11' && matrix.django-version == '5.0'
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  performance-test:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-test.txt

    - name: Run Performance Tests
      run: |
        pytest tests/users/ \
          --benchmark-only \
          --benchmark-json=benchmark.json \
          -m "performance" || true

    - name: Upload Performance Results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: benchmark.json

  integration-test:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push'
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-test.txt

    - name: Run Integration Tests
      run: |
        pytest tests/users/ \
          --cov=users \
          --cov-report=term-missing \
          --verbose \
          -m "integration"

  notify:
    runs-on: ubuntu-latest
    needs: [test, performance-test, integration-test]
    if: always()
    
    steps:
    - name: Notify Success
      if: needs.test.result == 'success'
      run: |
        echo "✅ Users API tests passed with 100% coverage!"
        echo "All test matrices completed successfully."

    - name: Notify Failure
      if: needs.test.result == 'failure'
      run: |
        echo "❌ Users API tests failed!"
        echo "Please check the test results and fix any issues."
        exit 1
