import base64
import json
import re
import threading
from datetime import datetime as dt
from io import BytesIO
from pathlib import Path

import pandas as pd
from django.contrib.auth.decorators import login_required
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.core.serializers import serialize
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from loguru import logger

from products.models import Product
from src.data_science.recommendations import Recommender
from users.models import UserImage, UserProfile

from .forms import CustomReply, ImageUploadForm, MultipleChoiceForm, SingleChoiceForm
from .models import Answer, Question, Reply

# Create your views here.


def how_it_works(request):
    if request.user.is_anonymous:
        status = "Forbidden"
    else:
        user_profile = get_object_or_404(UserProfile, user=request.user)
        completed = user_profile.completed_questions
        total_questions = Question.objects.count()
        status = (
            "NotStarted"
            if completed == 0
            else "Completed"
            if completed == total_questions
            else "Started"
        )

    return render(request, "questionaire/how_it_works.html", {"status": status})


@login_required
def questions(request):
    if request.method == "GET":
        user_profile = get_object_or_404(UserProfile, user=request.user)
        completed_questions = user_profile.completed_questions
        total_questions = Question.objects.count()

        if completed_questions >= total_questions:
            if not user_profile.recommendations.exists():
                threading.Thread(
                    target=create_recommendations,
                    args=(request.user.id,),
                ).start()
            return redirect("/users/recommendation/")

        current_question = get_object_or_404(Question, position=completed_questions + 1)
        form = get_question_form(request, current_question)
        return render(
            request,
            "questionaire/questions.html",
            {"current_question": current_question, "form": form},
        )

    if request.method == "POST":
        return handle_post_request(request)
    return None


def get_question_form(request, question):
    form_classes = {
        "CR": CustomReply,
        "SC": SingleChoiceForm,
        "MC": MultipleChoiceForm,
        "IU": ImageUploadForm,
    }
    form_class = form_classes.get(question.type)
    return form_class(request.POST, question=question) if form_class else None


def handle_post_request(request):
    reply = request.POST.get("reply")
    user_profile = get_object_or_404(UserProfile, user=request.user)
    current_question = get_object_or_404(
        Question,
        position=user_profile.completed_questions + 1,
    )

    if current_question.type == "CR":
        Reply.objects.create(question=current_question, text=reply, user=request.user)
    elif current_question.type == "IU":
        save_uploaded_image(request)
    else:
        answer = get_object_or_404(Answer, question=current_question, title=reply)
        Reply.objects.create(
            question=current_question,
            answer=answer,
            text=reply,
            user=request.user,
        )

    user_profile.completed_questions += 1
    user_profile.save()
    return redirect("/questionaires/questions/")


def save_uploaded_image(request) -> None:
    captured_image_url = request.POST.get("captured_image")
    captured_image_url = re.sub(r"^data:image/.+;base64,", "", captured_image_url)
    byte_data = base64.b64decode(captured_image_url)
    image_data = BytesIO(byte_data)

    user = request.user
    image_name = f"{user.first_name}_hair.jpeg"

    UserImage.objects.create(
        user=user,
        image=InMemoryUploadedFile(
            image_data,
            "image",
            image_name,
            "image/jpeg",
            byte_data.__sizeof__(),
            None,
        ),
    )
    user_profile = get_object_or_404(UserProfile, user=user)
    user_profile.hair_image.save(image_name, image_data)


def create_recommendations_no_try(user_id) -> None:
    rec = Recommender()
    recommendations = map(str.strip, rec.get_recommendations(user_id))
    rec_products = Product.objects.filter(name__in=recommendations)
    user_profile = get_object_or_404(UserProfile, user_id=user_id)

    user_profile.recommendations.set(rec_products)


def create_recommendations(user_id) -> None:
    try:
        rec = Recommender()
        recommendations = map(str.strip, rec.get_recommendations(user_id))
        rec_products = Product.objects.filter(name__in=recommendations)
        user_profile = get_object_or_404(UserProfile, user_id=user_id)

        user_profile.recommendations.set(rec_products)

    except Exception as e:
        logger.info(f"Error creating recommendations for user {user_id}: {e}")
        raise RuntimeError(
            f"Failed to create recommendations for user {user_id}"
        ) from e


@login_required
def reset_questions(request):
    user = request.user
    Reply.objects.filter(user=user).delete()
    UserProfile.objects.filter(user=user).update(completed_questions=0)
    UserImage.objects.filter(user=user).delete()
    return redirect("/questionaires/questions/")


@login_required
def export_questions(request):
    questions = Question.objects.all()
    questions_json = serialize("json", questions)
    file_path = (
        Path(__file__).resolve().parent.parent / "questionaire/data/questions.json"
    )

    read_raw_contextual_data()

    try:
        with open(file_path, "w") as json_file:
            json.dump(json.loads(questions_json), json_file, indent=4)
        return JsonResponse(
            {
                "status": "success",
                "file_path": str(file_path),
                "time_generated": dt.now().isoformat(),
            },
        )
    except Exception as e:
        return JsonResponse({"status": "error", "message": str(e)})


def read_raw_contextual_data() -> None:
    raw_file_path = (
        Path(__file__).resolve().parent.parent
        / "questionaire/data/contextual_report_raw.csv"
    )
    pd.read_csv(raw_file_path, index_col=0)


def get_report_as_csv(request) -> None:
    Reply.objects.all()
