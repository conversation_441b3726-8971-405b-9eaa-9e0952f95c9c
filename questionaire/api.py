import os
from uuid import UUID

import cloudinary
import cloudinary.uploader
from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.core.exceptions import ObjectDoesNotExist
from django.core.paginator import Paginator
from django.db.models import Max
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from loguru import logger
from ninja import File, Form, Query, Router
from ninja.files import UploadedFile

from cosmetrics_ai.helpers import fetch_latest_session_guid

# Import new router utilities
from helpers.error_handlers import handle_api_error, validate_user_access


def reply_to_schema(reply: "Reply") -> "ReplySchema":
    """Convert a Reply model instance to ReplySchema with proper UUID to string conversion."""
    return ReplySchema(
        user_id=reply.user_id,
        session_guid=str(reply.session_guid),  # Convert UUID to string
        question_id=reply.question_id,
        value=reply.answer.title if reply.answer else None,
        text=reply.text,
        created_at=reply.created_at,
        updated_at=reply.updated_at,
    )


# Import shared schemas
from django.contrib.auth import get_user_model

from helpers.schemas import MessageOut, NotFoundSchema
from users.models import UserImage, UserProfile

User = get_user_model()

# from .utils import handle_api_error
from .models import Answer, Question, Questionaire, Reply
from .schemas import (
    AnswerSchema,
    ImageAssetSchema,
    QuestionCategorySchema,
    QuestionnaireSchema,
    QuestionSchema,
    ReplyCreateResponseSchema,
    ReplyResponseSchema,
    ReplySchema,
    SessionGuidWithCompletionStatus,
    SessionGuidWithTimestamp,
    SessionSchema,
)

router: Router = Router()

UPLOAD_DIR = os.path.join(settings.MEDIA_ROOT, "uploads/replies")
os.makedirs(UPLOAD_DIR, exist_ok=True)

cloudinary.config(
    cloud_name=settings.CLOUDINARY_CLOUD_NAME,
    api_key=settings.CLOUDINARY_API_KEY,
    api_secret=settings.CLOUDINARY_API_SECRET,
)


@router.get(
    "/questionnaires",
    response={200: list[QuestionnaireSchema], 400: MessageOut, 500: MessageOut},
    tags=["Questionnaire"],
)
@handle_api_error
def get_questionnaires(request):
    # No need for manual try-catch - @handle_api_error decorator handles all exceptions
    return 200, Questionaire.objects.all()


@router.get(
    "/questionnaires/{int:questionaire_id}",
    response={
        200: QuestionnaireSchema,
        404: MessageOut,
        400: MessageOut,
        500: MessageOut,
    },
    tags=["Questionnaire"],
)
@handle_api_error
def get_questionnaire(request, questionaire_id: int):
    # No need for manual try-catch - @handle_api_error decorator handles all exceptions
    # get_object_or_404 raises ObjectDoesNotExist which is handled by the decorator
    q = get_object_or_404(Questionaire, id=questionaire_id)
    return 200, q


@router.post(
    "/questionnaires",
    response={200: QuestionnaireSchema, 400: MessageOut, 500: MessageOut},
    tags=["Questionnaire"],
)
# @handle_api_error
def create_questionnaire(request, payload: QuestionnaireSchema):
    return Questionaire.objects.create(**payload.dict())


@router.put(
    "/questionnaires/{int:questionaire_id}",
    response={
        200: QuestionnaireSchema,
        404: NotFoundSchema,
        400: MessageOut,
        500: MessageOut,
    },
    tags=["Questionnaire"],
)
@handle_api_error
def update_questionnaire(request, questionaire_id: int, payload: QuestionnaireSchema):
    questionaire = get_object_or_404(Questionaire, id=questionaire_id)
    for attr, value in payload.dict().items():
        setattr(questionaire, attr, value)
    questionaire.save()
    return questionaire


@router.delete(
    "/questionnaires/{questionaire_id}", response={204: None}, tags=["Questionnaire"]
)
def delete_questionaire(request, questionaire_id: int) -> int:
    questionaire = get_object_or_404(Questionaire, id=questionaire_id)
    questionaire.delete()
    return 204


@router.get("/questions", response=list[QuestionSchema], tags=["Questions"])
@handle_api_error
def list_questions(
    request, page: int = Query(1, gt=0), page_size: int = Query(20, gt=0, le=100)
):
    """List all questions sorted by their position with pagination.

    Args:
        request: The HTTP request object.
        page: Page number (default: 1)
        page_size: Number of items per page (default: 20, max: 100)

    Returns:
        List[QuestionSchema]: A paginated list of question schemas.
    """
    questions: list[Question] = sorted(Question.objects.all(), key=lambda x: x.position)
    paginator: Paginator = Paginator(questions, page_size)
    return paginator.get_page(page)


@router.get("/questions/count", response={200: dict}, tags=["Questions"])
@handle_api_error
def get_questions_count(request):
    """Get the total count of questions in the system.

    Returns:
        dict: A dictionary containing the total count of questions.
    """
    count = Question.objects.count()
    return {"count": count}


@router.post("/replies/attribute-session", response={200: dict}, tags=["Reply"])
@handle_api_error
def attribute_quiz_session(request, session_guid: str, user_id: int):
    """Attribute an anonymous quiz session to a newly authenticated user.

    This endpoint is used when a user completes a quiz anonymously and then
    logs in or registers. It transfers ownership of the quiz session to the user.

    Args:
        request: The HTTP request object.
        session_guid: The session GUID to attribute to the user.
        user_id: The user ID to attribute the session to.

    Returns:
        dict: Success message and session details.
    """
    from django.contrib.auth.models import User

    # Verify user exists
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        raise HttpError(404, f"User with ID {user_id} not found")

    # Check if session exists and has replies
    replies = Reply.objects.filter(session_guid=session_guid)
    if not replies.exists():
        raise HttpError(404, f"No quiz session found with GUID {session_guid}")

    # Check if session is already attributed to a user
    existing_user_replies = replies.exclude(user_id__isnull=True).exclude(user_id=0)
    if existing_user_replies.exists():
        existing_user_id = existing_user_replies.first().user_id
        if existing_user_id == user_id:
            return {
                "message": "Session already attributed to this user",
                "session_guid": session_guid,
                "user_id": user_id,
                "replies_count": replies.count(),
            }
        else:
            raise HttpError(
                400, f"Session is already attributed to user {existing_user_id}"
            )

    # Attribute all replies in this session to the user
    updated_count = replies.update(user_id=user_id)

    logger.info(
        f"Attributed quiz session {session_guid} to user {user_id}. Updated {updated_count} replies."
    )

    return {
        "message": "Quiz session successfully attributed to user",
        "session_guid": session_guid,
        "user_id": user_id,
        "replies_count": updated_count,
    }


@router.get(
    "/questions/{question_id}",
    response={200: QuestionSchema, 404: NotFoundSchema},
    tags=["Questions"],
)
def get_question(request, question_id: int):
    """Retrieve a question by its position ID.

    Args:
        request: The HTTP request object.
        question_id (int): The position ID of the question.

    Returns:
        tuple: A tuple containing the status code and either the question schema or a not found message.
    """
    try:
        return 200, Question.objects.get(position=question_id)
    except Question.DoesNotExist:
        return 404, {"message": "That 'Question' does not exist."}


@router.get(
    "/questions/category/{question_id}",
    response={200: QuestionCategorySchema, 404: NotFoundSchema},
    tags=["Questions"],
)
def get_category_by_question_id(request, question_id: int):
    """Retrieve the category of a question by its position ID.

    Args:
        request: The HTTP request object.
        question_id (int): The position ID of the question.

    Returns:
        tuple: A tuple containing the status code and either the question category schema or a not found message.
    """
    try:
        category = Question.objects.get(position=question_id)
        return 200, category
    except Question.DoesNotExist:
        return 404, {"message": "No such Question hence no Category."}


@router.get("/questions/category/", response=list[QuestionSchema], tags=["Questions"])
def get_category_by_name(request, category_name: str):
    """Retrieve questions by category name.

    Args:
        request: The HTTP request object.
        category_name (str): The name of the category.

    Returns:
        tuple: A tuple containing the status code and either a list of question schemas or a not found message.
    """
    try:
        category_ques = Question.objects.filter(category=category_name.capitalize())
        return 200, category_ques
    except Question.DoesNotExist:
        return 404, {"message": "No such Question hence no Category."}


@router.get("/categories/", response=list[QuestionSchema], tags=["Questions"])
def get_questions_by_category(request, category: str):
    """Retrieve questions by category.

    Args:
        request: The HTTP request object.
        category (str): The category of the questions.

    Returns:
        List[QuestionSchema]: A list of question schemas.
    """
    return Question.objects.filter(category=category)


@router.get(
    "/categories/all", response=list[QuestionCategorySchema], tags=["Questions"]
)
def get_categories(request):
    """Retrieve all distinct categories.

    Args:
        request: The HTTP request object.

    Returns:
        List[QuestionCategorySchema]: A list of distinct question categories.
    """
    categories = Question.objects.values_list("category", flat=True).distinct()
    return [QuestionCategorySchema(category=category) for category in categories]


@router.post("/questions", response=QuestionSchema, tags=["Questions"])
def create_question(request, payload: QuestionSchema):
    """Create a new question.

    Args:
        request: The HTTP request object.
        payload (QuestionSchema): The schema containing the question data.

    Returns:
        tuple: A tuple containing the status code and either the created question schema or an error message.
    """
    try:
        position = payload.position
        if Question.objects.get(position=position):
            return 200, payload

        question = Question.objects.create(**payload.dict())
        return 201, question
    except Exception as e:
        return 200, {"message": str(e)}


@router.put("/questions/{question_id}", response=QuestionSchema, tags=["Questions"])
def update_question(request, question_id: int, payload: QuestionSchema):
    """Update an existing question.

    Args:
        request: The HTTP request object.
        question_id (int): The position ID of the question to be updated.
        payload (QuestionSchema): The schema containing the updated question data.

    Returns:
        QuestionSchema: The updated question schema.
    """
    question = get_object_or_404(Question, position=question_id)
    for attr, value in payload.dict().items():
        setattr(question, attr, value)
    question.save()
    return question


@router.delete("/questions/{question_id}", response=dict, tags=["Questions"])
def delete_question(request, question_id: int):
    """Delete a question by its position ID.

    Args:
        request: The HTTP request object.
        question_id (int): The position ID of the question to be deleted.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    question = get_object_or_404(Question, position=question_id)
    question.delete()
    return {"success": True}


@router.get("/answers", response=list[AnswerSchema], tags=["Answers"])
def list_answers(request):
    return Answer.objects.all()


@router.get("/answers/values", response=list[AnswerSchema], tags=["Answers"])
def list_answers_with_values(request):
    return Answer.objects.filter(value__isnull=False)


@router.post("/answers", response=AnswerSchema, tags=["Answers"])
def create_answer(request, payload: AnswerSchema):
    return Answer.objects.create(**payload.dict())


@router.get("/answers/{answer_id}", response=AnswerSchema, tags=["Answers"])
def get_answer(request, answer_id: int):
    return get_object_or_404(Answer, id=answer_id)


@router.get(
    "/answers/questions/{question_id}", response=list[AnswerSchema], tags=["Answers"]
)
def get_answers_for_question(request, question_id: int):
    try:
        return list(Answer.objects.filter(question=question_id))

    except Answer.DoesNotExist:
        return 404, {"message": f"That 'Answers' not found for {question_id}."}


@router.put("/answers/{answer_id}", response=AnswerSchema, tags=["Answers"])
def update_answer(request, answer_id: int, payload: AnswerSchema):
    answer = get_object_or_404(Answer, id=answer_id)
    for attr, value in payload.dict().items():
        setattr(answer, attr, value)
    answer.save()
    return answer


@router.delete("/answers/{answer_id}", response={204: None}, tags=["Answers"])
def delete_answer(request, answer_id: int) -> int:
    answer = get_object_or_404(Answer, id=answer_id)
    answer.delete()
    return 204


@router.get("/replies", response=list[ReplySchema], tags=["Reply"])
def list_replies(request):
    try:
        replies = Reply.objects.all()
        # Convert Reply objects to ReplySchema with proper UUID to string conversion
        return [reply_to_schema(reply) for reply in replies]
    except Reply.DoesNotExist:
        return 404, {"message": "No Replies in the system."}


@router.get(
    "/replies/user/{int:user_id}/question/{int:position_id}",
    response={200: ReplySchema, 404: MessageOut, 500: MessageOut},
    tags=["Reply"],
)
def get_user_reply_by_position(
    request, user_id: int, position_id: int, session_guid: str | None = None
):
    if not session_guid:
        try:
            session_guid = fetch_latest_session_guid(user_id)
        except ObjectDoesNotExist as e:
            return 404, {"message": str(e)}

    try:
        reply = (
            Reply.objects.filter(user_id=user_id)
            .filter(session_guid=session_guid)
            .filter(question_id=position_id)
            .first()
        )

        if not reply:
            return 404, {
                "message": f"No reply found for user {user_id} and question {position_id}."
            }

        # Convert Reply object to ReplySchema with proper UUID to string conversion
        reply_data = {
            "user_id": reply.user_id,
            "session_guid": str(reply.session_guid),  # Convert UUID to string
            "question_id": reply.question_id,
            "value": reply.answer.title if reply.answer else None,
            "text": reply.text,
            "created_at": reply.created_at,
            "updated_at": reply.updated_at,
        }
        return 200, ReplySchema(**reply_data)

    except Exception as e:
        return 500, {"message": str(e)}


@router.get(
    "/replies/user/{int:user_id}/questions/",
    response={200: list[ReplySchema], 404: MessageOut, 500: MessageOut},
    tags=["Reply"],
)
def get_user_replies_by_list(
    request,
    user_id: int,
    session_guid: str | None = None,
    question_ids: list[int] = Query(...),
):
    if not session_guid:
        try:
            session_guid = fetch_latest_session_guid(user_id)
        except ObjectDoesNotExist as e:
            return 404, {"message": str(e)}

    try:
        replies = (
            Reply.objects.filter(user_id=user_id)
            .filter(session_guid=session_guid)
            .filter(question_id__in=question_ids)
        )

        # Convert Reply objects to ReplySchema with proper UUID to string conversion
        reply_schemas = []
        for reply in replies:
            reply_data = {
                "user_id": reply.user_id,
                "session_guid": str(reply.session_guid),  # Convert UUID to string
                "question_id": reply.question_id,
                "value": reply.answer.title if reply.answer else None,
                "text": reply.text,
                "created_at": reply.created_at,
                "updated_at": reply.updated_at,
            }
            reply_schemas.append(ReplySchema(**reply_data))

        return 200, reply_schemas
    except Reply.DoesNotExist:
        return 404, {
            "message": f"""No replies found for user {user_id} and
            question {question_ids}."""
        }
    except Exception as e:
        return 500, {"message": str(e)}


@router.post("/replies/images", tags=["Reply", "ImageProcessing"])
def create_reply_image(
    request,
    question_id: str = Form(...),
    user_id: str = Form(...),
    session_guid: str = Form(...),
    file: UploadedFile = File(...),
):
    try:
        if not question_id or not user_id or not session_guid:
            return JsonResponse(
                {"error": "question_id, user_id, and sessionId are required"},
                status=400,
            )

        try:
            question_id = int(question_id)
            user_id = int(user_id)
        except ValueError:
            return JsonResponse({"error": "Invalid question_id or user_id"}, status=400)

        try:
            question = Question.objects.get(id=question_id)
        except Question.DoesNotExist:
            return JsonResponse({"error": "Question not found"}, status=400)

        file_url = None

        if file:
            # Upload file to Cloudinary
            upload_result = cloudinary.uploader.upload(
                file,
                folder=f"replies/{user_id}/{session_guid}",  # Organizing by user and session
                resource_type="image",
            )

            file_url = upload_result.get("secure_url")  # Get Cloudinary image URL

        reply = Reply.objects.create(
            text=file_url, question=question, user_id=user_id, session_guid=session_guid
        )

        return JsonResponse(
            {
                "message": "Reply saved successfully",
                "reply": {
                    "id": reply.id,
                    "text": file_url,
                    "session_guid": session_guid,
                },
            },
            status=201,
        )

    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)


@router.post(
    "/replies/image/capture",
    response={201: dict, 400: MessageOut, 403: MessageOut, 500: MessageOut},
    tags=["Reply"],
)
@handle_api_error
# @login_required
def create_reply_image_refactor(request):
    """Create a new reply with an image attachment.

    Args:
        request: The HTTP request object containing POST data and FILES.

    Returns:
        dict: Created reply details including the uploaded image URL.
    """
    try:
        data = request.POST
        file = request.FILES.get("file")
        question_id = data.get("question_id")
        user_id = data.get("user_id")
        session_guid = data.get("session_guid")

        # Validate required fields
        if not all([file, question_id, user_id, session_guid]):
            return 400, {"message": "Missing required fields"}

        # Validate user access TODO: Enable this after testing
        # validate_user_access(request, int(user_id))

        # Validate file type
        if not file.content_type.startswith("image/"):
            return 400, {"message": "Invalid file type. Only images are allowed."}

        # Validate file size (max 5MB)
        if file.size > 5 * 1024 * 1024:
            return 400, {"message": "File size too large. Maximum size is 5MB."}

        question = get_object_or_404(Question, id=question_id)

        # Upload to Cloudinary
        upload_result = cloudinary.uploader.upload(
            file,
            folder="replies",
            resource_type="image",
            allowed_formats=["jpg", "jpeg", "png", "gif"],
            max_bytes=5 * 1024 * 1024,  # 5MB limit
        )

        file_url = upload_result.get("secure_url")

        reply = Reply.objects.create(
            text=file_url, question=question, user_id=user_id, session_guid=session_guid
        )
        logger.info("Reply saved successfully", reply)

        return 201, {
            "message": "Reply saved successfully",
            "reply": {
                "id": reply.id,
                "text": file_url,
                "session_guid": session_guid,
            },
        }

    except Exception as e:
        return 500, {"message": str(e)}


@router.post(
    "/replies",
    response={201: ReplyCreateResponseSchema, 400: MessageOut, 404: MessageOut},
    tags=["Reply"],
)
def create_reply(request, payload: ReplySchema):
    try:
        question = Question.objects.get(id=payload.question_id)
    except Question.DoesNotExist:
        return 404, {"message": "Question not found"}

    # Convert user_id to int if it's a string
    user_id = (
        int(payload.user_id) if isinstance(payload.user_id, str) else payload.user_id
    )

    # Convert session_guid to UUID if it's a string
    import uuid

    try:
        if isinstance(payload.session_guid, str):
            session_guid = uuid.UUID(payload.session_guid)
        else:
            session_guid = payload.session_guid
    except ValueError:
        # If it's not a valid UUID, generate a new one
        session_guid = uuid.uuid4()

    try:
        reply = Reply.objects.create(
            text=payload.text,
            question=question,
            user_id=user_id,
            session_guid=session_guid,
        )

        # Convert to response format with string session_guid
        response_data = {
            "id": reply.id,
            "user_id": reply.user_id,
            "session_guid": str(reply.session_guid),
            "question_id": reply.question.id,
            "value": payload.value,
            "text": reply.text,
            "created_at": reply.created_at,
            "updated_at": reply.updated_at,
        }

        return 201, response_data
    except Exception as e:
        return 400, {"message": f"Error creating reply: {str(e)}"}


@router.put(
    "/replies/{int:reply_id}",
    response={200: ReplySchema, 403: MessageOut, 404: NotFoundSchema},
    tags=["Reply"],
)
@handle_api_error
# @login_required
def update_reply(request, reply_id: int, payload: ReplySchema):
    """Update an existing reply.

    Args:
        request: The HTTP request object.
        reply_id: ID of the reply to update.
        payload: Updated reply data.

    Returns:
        ReplySchema: The updated reply.
    """
    reply = get_object_or_404(Reply, id=reply_id)
    # validate_user_access(request, reply.user_id) TODO: Enable this after testing

    for attr, value in payload.dict().items():
        setattr(reply, attr, value)
    reply.save()
    return 200, reply_to_schema(reply)


@router.delete(
    "/replies/{reply_id}",
    response={204: None, 403: MessageOut, 404: NotFoundSchema},
    tags=["Reply"],
)
@handle_api_error
@login_required
def delete_reply(request, reply_id: int) -> int:
    """Delete a reply.

    Args:
        request: The HTTP request object.
        reply_id: ID of the reply to delete.

    Returns:
        int: 204 status code on successful deletion.
    """
    reply = get_object_or_404(Reply, id=reply_id)
    validate_user_access(request, reply.user_id)
    reply.delete()
    return 204


@router.get(
    "/replies/all/{int:user_id}",
    response={
        200: list[ReplySchema],
        403: MessageOut,
        404: NotFoundSchema,
        500: MessageOut,
    },
    tags=["Reply"],
)
@handle_api_error
# @login_required
def get_replies_by_user_id(
    request,
    user_id: int,
    page: int = Query(1, gt=0),
    page_size: int = Query(20, gt=0, le=100),
):
    """Get paginated replies for a specific user.

    Args:
        request: The HTTP request object.
        user_id: ID of the user whose replies to fetch.
        page: Page number (default: 1)
        page_size: Number of items per page (default: 20, max: 100)

    Returns:
        List[ReplySchema]: A paginated list of replies.
    """
    # validate_user_access(request, user_id)
    replies = (
        Reply.objects.filter(user_id=user_id)
        .select_related("question")
        .order_by("-created_at")
    )
    paginator = Paginator(replies, page_size)
    page_obj = paginator.get_page(page)

    # Convert Reply objects to ReplySchema with proper UUID to string conversion
    reply_schemas = []
    for reply in page_obj:
        reply_data = {
            "user_id": reply.user_id,
            "session_guid": str(reply.session_guid),  # Convert UUID to string
            "question_id": reply.question_id,
            "value": reply.answer.title if reply.answer else None,
            "text": reply.text,
            "created_at": reply.created_at,
            "updated_at": reply.updated_at,
        }
        reply_schemas.append(ReplySchema(**reply_data))

    return 200, reply_schemas


@router.get(
    "/replies/sessions/user/{int:user_id}/session/{str:session_guid}",
    response={
        200: SessionSchema,
        403: MessageOut,
        404: NotFoundSchema,
        500: MessageOut,
    },
    tags=["Reply"],
)
@handle_api_error
# @login_required
def get_user_replies_by_session(request, user_id: int, session_guid: UUID):
    """
    Get all replies for a specific session.

    Args:
        request: The HTTP request object.
        user_id: ID of the user whose replies to fetch.
        session_guid: UUID of the session.

    Returns:
        SessionSchema: Session data including all associated replies.
    """
    try:
        replies = (
            Reply.objects.filter(user_id=user_id, session_guid=session_guid)
            .select_related("question")
            .order_by("created_at")
        )
        # Validate user has access to at least one reply in the session
        # first_reply = replies.first()
        # validate_user_access(request, first_reply.user_id)

        data = [reply_to_schema(reply) for reply in replies]
        logger.info(f"\n\tDATA RETURNED: {data}\n")
        return 200, {"guid": session_guid, "data": data}
    except Reply.DoesNotExist:
        return 404, {"message": f"No replies found for session_guid: {session_guid}"}
    except Exception as e:
        return 500, {"message": str(e)}


@router.get(
    "/replies/sessions/{str:session_guid}",
    response={
        200: SessionSchema,
        400: MessageOut,
        404: NotFoundSchema,
        500: MessageOut,
    },
    tags=["Reply"],
)
@handle_api_error
# @login_required
def get_replies_by_session_optional(
    request, session_guid: UUID, question_ids: list[int] = Query(...)
):
    """Get replies with question_ids for a specific session.

    Args:
        request: The HTTP request object.
        session_guid: UUID of the session.
        question_ids: List of question IDs to filter by.

    Returns:
        SessionSchema: Session data including all associated replies.
    """
    try:
        replies = (
            Reply.objects.filter(session_guid=session_guid)
            .filter(question_id__in=question_ids)
            .select_related("question")
            .order_by("created_at")
        )
        # Validate user has access to at least one reply in the session
        # first_reply = replies.first()
        # validate_user_access(request, first_reply.user_id)

        data = [reply_to_schema(reply) for reply in replies]
        return 200, {"guid": session_guid, "data": data}
    except Reply.DoesNotExist:
        return 404, {"message": f"No replies found for session_guid: {session_guid}"}
    except Exception as e:
        return 500, {"message": str(e)}


@router.get(
    "/replies/sessions/user/{user_id}",
    response={200: list[SessionGuidWithTimestamp], 404: MessageOut, 500: MessageOut},
    tags=["Reply"],
)
@handle_api_error
def get_user_sessions_with_timestamps(request, user_id: int):
    # Check if user exists - will raise ObjectDoesNotExist if not found
    user = User.objects.get(id=user_id)
    logger.info(f"User exists - {user}")

    # Get sessions for the user
    sessions = (
        Reply.objects.filter(user_id=user_id)
        .order_by("-updated_at")
        .values("session_guid")
        .annotate(updated_at=Max("updated_at"))
        .order_by("-updated_at")
    )
    return 200, list(sessions)


@router.get(
    "/replies/sessions/user/{user_id}/with-completion",
    response={
        200: list[SessionGuidWithCompletionStatus],
        404: MessageOut,
        500: MessageOut,
    },
    tags=["Reply"],
)
@handle_api_error
def get_user_sessions_with_completion_status(request, user_id: int):
    """Get user sessions with completion status information."""
    # Check if user exists - will raise ObjectDoesNotExist if not found
    user = User.objects.get(id=user_id)
    logger.info(f"User exists - {user}")

    # Get total questions count
    total_questions = Question.objects.count()
    logger.info(f"Total questions in system: {total_questions}")

    # Get sessions for the user with unique question counts (not just reply counts)
    from django.db.models import Count

    sessions = (
        Reply.objects.filter(user_id=user_id)
        .values("session_guid")
        .annotate(
            updated_at=Max("updated_at"),
            replies_count=Count("id"),
            unique_questions_count=Count("question_id", distinct=True),
        )
        .order_by("-updated_at")
    )

    # Add completion status to each session
    sessions_with_completion = []
    for session in sessions:
        # Use unique questions count for completion, not total replies
        is_completed = session["unique_questions_count"] >= total_questions
        session_data = {
            "session_guid": session["session_guid"],
            "updated_at": session["updated_at"],
            "is_completed": is_completed,
            "replies_count": session[
                "unique_questions_count"
            ],  # Use unique questions for consistency
            "total_questions": total_questions,
        }
        sessions_with_completion.append(session_data)
        logger.info(
            f"Session {session['session_guid']}: {session['unique_questions_count']}/{total_questions} unique questions answered, completed: {is_completed}"
        )

    return 200, sessions_with_completion


# Duplicate route removed - using the version above with @handle_api_error decorator


@router.get(
    "/replies/session/{str:session_guid}/prompt",
    response={200: ReplyResponseSchema, 404: MessageOut, 500: MessageOut},
    tags=["Reply"],
)
def get_user_replies_as_prompt(request, session_guid: str):
    question_ids: list[int] = [1, 2, 6, 8, 9, 37]
    try:
        # Get all replies for the session and specific questions, ordered by question_id and created_at
        # Use database-agnostic approach instead of PostgreSQL-specific distinct
        all_replies = (
            Reply.objects.filter(
                session_guid=session_guid,
                question_id__in=question_ids,
            )
            .select_related("question")
            .order_by("question_id", "created_at")
        )

        # Get the latest reply for each question (database-agnostic)
        seen_questions = set()
        replies = []
        for reply in all_replies:
            if reply.question_id not in seen_questions:
                replies.append(reply)
                seen_questions.add(reply.question_id)

        # Optionally validate access using the first reply
        # first_reply = replies.first()
        # validate_user_access(request, first_reply.user_id)

        data = [reply_to_schema(reply) for reply in replies]
        return 200, {"guid": session_guid, "data": data}

    except Reply.DoesNotExist:
        return 404, {"message": f"No replies found for session_guid: {session_guid}"}
    except Exception as e:
        return 500, {"message": str(e)}


# --------------------------------------------------------------------------- #
# DELETE — all replies for a given *user_id*
# --------------------------------------------------------------------------- #
@router.delete(
    "/replies/sessions/user/{int:user_id}",
    response={
        204: None,  # No Content on success
        403: MessageOut,  # Forbidden (e.g. wrong user)
        404: NotFoundSchema,  # Nothing to delete
        500: MessageOut,  # Unexpected error
    },
    tags=["Reply"],
)
@handle_api_error
def delete_replies_by_user(request, user_id: int):
    """
    Delete **all** replies created by *user_id* across every session.

    Returns
    -------
    • **204** - when at least one row was removed.
    • **404** - when no replies matched the filter.
    • **403** - when `validate_user_access` (if you wire it in) denies the call.
    """
    try:
        qs = Reply.objects.filter(user_id=user_id)
        # validate_user_access(request, user_id)  # uncomment if you enforce auth

        deleted_count, _ = qs.delete()
        if deleted_count == 0:
            return 404, {"message": f"No replies found for user_id {user_id}"}

        logger.info(f"Deleted {deleted_count} replies for user {user_id}")
        return 204, None  # Django Ninja converts this to an empty HTTP 204
    except Exception as exc:
        logger.exception(exc)
        return 500, {"message": str(exc)}


# --------------------------------------------------------------------------- #
# DELETE — all replies for a given *session_guid*
# --------------------------------------------------------------------------- #
@router.delete(
    "/replies/sessions/session/{str:session_guid}",
    response={
        204: None,
        403: MessageOut,
        404: NotFoundSchema,
        500: MessageOut,
    },
    tags=["Reply"],
)
@handle_api_error
def delete_replies_by_session(request, session_guid: UUID):
    """
    Delete **every** reply that belongs to *session_guid* (all users).

    Response codes mirror the *delete_replies_by_user* endpoint.
    """
    try:
        qs = Reply.objects.filter(session_guid=session_guid)

        # If you need per‑user security, pull the first row
        # first_reply = qs.first()
        # validate_user_access(request, first_reply.user_id)

        deleted_count, _ = qs.delete()
        if deleted_count == 0:
            return 404, {"message": f"No replies found for session {session_guid}"}

        logger.info(f"Deleted {deleted_count} replies for session {session_guid}")
        return 204, None
    except Exception as exc:
        logger.exception(exc)
        return 500, {"message": str(exc)}


@router.get(
    "/replies/images/user/{int:user_id}",
    response={200: list[ImageAssetSchema], 404: MessageOut, 500: MessageOut},
    tags=["Reply"],
)
def get_user_uploaded_images(request, user_id: int):
    replies = (
        Reply.objects.filter(user_id=user_id, question_id__in=[11, 12, 13])
        .exclude(text__isnull=True)
        .exclude(text__exact="")
        .values("question_id", "text")
    )
    return [{"question_id": r["question_id"], "image_url": r["text"]} for r in replies]


@router.get(
    "/replies/images/user/{int:user_id}/session/{str:session_guid}",
    response=list[ImageAssetSchema],
    tags=["Reply"],
)
@handle_api_error
def get_user_session_images(request, user_id: int, session_guid: str):
    """Get uploaded images for a specific user session.

    Args:
        request: The HTTP request object.
        user_id: The user ID.
        session_guid: The session GUID.

    Returns:
        List[ImageAssetSchema]: List of uploaded images for the session.
    """
    replies = (
        Reply.objects.filter(
            user_id=user_id,
            session_guid=session_guid,
            question_id__in=[11, 12, 13],  # Photo upload questions
        )
        .exclude(text__isnull=True)
        .exclude(text__exact="")
        .values("question_id", "text", "created_at")
        .order_by("question_id")
    )

    return [
        {
            "question_id": r["question_id"],
            "image_url": r["text"],
            "uploaded_at": r["created_at"],
        }
        for r in replies
    ]


@router.get(
    "/replies/images/user/{int:user_id}/session/{str:session_guid}/count",
    response={200: dict},
    tags=["Reply"],
)
@handle_api_error
def get_user_session_images_count(request, user_id: int, session_guid: str):
    """Get count of uploaded images for a specific user session.

    Args:
        request: The HTTP request object.
        user_id: The user ID.
        session_guid: The session GUID.

    Returns:
        dict: Count of uploaded images and completion status.
    """
    count = (
        Reply.objects.filter(
            user_id=user_id, session_guid=session_guid, question_id__in=[11, 12, 13]
        )
        .exclude(text__isnull=True)
        .exclude(text__exact="")
        .count()
    )

    return {"count": count, "has_photos": count > 0, "is_complete": count >= 3}


@router.get(
    "/replies/user/{int:user_id}/with-completion",
    response=list[dict],
    tags=["Reply"],
)
@handle_api_error
def get_user_sessions_with_completion(request, user_id: int):
    """Get all quiz sessions for a user with completion status.

    Args:
        request: The HTTP request object.
        user_id: The user ID.

    Returns:
        List[dict]: List of sessions with completion status.
    """
    from django.db.models import Count, Max

    # Get total questions for completion calculation
    total_questions = Question.objects.count()

    # Get sessions for the user with unique question counts (not just reply counts)
    sessions = (
        Reply.objects.filter(user_id=user_id)
        .values("session_guid")
        .annotate(
            updated_at=Max("updated_at"),
            replies_count=Count("id"),
            unique_questions_count=Count("question_id", distinct=True),
        )
        .order_by("-updated_at")
    )

    # Add completion status to each session
    sessions_with_completion = []
    for session in sessions:
        # Use unique questions count for completion, not total replies
        is_completed = session["unique_questions_count"] >= total_questions
        session_data = {
            "session_guid": session["session_guid"],
            "updated_at": session["updated_at"],
            "is_completed": is_completed,
            "replies_count": session[
                "unique_questions_count"
            ],  # Use unique questions for consistency
            "total_questions": total_questions,
        }
        sessions_with_completion.append(session_data)
        logger.info(
            f"Session {session['session_guid']}: {session['unique_questions_count']}/{total_questions} unique questions answered, completed: {is_completed}"
        )

    return sessions_with_completion


@router.post("/reset-questions/", tags=["Reset"])
@login_required
def reset_questions(request):
    user = request.user
    Reply.objects.filter(user=user).delete()
    user_profile = get_object_or_404(UserProfile, user=user)
    user_profile.completed_questions = 0
    UserImage.objects.filter(user=user).delete()
    user_profile.save()
    return {"message": "Questions reset successfully."}
