from django.db.models.signals import post_save
from django.dispatch import receiver
from loguru import logger

from questionaire.models import Question, Reply
from reporter.services import Reporter
from users.models import UserProfile


@receiver(post_save, sender=Reply)
def auto_generate_report(sender, instance, **kwargs):
    logger.info(f"\n\tAuto-generating report for user {instance.user_id}\n")
    user = instance.user
    user_profile = UserProfile.objects.get(user=user)

    total_questions = Question.objects.count()
    if user_profile.completed_questions >= total_questions:
        try:
            Reporter().create_report(user.id, instance.session_guid)
        except Exception:
            # Optionally log error here
            pass


@receiver(post_save, sender=Reply)
def update_questionnaire_completion(sender, instance, **kwargs):
    logger.info(f"\n\tUpdating questionnaire completion for user {instance.user_id}\n")
    user = instance.user
    total_questions = Question.objects.count()
    user_replies = (
        Reply.objects.filter(user=user).values_list("question_id", flat=True).distinct()
    )

    if len(user_replies) >= total_questions:
        profile, created = UserProfile.objects.get_or_create(user=user)
        if not profile.questionaire_completed:
            profile.questionaire_completed = True
            profile.save()
            logger.info(
                f"\n\tQuestionnaire completion status updated for user {instance.user_id}\n"
            )
