# Generated by Django 5.2 on 2025-04-08 14:16

import uuid

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("questionaire", "0012_alter_reply_session_guid"),
    ]

    operations = [
        migrations.AddField(
            model_name="reply",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="reply",
            name="session_guid",
            field=models.UUIDField(
                default=uuid.UUID("4058957a-3ac8-4df9-9bca-a475c52143b5"),
                editable=False,
            ),
        ),
    ]
