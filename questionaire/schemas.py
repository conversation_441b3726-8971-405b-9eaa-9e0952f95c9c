from datetime import datetime
from uuid import UUID

from ninja import ModelSchema, Schema

# Import shared schemas to avoid duplication
from .models import Answer, Question, Questionaire, Reply


class QuestionnaireSchema(ModelSchema):
    """
    Represents the full schema of a saved Questionnaire.
    """

    class Meta:
        model: type[Questionaire] = Questionaire
        fields = "__all__"


class QuestionSchema(ModelSchema):
    """
    Represents a single question within a questionnaire.
    """

    class Meta:
        model: type[Question] = Question
        fields = "__all__"


class AnswerSchema(ModelSchema):
    """
    Represents a possible answer to a question.
    """

    class Meta:
        model: type[Answer] = Answer
        fields = "__all__"


class AnswerScoreSchema(ModelSchema):
    class Meta:
        model: type[Answer] = Answer
        fields: list[str] = ["score"]


class ReplyDefaultSchema(ModelSchema):
    """
    Represents the raw Reply model as stored in the database.
    """

    class Meta:
        model: type[Reply] = Reply
        fields = "__all__"


class ReplySchema(Schema):
    user_id: int | str  # Accept both int and string from frontend
    session_guid: str  # Accept string instead of UUID for flexibility
    question_id: int
    value: str | None = None
    text: str | None = None
    created_at: datetime | None = datetime.now()
    updated_at: datetime | None = datetime.now()


# ErrorSchema is similar to MessageOut - use MessageOut from helpers/schemas.py instead


class ReplyResponseSchema(Schema):
    guid: str
    data: list[ReplySchema]


class ReplyCreateResponseSchema(Schema):
    """Response schema for creating a single reply"""

    id: int
    user_id: int
    session_guid: str
    question_id: int
    value: str | None = None
    text: str | None = None
    created_at: datetime
    updated_at: datetime


class QuestionCategorySchema(Schema):
    """
    Used to group questions under a specific category name.
    """

    category: str


class QuestionsListSchema(Schema):
    """
    Represents a list of QuestionSchema objects.
    """

    questions: list[QuestionSchema]


class ImageAssetSchema(Schema):
    """
    Associates an image URL with a specific question.
    """

    question_id: int
    image_url: str
    uploaded_at: datetime | None = None


class SessionGuidWithTimestamp(Schema):
    """
    Provides a session GUID along with its last update timestamp.
    """

    session_guid: UUID
    updated_at: datetime


class SessionGuidWithCompletionStatus(Schema):
    """
    Provides a session GUID along with its last update timestamp and completion status.
    """

    session_guid: UUID
    updated_at: datetime
    is_completed: bool
    replies_count: int
    total_questions: int


class SessionSchema(Schema):
    """
    Represents a full session with its GUID and the list of submitted replies.
    """

    guid: UUID
    data: list[ReplySchema]


class ReplyIn(Schema):
    """
    Input schema for creating a new reply.
    """

    question_id: int
    user_id: int
    text: str | None = None
    answer_id: int | None = None


class ReplyOut(Schema):
    """
    Output schema for displaying a stored reply.
    """

    id: int
    session_guid: UUID
    question_id: int
    user_id: int
    updated_at: datetime
    text: str | None = None
    answer_id: int | None = None

    class Meta:
        from_attributes = True


# MessageOut and NotFoundSchema moved to helpers/schemas.py


class RecommendationNameSchema(Schema):
    """
    A list of recommended product names.
    """

    names: list[str]
