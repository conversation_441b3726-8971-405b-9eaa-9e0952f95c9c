import base64
import json
import os
import re
import threading
from datetime import datetime as dt
from io import BytesIO
from pathlib import Path

import pandas as pd
from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required
from django.core.serializers import serialize

User = get_user_model()
from django.http import JsonResponse
from django.shortcuts import redirect, render

from products.models import Product
from src.data_science.recommendations import Recommender
from users.models import UserImage, UserProfile

from .forms import CustomReply, ImageUploadForm, MultipleChoiceForm, SingleChoiceForm
from .models import Answer, Question, Reply


# Create your views here.
def how_it_works(request):
    if request.user.is_anonymous:
        status = "Forbidden"
    else:
        user_profile = UserProfile.objects.get(user=request.user)
        if user_profile.completed_questions == 0:
            status = "NotStarted"
        elif user_profile.completed_questions == Question.objects.count():
            status = "Completed"
        else:
            status = "Started"
    return render(request, "questionaire/how_it_works.html", {"status": status})


@login_required
def questions(request):
    # get_report_as_csv(request)

    if request.method == "GET":
        user_profile = UserProfile.objects.get(user=request.user)

        if user_profile.completed_questions >= Question.objects.count():
            if not user_profile.recommendations.all():
                threading.Thread(
                    target=create_recommendations,
                    args=(request.user.id,),
                ).start()
            return redirect("/users/recommendation/")

        current_question = Question.objects.get(
            position=user_profile.completed_questions + 1,
        )
        context = {"current_question": current_question}
        if current_question.type == "CR":
            context["form"] = CustomReply(request.GET, question=current_question)
        elif current_question.type == "SC":
            context["form"] = SingleChoiceForm(request.GET, question=current_question)
        elif current_question.type == "MC":
            context["form"] = MultipleChoiceForm(request.GET, question=current_question)
        elif current_question.type == "IU":
            context["form"] = ImageUploadForm(request.GET, question=current_question)
        return render(request, "questionaire/questions.html", context)

    if request.method == "POST":
        reply = request.POST.get("reply")
        user_profile = UserProfile.objects.get(user=request.user)
        current_question = Question.objects.get(
            position=user_profile.completed_questions + 1,
        )

        if current_question.type == "CR":
            new_reply = Reply(question=current_question, text=reply, user=request.user)
            new_reply.save()
        elif current_question.type == "IU":
            captured_image_url = request.POST.get("captured_image")
            captured_image_url = re.sub(
                "^data:image/.+;base64,",
                "",
                captured_image_url,
            )

            byte_data = base64.b64decode(captured_image_url)
            image_data = BytesIO(byte_data)

            # TOD0: decide the correct place to save the image and update
            new_image = UserImage(user=request.user)
            new_image.image.save(f"{request.user.first_name}_hair.jpeg", image_data)

            user_profile = UserProfile.objects.filter(user=request.user).first()
            user_profile.hair_image.save(
                f"{request.user.first_name}_hair.jpeg",
                image_data,
            )

        else:
            answer = Answer.objects.filter(question=current_question).get(title=reply)
            new_reply = Reply(
                question=current_question,
                answer=answer,
                text=reply,
                user=request.user,
            )
            new_reply.save()

        user_profile.completed_questions += 1
        user_profile.save()
        return redirect("/questionaires/questions/")
    return None


def create_recommendations(user_id: int) -> None:
    # rec = NewRecommender()
    rec = Recommender()
    recommendations = rec.get_recommendations(user_id)
    recommendations = (x.strip() for x in recommendations)
    rec_products = Product.objects.filter(name__in=recommendations)
    user = User.objects.get(id=user_id)
    user_profile = UserProfile.objects.get(user=user)
    for product in rec_products:
        user_profile.recommendations.add(product)


@login_required
def reset_questions(request):
    user = request.user
    Reply.objects.filter(user=user).delete()
    user_profile = UserProfile.objects.get(user=user)
    user_profile.completed_questions = 0
    user_images = UserImage.objects.filter(user=user)
    user_images.delete()
    user_profile.save()
    return redirect("/questionaires/questions/")


@login_required
def export_questions(request):
    """Retrieve all questions in the questionnaire and save as JSON file in data directory"""
    # Retrieve all questions
    questions: object = Question.objects.all()

    # Serialize the questions to JSON
    questions_json = serialize("json", questions)

    # print(questions_json)

    # Define the path where you want to save the JSON file
    BASE_DIR = Path(__file__).resolve().parent.parent

    file_path = os.path.join(BASE_DIR / "questionaire/data/questions.json")

    read_raw_contextual_data(request)

    # Save the JSON data to a file
    try:
        with open(file_path, "w") as json_file:
            json.dump(json.loads(questions_json), json_file, indent=4)
        # TODO: Test contextual report INGESTION

        # Return a JsonResponse indicating success
        return JsonResponse(
            {"status": "success", "file_path": file_path, "time_generated": dt.now()},
        )
    except Exception:
        raise


def read_raw_contextual_data(request) -> None:
    BASE_DIR = Path(__file__).resolve().parent.parent

    raw_file_path = os.path.join(
        BASE_DIR / "questionaire/data/contextual_report_raw.csv",
    )

    pd.read_csv(raw_file_path, index_col=0)

    # Step 2: Transpose the DataFrame

    # Step 3: Display the transposed DataFrame


def get_report_as_csv(request) -> None:
    Reply.objects.all()
