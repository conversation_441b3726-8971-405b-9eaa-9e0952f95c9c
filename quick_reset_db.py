#!/usr/bin/env python
"""
Quick Database Reset Script

A simplified version of the database reset and population script for quick use.
This script will:
1. Clear all data from the database
2. Populate users, products, and questions
3. Verify the population

Usage:
    python quick_reset_db.py
"""

import subprocess
import sys
from pathlib import Path


def run_command(command, description):
    """Run a command and return success status."""
    print(f"🔄 {description}...")

    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=Path(__file__).parent,
            capture_output=True,
            text=True,
            timeout=300,
        )

        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed")
            if result.stderr:
                print(f"Error: {result.stderr.strip()}")
            return False

    except Exception as e:
        print(f"❌ {description} failed: {e}")
        return False


def main():
    """Main function to reset and populate database."""
    print("🚀 Starting quick database reset and population...")

    # Check if we're in the right directory
    if not Path("manage.py").exists():
        print(
            "❌ Error: manage.py not found. Please run this script from the Django project root."
        )
        sys.exit(1)

    steps = [
        ("python clear_database.py", "Clearing database"),
        ("python manage.py makemigrations", "Making migrations"),
        ("python manage.py migrate", "Applying migrations"),
        ("python manage.py populate_users", "Populating users"),
        ("python manage.py populate_default_products", "Populating products"),
        ("python manage.py populate_default_questions", "Populating questions"),
        ("python verify_database.py", "Verifying population"),
    ]

    for command, description in steps:
        if not run_command(command, description):
            print(f"\n❌ Process failed at step: {description}")
            sys.exit(1)
        print()  # Add spacing

    print("🎉 Database reset and population completed successfully!")
    print("\n📊 Summary:")
    print("🔄 Migrations: Applied")
    print("👥 Users: 26")
    print("🛍️  Products: 49")
    print("❓ Questions: 46")
    print("📋 Questionnaires: 1")


if __name__ == "__main__":
    main()
