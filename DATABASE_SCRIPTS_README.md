# Database Management Scripts

This directory contains scripts to manage the database for the Cosmetrics AI project.

## 📋 Available Scripts

### 1. `reset_and_populate_db.py` - Full-Featured Script

The comprehensive database reset and population script with advanced options.

**Features:**
- ✅ Database clearing with safety checks
- ✅ Django migrations (makemigrations + migrate)
- ✅ User population (26 test users)
- ✅ Product population (49 hair care products)
- ✅ Question population (46 quiz questions)
- ✅ Population verification
- ✅ Detailed logging and error handling
- ✅ Dry-run mode for testing
- ✅ Verbose output option
- ✅ Skip clearing option

**Usage:**
```bash
# Basic usage
python reset_and_populate_db.py

# Dry run (see what would happen without doing it)
python reset_and_populate_db.py --dry-run

# Verbose output
python reset_and_populate_db.py --verbose

# Skip database clearing (only populate)
python reset_and_populate_db.py --skip-clear

# Get help
python reset_and_populate_db.py --help
```

### 2. `quick_reset_db.py` - Simple Script

A simplified version for quick database reset and population.

**Usage:**
```bash
python quick_reset_db.py
```

### 3. Helper Scripts

#### `clear_database.py`
Clears all data from the database while preserving superusers.

```bash
python clear_database.py
```

#### `verify_database.py`
Verifies that the database has been populated correctly.

```bash
python verify_database.py
```

## 🎯 What Gets Populated

### Users (26 total)
- Test users with realistic names and email addresses
- Passwords: `Password123!` for all test users
- Includes users like Alice Anderson, Bob Brown, Carol Davis, etc.

### Products (49 total)
- Hair care products (shampoos, conditioners, treatments)
- Real product names from brands like Shea Moisture, As I Am, etc.
- Proper categorization and pricing

### Questions (46 total)
- Complete diagnostic questionnaire
- Multiple question types:
  - **SC**: Single Choice
  - **MC**: Multiple Choice  
  - **CR**: Custom Reply (text input)
  - **IU**: Image Upload
- Covers hair care, health, lifestyle, and preferences

## 🔧 Technical Details

### Database Models Affected
- `User` - Django auth users
- `UserProfile` - User profile information
- `Product` - Hair care products
- `Category` - Product categories
- `Question` - Quiz questions
- `Answer` - Question answer options
- `Questionaire` - Quiz container
- `Reply` - User responses (cleared but not populated)

### Safety Features
- **Preserves superusers** - Admin accounts are never deleted
- **Idempotent operations** - Safe to run multiple times
- **Error handling** - Graceful failure with detailed error messages
- **Verification** - Confirms population was successful

## 🚀 Quick Start

For most use cases, simply run:

```bash
python quick_reset_db.py
```

This will:
1. Clear all existing data (except superusers)
2. Run Django migrations to ensure schema is up to date
3. Populate 26 users, 49 products, and 46 questions
4. Verify the population was successful

## 📊 Expected Results

After successful execution, you should have:

```
📊 Database Population Summary:
👥 Users: 26
🛍️  Products: 49
❓ Questions: 46
📋 Questionnaires: 1
✅ Database population verification successful!
```

## ⚠️ Important Notes

1. **Backup First**: These scripts will delete existing data. Make sure to backup any important data before running.

2. **Superuser Safety**: Superuser accounts are preserved during database clearing.

3. **Development Only**: These scripts are intended for development and testing environments.

4. **Dependencies**: Make sure you're in the Django project root directory with `manage.py` present.

## 🐛 Troubleshooting

### Common Issues

**"manage.py not found"**
- Make sure you're running the script from the Django project root directory

**"Permission denied"**
- Make sure the scripts are executable: `chmod +x *.py`

**"Module not found"**
- Make sure your virtual environment is activated
- Ensure Django settings are properly configured

**"Database locked"**
- Stop the Django development server before running the scripts
- Close any database browser tools that might have locks

### Getting Help

If you encounter issues:
1. Run with `--verbose` flag for detailed output
2. Use `--dry-run` to see what would happen without making changes
3. Check the error messages for specific issues
4. Verify your Django environment is properly set up

## 📝 Example Output

```bash
$ python quick_reset_db.py
🚀 Starting quick database reset and population...
🔄 Clearing database...
✅ Clearing database completed successfully

🔄 Populating users...
✅ Populating users completed successfully

🔄 Populating products...
✅ Populating products completed successfully

🔄 Populating questions...
✅ Populating questions completed successfully

🔄 Verifying population...
✅ Verifying population completed successfully

🎉 Database reset and population completed successfully!

📊 Summary:
👥 Users: 26
🛍️  Products: 49
❓ Questions: 46
📋 Questionnaires: 1
```
