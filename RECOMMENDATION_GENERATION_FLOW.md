# Recommendation Generation Flow

## 🎯 Overview

This document describes the automatic recommendation generation system that triggers after every completed quiz. The system ensures that users receive personalized hair care recommendations immediately upon quiz completion.

## 🔄 Flow Diagram

```
Quiz Completion → Upload Responses → Generate Recommendations → Mark Complete → Show Results
      ↓                ↓                      ↓                    ↓            ↓
   User submits    API stores all      AI generates         Redux state    User sees
   final quiz      quiz responses      personalized         updated        recommendations
   responses       in database         recommendations                     in dashboard
```

## 📋 Implementation Details

### 1. API Endpoint
- **URL**: `http://localhost:8000/api/recommendations/generate/user/${user_id}?session_guid=${session_guid}`
- **Method**: `POST`
- **Authentication**: JWT Bearer token required
- **Response**: Recommendation object with generated products

### 2. Trigger Points

#### A. Normal Quiz Completion
**Location**: `ui/src/screens/Quiz/pages/Results/index.tsx`
**Function**: `handleCompleteQuiz()`

```typescript
const handleCompleteQuiz = async (userId: string) => {
  try {
    // Step 1: Upload quiz responses
    await uploadQuestionaire(userId, answers, "", sessionGuid);
    
    // Step 2: Generate recommendations
    await generateRecommendations(userId, sessionGuid);
    
    // Step 3: Mark quiz as completed
    dispatch(markQuizCompleted());
    
    toast.success('Quiz completed and personalized recommendations generated!');
  } catch (error) {
    // Handle errors gracefully
  }
};
```

#### B. Session Attribution (Anonymous → Authenticated)
**Location**: `ui/src/hooks/user.actions.ts`
**Function**: `attributeQuizSessionToUser()`

```typescript
// After attributing anonymous session to authenticated user
await api.post('/api/quiz/replies/attribute-session', {
  session_guid: session.session_guid,
  user_id: userId
});

// Generate recommendations for the attributed session
await generateRecommendations(userId.toString(), session.session_guid);
```

### 3. Error Handling Strategy

#### Graceful Degradation
- **Quiz Upload Success + Recommendation Failure**: Quiz marked complete, user notified
- **Quiz Upload Failure**: User prompted to retry, quiz not marked complete
- **Network Issues**: Appropriate error messages, retry mechanisms

#### User Feedback
```typescript
// Success case
toast.success('Quiz completed and personalized recommendations generated!');

// Partial success case
toast.warning('Quiz completed successfully, but recommendation generation failed. You can try again later.');

// Failure case
toast.error('Failed to submit quiz. Please try again.');
```

## 🛠️ Technical Implementation

### 1. API Functions

#### Primary Function
```typescript
// ui/src/apis/index.ts
export const generateRecommendations = async (
  user_id: string,
  sessionGuid: string
): Promise<any> => {
  const response = await api.post(
    `/recommendations/generate/user/${user_id}?session_guid=${sessionGuid}`,
    {}
  );
  return response.data;
};
```

#### Modern API Service
```typescript
// ui/src/services/modernAPI.ts
async generateRecommendations(userId: number, sessionGuid: string): Promise<any> {
  return this.request<any>(`/recommendations/generate/user/${userId}?session_guid=${sessionGuid}`, {
    method: 'POST',
  });
}
```

### 2. React Query Hooks

#### Generation Hook
```typescript
export function useGenerateRecommendations() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, sessionGuid }: { userId: number; sessionGuid: string }) =>
      apiClient.generateRecommendations(userId, sessionGuid),
    onSuccess: (data, variables) => {
      // Invalidate recommendation caches
      queryClient.invalidateQueries({ queryKey: ['recommendations', variables.userId] });
    },
  });
}
```

#### Fetching Hooks
```typescript
// Get all user recommendations
export function useUserRecommendations(userId: number) {
  return useQuery({
    queryKey: ['recommendations', userId],
    queryFn: () => apiClient.getUserRecommendations(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get recommendations for specific session
export function useRecommendationBySession(userId: number, sessionGuid: string) {
  return useQuery({
    queryKey: ['recommendations', userId, sessionGuid],
    queryFn: () => apiClient.getRecommendationBySession(userId, sessionGuid),
    enabled: !!userId && !!sessionGuid,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
```

## 🔍 Testing

### Unit Tests
**Location**: `ui/src/utils/__tests__/recommendationGeneration.test.ts`

#### Test Coverage
- ✅ API endpoint calls with correct parameters
- ✅ Error handling for network failures
- ✅ Success response processing
- ✅ Integration with quiz completion flow
- ✅ Session attribution scenarios

#### Example Test
```typescript
it('should call the correct API endpoint with proper parameters', async () => {
  const mockResponse = { data: { id: 1, recommendations: ['Product A'] } };
  mockedApi.post.mockResolvedValueOnce(mockResponse);

  const result = await generateRecommendations('123', 'test-session');

  expect(mockedApi.post).toHaveBeenCalledWith(
    '/recommendations/generate/user/123?session_guid=test-session',
    {},
    {}
  );
  expect(result).toEqual(mockResponse.data);
});
```

## 📊 Monitoring & Analytics

### Logging Points
1. **Quiz Upload Start**: `console.log('Uploading quiz responses...')`
2. **Recommendation Generation Start**: `console.log('Generating recommendations...')`
3. **Success**: `console.log('Quiz completion and recommendation generation successful!')`
4. **Errors**: `console.error('Error during quiz completion process:', error)`

### Metrics to Track
- **Recommendation Generation Success Rate**: % of successful generations
- **Generation Time**: Time taken to generate recommendations
- **Error Types**: Classification of failure reasons
- **User Completion Rate**: % of users who complete the full flow

## 🚀 Deployment Considerations

### Environment Variables
```bash
# API Base URL
REACT_APP_API_BASE=http://localhost:8000/api

# Recommendation Service Timeout
RECOMMENDATION_TIMEOUT=30000
```

### Backend Dependencies
- Recommendation service must be running
- Database connection for storing recommendations
- AI/ML models loaded and ready

### Error Recovery
- Retry mechanism for transient failures
- Fallback to manual recommendation generation
- User notification system for failures

## 🔧 Troubleshooting

### Common Issues

#### 1. Recommendation Generation Fails
**Symptoms**: Quiz completes but no recommendations appear
**Solutions**:
- Check backend recommendation service status
- Verify user has sufficient quiz data
- Check API endpoint accessibility

#### 2. Session Attribution Issues
**Symptoms**: Anonymous quiz not attributed to user after login
**Solutions**:
- Verify session preservation in localStorage
- Check user authentication status
- Validate session GUID format

#### 3. Network Timeouts
**Symptoms**: Long delays or timeout errors
**Solutions**:
- Increase timeout values
- Implement retry logic
- Add loading indicators

### Debug Commands
```javascript
// Check preserved session
localStorage.getItem('preservedQuizSession')

// Check current user
store.getState().auth.user

// Check quiz state
store.getState().quiz

// Manual recommendation generation
generateRecommendations('USER_ID', 'SESSION_GUID')
```

## 📈 Future Enhancements

### Planned Improvements
1. **Background Processing**: Move recommendation generation to background jobs
2. **Real-time Updates**: WebSocket notifications for recommendation completion
3. **Batch Processing**: Generate recommendations for multiple sessions
4. **Caching**: Cache recommendations for faster retrieval
5. **A/B Testing**: Test different recommendation algorithms

### Performance Optimizations
- Lazy loading of recommendation data
- Pagination for large recommendation sets
- Compression of recommendation payloads
- CDN caching for static recommendation assets

## 📞 Support

### For Development Issues
1. Check console logs for error details
2. Verify API endpoint responses in Network tab
3. Test with different user scenarios
4. Review error handling in try-catch blocks

### For Production Issues
1. Monitor recommendation generation success rates
2. Set up alerts for high error rates
3. Implement health checks for recommendation service
4. Maintain fallback mechanisms for service outages

---

**The recommendation generation system is now fully integrated and will automatically create personalized recommendations for every completed quiz!** 🚀✨
