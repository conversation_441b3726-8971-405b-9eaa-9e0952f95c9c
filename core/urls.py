from django.shortcuts import redirect
from django.urls import path

from . import views


def redirect_to_products(request):
    return redirect("/products/")


def redirect_to_questions(request):
    return redirect("/questionaire/questions/")


def redirect_to_login(request):
    return redirect("/users/login/")


def redirect_to_register(request):
    return redirect("/users/register/")


urlpatterns = [
    path("", views.homepage, name="homepage"),
    path("about/", views.about, name="about"),
    path("contact/", views.contact, name="contact"),
    path("camera/", views.camera, name="test_camera"),
    # Redirects for compatibility - removed products redirect to avoid loop
    path("question/", redirect_to_questions, name="question"),
    path("login/", redirect_to_login, name="login_redirect"),
    path("register/", redirect_to_register, name="register_redirect"),
]
