from loguru import logger

HAIR_TYPE_MAP: dict[str, str] = {
    "Type 2a": "1",
    "Type 2b": "2",
    "Type 2c": "3",
    "Type 3a": "4",
    "Type 3b": "5",
    "Type 3c": "6",
    "Type 4a": "7",
    "Type 4b": "8",
    "Type 4c": "9",
}

TEXTURE_MAP: dict[str, str] = {"Fine": "10", "Medium": "11", "Thick": "12"}

POROSITY_MAP: dict[str, str] = {"Low": "13", "Medium": "14", "High": "15"}

GOAL_MAP: dict[str, str] = {
    "Growth": "16",
    "Healthy": "17",
    "Moisture": "18",
    "Ultra": "19",
}

ISSUE_MAP: dict[str, str] = {
    "Scalp dryness": "20",
    "Breakage": "21",
    "Thinning": "22",
    "Not me": "23",
}

logger.info("Using mappings for features")


def map_column_with_dict(df, column, mapping):
    return df[column].map(
        lambda val: next((v for k, v in mapping.items() if k in str(val)), "0")
    )


def map_all_ids(shampoo_df, conditioner_df, user_df):
    shampoo_df["Hair_TypeID"] = map_column_with_dict(
        shampoo_df, "Hair Type", HAIR_TYPE_MAP
    )
    conditioner_df["Hair_TypeID"] = map_column_with_dict(
        conditioner_df, "Hair Type", HAIR_TYPE_MAP
    )
    user_df["Hair_TypeID"] = map_column_with_dict(user_df, "Hair Type", HAIR_TYPE_MAP)

    shampoo_df["TextureID"] = map_column_with_dict(shampoo_df, "Texture ", TEXTURE_MAP)
    conditioner_df["TextureID"] = map_column_with_dict(
        conditioner_df, "Texture ", TEXTURE_MAP
    )
    user_df["TextureID"] = map_column_with_dict(user_df, "Hair Texture", TEXTURE_MAP)

    shampoo_df["PorosityID"] = map_column_with_dict(
        shampoo_df, "Porosity", POROSITY_MAP
    )
    conditioner_df["PorosityID"] = map_column_with_dict(
        conditioner_df, "Porosity", POROSITY_MAP
    )

    shampoo_df["GoalsID"] = map_column_with_dict(shampoo_df, "Hair Goals", GOAL_MAP)
    conditioner_df["GoalsID"] = map_column_with_dict(
        conditioner_df, "Hair Goals", GOAL_MAP
    )
    user_df["GoalsID"] = map_column_with_dict(user_df, "Goal ", GOAL_MAP)

    shampoo_df["IssuesID"] = map_column_with_dict(shampoo_df, "Hair Issues", ISSUE_MAP)
    conditioner_df["IssuesID"] = map_column_with_dict(
        conditioner_df, "Hair Issues", ISSUE_MAP
    )
    user_df["IssuesID"] = map_column_with_dict(user_df, "Issue ", ISSUE_MAP)

    return shampoo_df, conditioner_df, user_df
