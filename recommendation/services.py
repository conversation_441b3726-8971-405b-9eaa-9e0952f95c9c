from typing import Any

import requests
from django.conf import settings
from django.core.cache import cache
from django.shortcuts import get_object_or_404
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_fixed

# Import consolidated constants
from django.contrib.auth import get_user_model
from helpers.constants import APIConfig, APIUrls, CacheKeys
from users.models import UserProfile

User = get_user_model()

from .constants import QUESTION_POSITIONS, SHEET_PATHS
from .loader import get_user_question_answers, load_workbook_sheets
from .mappings import map_all_ids
from .models import Recommendation
from .utils import (
    build_user_profile_dataframe,
    compute_product_matches,
)


class RecommenderModule:
    def get_recommendations(
        self, user_id: int, session_guid: str = None
    ) -> Recommendation:
        logger.info(
            f"Generating recommendations GET_RECOMMENDATIONS for user {user_id} with session GUID {session_guid}"
        )

        user = get_object_or_404(User, id=user_id)
        logger.info(f"\n\tUser: {user.email}\n")
        user_profile = UserProfile.objects.select_related("user").get(user=user)
        logger.info(f"\n\tUser Profile: {user_profile}\n")

        if not session_guid:
            session_guid = self.fetch_latest_session_guid(user_id)
            logger.info(f"Fetched latest session_guid: {session_guid}")

        try:
            answers = get_user_question_answers(user, session_guid, QUESTION_POSITIONS)

        except Exception as e:
            logger.error(
                f"\n\tFailed to fetch answers: {e} - Question_positions: {QUESTION_POSITIONS}\n"
            )
            raise

        sheet_data = load_workbook_sheets(
            SHEET_PATHS["product_data"],
            ["Shampoo", "Conditioners", "Shampoo_o", "Conditioners_o"],
        )

        # TODO: Bug - There is is no logic for select the recommendations hence same products are recommended

        shampoo_df = sheet_data["Shampoo"]
        conditioner_df = sheet_data["Conditioners"]
        shamp_info_df = sheet_data["Shampoo_o"]
        cond_info_df = sheet_data["Conditioners_o"]

        logger.info(f"Loaded sheets: {list(sheet_data.keys())}")

        # Build structured dataframe from user + profile + answers
        user_df = build_user_profile_dataframe(user, user_profile, answers)

        logger.info(f"\n\tBuilt user profile - USER_DF: {user_df}\n")
        # user_df = user_df.applymap(lambda x: x)
        # logger.warning(user_df)

        # Map string attributes to ID values for categorical matching
        shampoo_df, conditioner_df, user_df = map_all_ids(
            shampoo_df, conditioner_df, user_df
        )

        # Compute top matches
        recommended_shampoos, recommended_conditioners = compute_product_matches(
            user_df, shampoo_df, conditioner_df
        )

        # recommended_shampoos = list(set(recommended_shampoos))
        # recommended_conditioners = list(set(recommended_conditioners))

        # Save to database (create or update existing recommendation for this session)
        recommendation, created = Recommendation.objects.update_or_create(
            user_id=user_id,
            session_guid=session_guid,
            defaults={
                "shampoos": recommended_shampoos,
                "conditioners": recommended_conditioners,
            },
        )

        logger.info(
            f"Recommendation {'created' if created else 'updated'} for user {user_id}, session {session_guid}"
        )

        # Return the actual model instance instead of a dictionary
        return recommendation

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
    def fetch_latest_session_guid(self, user_id: int) -> str | dict:
        try:
            user = get_object_or_404(User, id=user_id)
        except User.DoesNotExist as e:
            logger.error(f"Failed to fetch user: {e}")
            raise

        cache_key = CacheKeys.user_session_key(user_id)
        cached = cache.get(cache_key)
        if cached:
            return cached

        try:
            base_url: str | Any = getattr(
                settings, "QUIZ_API_BASE_URL", APIUrls.DEFAULT_QUIZ_API_BASE
            )
            response = requests.get(
                f"{base_url}/api/quiz/replies/sessions/user/{user_id}"
            )
            response.raise_for_status()
            sessions = response.json()
            if not sessions:
                logger.error("No sessions found for user.")
                return {"error": "No sessions found for user."}
                # raise ValueError("No sessions found for user.")
            session_guid: str | Any = sessions[0]["session_guid"]
            cache.set(cache_key, session_guid, timeout=APIConfig.CACHE_TIMEOUT)
            return session_guid
        except Exception as e:
            logger.error(f"Failed to fetch session_guid: {e}")
            raise
