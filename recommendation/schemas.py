from datetime import datetime

from ninja import Field, ModelSchema, Schema
from pydantic import computed_field

# Import shared schemas to avoid duplication
from .models import Recommendation


class RecommendationOut(ModelSchema):
    """
    Output schema for a Recommendation.
    This is configurable to return either the full list of recommendations or just the first one.
    """

    class Meta:
        from_attributes = True
        model = Recommendation
        model_fields = "__all__"
        exclude = [
            "user",
            "id",
        ]

        json_encoders = {datetime: lambda v: v.isoformat()}

    user_id: int = Field(..., gt=0)
    session_guid: str | None = None
    created_at: datetime = datetime.now()
    updated_at: datetime = datetime.now()
    conditioners: list[str] = Field(default_factory=list, exclude=True)
    shampoos: list[str] = Field(default_factory=list, exclude=True)

    @computed_field
    def conditioners_recs(self) -> str:
        """Returns the first item in the conditioners recommendations list as a string."""
        if self.conditioners and len(self.conditioners) > 0:
            # If it's already a string (like from the database), return it
            if isinstance(self.conditioners, str):
                return self.conditioners
            # If it's a list, return the first item
            return str(self.conditioners[0]) if self.conditioners[0] else ""
        return ""

    @computed_field
    def shampoos_recs(self) -> str:
        """Returns the first item in the shampoos recommendations list as a string."""
        if self.shampoos and len(self.shampoos) > 0:
            # If it's already a string (like from the database), return it
            if isinstance(self.shampoos, str):
                return self.shampoos
            # If it's a list, return the first item
            return str(self.shampoos[0]) if self.shampoos[0] else ""
        return ""


# ErrorDetail and Validation422Schema moved to helpers/schemas.py


class ProductOutRecs(Schema):
    id: int
    name: str
    price: float
    image_url: str | None


class RecommendationWithProductsOut(Schema):
    id: int
    created_at: datetime
    session_guid: str
    products: list[ProductOutRecs]


# MessageOut moved to helpers/schemas.py
