import numpy as np
import pandas as pd

# Import consolidated utility functions
from helpers.shared_utils import build_dataframe_from_dict, get_sheet_values


def get_values(sheet):
    """Legacy function - use get_sheet_values from shared_utils instead."""
    return get_sheet_values(sheet)


def build_user_profile_dataframe(user, user_profile, answers: dict) -> pd.DataFrame:
    # TODO: Need to update user profile once answers are available.  user_profile does  not have hair type by default
    profile_data = {
        "Customer_ID": user.id,
        "Hair Type": answers.get("hair_type"),
        "Hair Texture": answers.get("head_state"),
        "Goal ": answers.get("goal"),
        "Issue ": answers.get("issue"),
        "Preferences": answers.get("preferences"),
    }

    # Use consolidated utility function
    built_profile = build_dataframe_from_dict(profile_data)

    # "Hair Type": user_profile.hair_type #no need to use user profile hair type
    return built_profile


def compute_product_matches(user_df, shamp_df, cond_df):
    user_features = (
        user_df[["Hair_TypeID", "TextureID", "GoalsID", "IssuesID"]].astype(int).values
    )

    shamp_features = (
        shamp_df[["Hair_TypeID", "TextureID", "GoalsID", "IssuesID"]].astype(int).values
    )

    cond_features = (
        cond_df[["Hair_TypeID", "TextureID", "GoalsID", "IssuesID"]].astype(int).values
    )

    user_vector = user_features[0]
    shamp_scores = np.matmul(shamp_features, user_vector)
    cond_scores = np.matmul(cond_features, user_vector)

    # TODO: Set number of products returned before selecting unique products only
    top_n = 12
    shamp_df_sorted = shamp_df.iloc[shamp_scores.argsort()[::-1]].head(top_n)
    cond_df_sorted = cond_df.iloc[cond_scores.argsort()[::-1]].head(top_n)

    shampoos = list(set(shamp_df_sorted["Product "]))
    conditioners = list(set(cond_df_sorted["Product "]))

    return shampoos, conditioners
