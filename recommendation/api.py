import pandas as pd
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.template.loader import render_to_string
from loguru import logger
from ninja import Path, Router
from weasyprint import HTML

from helpers.error_handlers import handle_api_error

# Import shared schemas
from helpers.schemas import MessageOut, Validation422Schema
from helpers.utils import validate_param_rules
from reporter.models import HairScoreReport

from .models import Recommendation
from .schemas import (
    RecommendationOut,
)
from .services import RecommenderModule

router = Router()


@router.get(
    "/user/{int:user_id}",
    response={
        200: list[RecommendationOut],
        404: MessageOut,
        422: Validation422Schema,
        500: MessageOut,
    },
)
@handle_api_error
def get_latest_user_recommendations(request, user_id: int = Path(..., gt=0)):
    # No need for manual try-catch - @handle_api_error decorator handles all exceptions
    data = Recommendation.objects.filter(user_id=user_id).order_by("-created_at")
    logger.debug(data)
    return 200, data


# @router.get(
#     "/user/{int:user_id}/products",
#     response={
#         200: list[RecommendationWithProductsOut],
#         404: MessageOut,
#         422: Validation422Schema,
#         500: MessageOut,
#     },
# )
# def get_latest_user_recommendations_with_products(
#     request,
#     user_id: int = Path(..., gt=0),
# ):
#     """
#     Return a user's recommendations **plus** the `Product` objects they reference.
#     `shampoos` and `conditioners` are assumed to be *lists of product IDs*
#     stored in the JSON fields of the `Recommendation` model.
#     """
#     logger.warning(f"Fetching recommendations for user {user_id}")
#     # search_query = SearchQuery("name")
#     try:
#         # Fetch recommendations (latest first)
#         rec_qs = Recommendation.objects.filter(user_id=user_id).order_by("-created_at")

#         for rec in rec_qs:
#             logger.warning(f" RECOMMENDATION: {rec.shampoos}")
#             logger.warning(f" RECOMMENDATION: {rec.conditioners}")

#         if not rec_qs.exists():
#             return 404, {"message": "No recommendations found for that user"}

#         # ──────────────────────────────
#         # 1. Gather **all** product-ids
#         # ──────────────────────────────
#         shampoos = rec.shampoos
#         conditioners = rec.conditioners
#         for rec in shampoos:
#             products_qs = (
#                 Product.objects.annotate(search=SearchVector("name"))
#                 .filter(search=search_query)
#                 .order_by("id")  # or any ordering you prefer
#                 .distinct()
#             )
#             shampoos.append(product)
#             shampoos.extend(rec.shampoos)
#             conditioners.extend(rec.conditioners)

#         all_product_names = set(  # dedupe early
#             chain.from_iterable(
#                 (rec.shampoos or []) + (rec.conditioners or []) for rec in rec_qs
#             )
#         )

#         if not all_product_names:
#             # Edge-case: recs exist but point to nothing
#             return 200, [
#                 {
#                     "id": rec.id,
#                     "created_at": rec.created_at,
#                     "session_guid": rec.session_guid,
#                     "products": [],
#                 }
#                 for rec in rec_qs
#             ]

#         # ──────────────────────────────
#         # 2️ Fetch the Product objects
#         # ──────────────────────────────
#         product_map: Dict[str, Product] = {
#             p: p for p in Product.objects.filter(name__in=all_product_names)
#         }

#         # ──────────────────────────────
#         # 3️ Assemble the response
#         # ──────────────────────────────
#         payload = []
#         for rec in rec_qs:
#             product_names_this_rec = (rec.shampoos or []) + (rec.conditioners or [])
#             logger.warning(product_names_this_rec)
#             products_this_rec = [
#                 product_map[p_name]
#                 for p_name in product_names_this_rec
#                 if p_name in product_map
#             ]

#             payload.append(
#                 {
#                     "id": rec.id,
#                     "created_at": rec.created_at,
#                     "session_guid": rec.session_guid,
#                     "products": products_this_rec,
#                 }
#             )

#         return 200, payload

#     except Exception as exc:
#         # Log however your project handles errors
#         return 500, {"message": str(exc)}


@router.get(
    "/user/{int:user_id}/session/{str:session_guid}",
    response={200: RecommendationOut, 404: MessageOut, 500: MessageOut},
)
@validate_param_rules(user_id="gt0")
def get_recommendation_by_session(request, user_id: int, session_guid: str):
    try:
        recommendations = get_object_or_404(
            Recommendation, session_guid=session_guid, user_id=user_id
        )
        return 200, recommendations
    except Recommendation.DoesNotExist:
        return 404, {"message": "Recommendation not found"}
    except Exception as e:
        logger.warning(e)
        return 500, {"message": str(e)}


@router.post(
    "/generate/user/{int:user_id}",
    response={
        200: RecommendationOut,
        404: MessageOut,
        422: MessageOut,
        500: MessageOut,
    },
)
@handle_api_error
def generate_recommendation(
    request,
    user_id: int = Path(..., gt=0),
    session_guid: str | None = None,
):
    if not session_guid:
        session_guid = None
    logger.debug(
        f"Generating recommendations for user {user_id}, session {session_guid}"
    )
    recommender = RecommenderModule()
    # No need for manual try-catch - @handle_api_error decorator handles all exceptions
    logger.info(
        f"\n\tGenerating recommendations for USER {user_id}, session {session_guid}\n"
    )
    result = recommender.get_recommendations(user_id=user_id, session_guid=session_guid)

    return result


@router.get("/reports/session/{session_guid}/csv")
def export_csv(request, session_guid: str):
    report = get_object_or_404(HairScoreReport, session_guid=session_guid)
    df = pd.DataFrame(report.data).T.rename(
        columns={"value": "score", "description": "category"}
    )
    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = (
        f"attachment; filename=hair_report_{session_guid}.csv"
    )
    df.to_csv(path_or_buf=response)
    return response


# TODO: Fix templates to re-enable PDF export
# @router.get("/reports/session/{session_guid}/pdf")
def export_pdf(request, session_guid: str):
    report = get_object_or_404(HairScoreReport, session_guid=session_guid)
    html_string = render_to_string("report_template.html", {"report": report})
    pdf = HTML(string=html_string).write_pdf()
    response = HttpResponse(pdf, content_type="application/pdf")
    response["Content-Disposition"] = f"inline; filename=hair_report_{session_guid}.pdf"
    return response
