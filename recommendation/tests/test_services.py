from unittest.mock import MagicMock, patch

import pytest
from django.contrib.auth import get_user_model

from recommendation.models import Recommendation
from recommendation.services import RecommenderModule

User = get_user_model()


@pytest.mark.django_db
@patch("recommendation.services.requests.get")
def test_fetch_latest_session_guid(mock_get):
    mock_get.return_value.status_code = 200
    mock_get.return_value.json.return_value = [
        {"session_guid": "abc123", "updated_at": "2025-04-10T10:00:00Z"},
    ]

    recommender = RecommenderModule()
    session_guid = recommender.fetch_latest_session_guid(user_id=1)
    assert session_guid == "abc123"


@pytest.mark.django_db
@patch("recommendation.services.get_user_question_answers")
@patch("recommendation.services.load_workbook_sheets")
@patch("recommendation.services.map_all_ids")
@patch("recommendation.services.build_user_profile_dataframe")
@patch("recommendation.services.compute_product_matches")
def test_generate_recommendations(
    mock_compute_matches,
    mock_build_df,
    mock_map_ids,
    mock_load_sheets,
    mock_get_answers,
):
    user = User.objects.create_user(
        username="tester", email="<EMAIL>", password="testpass"
    )

    mock_get_answers.return_value = {
        "goal": "Growth",
        "issue": "Scalp dryness",
        "head_state": "Fine",
        "preferences": "Natural",
    }

    mock_load_sheets.return_value = {
        "Shampoo": MagicMock(),
        "Conditioners": MagicMock(),
        "Shampoo_o": MagicMock(),
        "Conditioners_o": MagicMock(),
    }

    mock_build_df.return_value = MagicMock()
    mock_map_ids.return_value = (MagicMock(), MagicMock(), MagicMock())
    mock_compute_matches.return_value = (
        ["Shampoo A", "Shampoo B"],
        ["Conditioner X", "Conditioner Y"],
    )

    recommender = Recommender()
    result = recommender.get_recommendations(
        user_id=user.id, session_guid="test-session-123"
    )

    assert result["user_id"] == user.id
    assert result["session_guid"] == "test-session-123"
    assert "Shampoo A" in result["shampoos"]
    assert "Conditioner X" in result["conditioners"]

    rec = Recommendation.objects.get(user=user, session_guid="test-session-123")
    assert rec.shampoos == ["Shampoo A", "Shampoo B"]
