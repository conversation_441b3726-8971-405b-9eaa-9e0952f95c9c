import pandas as pd
from loguru import logger
from openpyxl import load_workbook

from questionaire.models import Question, Reply

from .utils import get_values


def load_workbook_sheets(filepath, sheet_names):
    logger.info("Loading workbook...")
    workbook = load_workbook(filename=filepath, data_only=True)
    sheet_data = {}
    for name in sheet_names:
        rows = get_values(workbook[name])
        sheet_data[name] = pd.DataFrame(rows[1:], columns=rows[0])  # fix data structure
    return sheet_data


def get_user_question_answers(user, session_guid, question_position_map):
    answers = {}
    logger.info("Fetching answers...")
    for label, pos in question_position_map.items():
        try:
            question = Question.objects.filter(position=pos).first()
            reply = Reply.objects.filter(
                user=user, question_id=question.id, session_guid=session_guid
            ).first()
            if not reply:
                answers[label] = "No Reply"
            else:
                answers[label] = reply.text if reply else label
                # answers[label] = reply.text or reply.answer.score if reply.answer else label
        except Reply.DoesNotExist:
            answers[label] = "No Reply Exists"
    return answers
