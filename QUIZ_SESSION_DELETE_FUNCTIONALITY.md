# Quiz Session Delete Functionality

## 🎯 Overview

This document describes the delete functionality added to the Result History section of the dashboard. Users can now delete incomplete quiz sessions, removing them from both the UI and the database.

## 🔧 Implementation

### API Integration

#### Backend Endpoint
- **URL**: `/quiz/replies/sessions/session/{session_guid}`
- **Method**: `DELETE`
- **Authentication**: JWT Bear<PERSON> token required
- **Response**: Success confirmation with deletion details

#### Frontend API Function
```typescript
// ui/src/apis/index.ts
export const deleteQuizSession = async (sessionGuid: string): Promise<void> => {
  try {
    console.log(`Deleting quiz session: ${sessionGuid}`);
    const response = await api.delete(`/quiz/replies/sessions/session/${sessionGuid}`);
    console.log("Quiz session deleted successfully:", sessionGuid);
    return response.data;
  } catch (error) {
    console.error("Error deleting quiz session:", error);
    throw error;
  }
};
```

### UI Components

#### Delete Button
**Location**: `ui/src/screens/Dashboard/ReportHistory/UserSessions/index.tsx`

**Features**:
- ✅ **Only shown for incomplete sessions** - Completed sessions cannot be deleted
- ✅ **Icon-only button** with trash icon for clean UI
- ✅ **Tooltip** explaining the action
- ✅ **Positioned next to "Continue Quiz" button**

```typescript
<Button
  variant="outline-danger"
  size="sm"
  className="rounded-pill px-2"
  onClick={() => handleDeleteClick(session.session_guid)}
  style={{ fontSize: '0.875rem' }}
  title="Delete incomplete quiz session"
>
  <Trash2 size={14} />
</Button>
```

#### Confirmation Modal
**Features**:
- ✅ **Warning design** with danger colors and icons
- ✅ **Clear messaging** about permanent deletion
- ✅ **Loading state** during deletion process
- ✅ **Cancel option** to abort deletion

```typescript
<Modal show={showDeleteModal} onHide={handleDeleteCancel} centered>
  <Modal.Header closeButton>
    <Modal.Title className="fs-5 fw-bold text-danger">
      <Trash2 size={20} className="me-2" />
      Delete Quiz Session
    </Modal.Title>
  </Modal.Header>
  <Modal.Body>
    <div className="text-center py-3">
      <div className="mb-3">
        <div 
          className="d-inline-flex align-items-center justify-content-center p-3 rounded-circle"
          style={{ background: '#FEF2F2', color: '#DC2626' }}
        >
          <AlertCircle size={32} />
        </div>
      </div>
      <h5 className="fw-bold mb-2">Are you sure?</h5>
      <p className="text-muted mb-0">
        This will permanently delete the incomplete quiz session and all its responses. 
        This action cannot be undone.
      </p>
    </div>
  </Modal.Body>
  <Modal.Footer className="border-0 pt-0">
    <Button variant="outline-secondary" onClick={handleDeleteCancel} disabled={deleting}>
      Cancel
    </Button>
    <Button variant="danger" onClick={handleDeleteConfirm} disabled={deleting}>
      {deleting ? (
        <>
          <Spinner size="sm" animation="border" />
          Deleting...
        </>
      ) : (
        <>
          <Trash2 size={16} />
          Delete Session
        </>
      )}
    </Button>
  </Modal.Footer>
</Modal>
```

### State Management

#### Component State
```typescript
const [showDeleteModal, setShowDeleteModal] = useState(false);
const [sessionToDelete, setSessionToDelete] = useState<string | null>(null);
const [deleting, setDeleting] = useState(false);
```

#### Delete Flow
```typescript
const handleDeleteClick = (sessionGuid: string) => {
  setSessionToDelete(sessionGuid);
  setShowDeleteModal(true);
};

const handleDeleteConfirm = async () => {
  if (!sessionToDelete) return;

  setDeleting(true);
  try {
    await deleteQuizSession(sessionToDelete);
    
    // Remove from local state
    setSessions(prevSessions => 
      prevSessions.filter(session => session.session_guid !== sessionToDelete)
    );
    
    toast.success('Quiz session deleted successfully');
    setShowDeleteModal(false);
    setSessionToDelete(null);
    
    // Refresh data
    handleRefresh();
  } catch (error) {
    console.error('Error deleting session:', error);
    toast.error('Failed to delete quiz session. Please try again.');
  } finally {
    setDeleting(false);
  }
};
```

## 🎯 User Experience

### Visual Design

#### Delete Button
- **Style**: Outline danger button with red border
- **Size**: Small, compact design
- **Icon**: Trash2 icon from Lucide React
- **Position**: Right side, next to "Continue Quiz" button
- **Tooltip**: "Delete incomplete quiz session"

#### Confirmation Modal
- **Design**: Centered modal with warning styling
- **Colors**: Red/danger theme for destructive action
- **Icon**: Alert circle for warning
- **Layout**: Clean, focused design with clear messaging

#### Loading States
- **Button**: Shows spinner and "Deleting..." text
- **Disabled**: All interactive elements disabled during deletion
- **Feedback**: Toast notifications for success/error

### User Flow

1. **User sees incomplete session** in Result History
2. **User clicks delete button** (trash icon)
3. **Confirmation modal appears** with warning message
4. **User confirms deletion** by clicking "Delete Session"
5. **Loading state shown** during API call
6. **Success feedback** via toast notification
7. **Session removed** from list immediately
8. **Data refreshed** to ensure consistency

### Safety Features

#### Restrictions
- ✅ **Only incomplete sessions** can be deleted
- ✅ **Completed sessions** have no delete option
- ✅ **Confirmation required** - no accidental deletions
- ✅ **Clear warning** about permanent action

#### Error Handling
- ✅ **Network errors** handled gracefully
- ✅ **Server errors** show appropriate messages
- ✅ **User feedback** via toast notifications
- ✅ **State recovery** on error

## 🔧 Technical Implementation

### React Query Integration

#### Delete Hook
```typescript
export function useDeleteQuizSession() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (sessionGuid: string) => apiClient.deleteQuizSession(sessionGuid),
    onSuccess: (data, sessionGuid) => {
      console.log('Quiz session deleted successfully:', sessionGuid);
      // Invalidate user sessions queries to refresh the list
      queryClient.invalidateQueries({ queryKey: ['userSessions'] });
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
    },
    onError: (error) => {
      console.error('Quiz session deletion failed:', error);
    },
  });
}
```

#### Modern API Service
```typescript
async deleteQuizSession(sessionGuid: string): Promise<void> {
  return this.request<void>(`/quiz/replies/sessions/session/${sessionGuid}`, {
    method: 'DELETE',
  });
}
```

### Error Handling

#### API Errors
- **404**: Session not found
- **403**: Cannot delete completed session
- **401**: Authentication required
- **500**: Server error

#### User Feedback
```typescript
try {
  await deleteQuizSession(sessionToDelete);
  toast.success('Quiz session deleted successfully');
} catch (error) {
  console.error('Error deleting session:', error);
  toast.error('Failed to delete quiz session. Please try again.');
}
```

## 📊 Testing

### Unit Tests
**Location**: `ui/src/utils/__tests__/quizSessionDeletion.test.ts`

#### Test Coverage
- ✅ **API endpoint calls** with correct parameters
- ✅ **Error handling** for various HTTP status codes
- ✅ **Success response** processing
- ✅ **Network error** handling
- ✅ **Edge cases** (empty GUID, server errors)

#### Example Test
```typescript
it('should call the correct API endpoint with session GUID', async () => {
  const mockResponse = { data: { success: true } };
  mockedApi.delete.mockResolvedValueOnce(mockResponse);

  const sessionGuid = 'test-session-guid-123';
  const result = await deleteQuizSession(sessionGuid);

  expect(mockedApi.delete).toHaveBeenCalledWith(
    `/quiz/replies/sessions/session/${sessionGuid}`
  );
  expect(result).toEqual(mockResponse.data);
});
```

## 🚀 Benefits

### For Users
- ✅ **Clean up incomplete sessions** that are no longer needed
- ✅ **Reduce clutter** in Result History
- ✅ **Clear interface** with only relevant sessions
- ✅ **Safe deletion** with confirmation step

### For System
- ✅ **Database cleanup** removes unused data
- ✅ **Storage optimization** by removing incomplete responses
- ✅ **Data integrity** maintained through proper deletion
- ✅ **Performance** improved with less data to query

### For Developers
- ✅ **Consistent API** following REST conventions
- ✅ **Proper error handling** with user feedback
- ✅ **Reusable components** for future delete operations
- ✅ **Well-tested** functionality with comprehensive tests

## 🔒 Security Considerations

### Authorization
- ✅ **JWT authentication** required for API access
- ✅ **User ownership** verified by backend
- ✅ **Session validation** ensures user can only delete own sessions

### Data Protection
- ✅ **Confirmation required** prevents accidental deletion
- ✅ **Audit logging** on backend for deletion tracking
- ✅ **Proper cleanup** ensures all related data is removed

## 📈 Future Enhancements

### Planned Features
- **Bulk deletion** for multiple incomplete sessions
- **Soft delete** option with recovery period
- **Export data** before deletion
- **Deletion history** for audit purposes

### Performance Optimizations
- **Optimistic updates** for faster UI response
- **Background deletion** for large datasets
- **Batch operations** for multiple deletions

---

**The delete functionality provides users with a clean, safe way to remove incomplete quiz sessions from their Result History while maintaining data integrity and providing excellent user experience!** 🗑️✨
