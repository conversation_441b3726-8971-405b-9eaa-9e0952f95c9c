#!/usr/bin/env python
"""
Database clearing script for Django project.
This script clears all data from the database while preserving superusers.
"""

import os
import sys

import django

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings")
django.setup()

from django.contrib.auth import get_user_model

from products.models import Product
from questionaire.models import Answer, Question, Questionaire, Reply


def clear_database():
    """Clear all data from the database."""
    User = get_user_model()
    print("🗑️  Clearing all data...")

    # Clear questionnaire data
    reply_count = Reply.objects.count()
    Reply.objects.all().delete()
    print(f"✅ Deleted {reply_count} replies")

    answer_count = Answer.objects.count()
    Answer.objects.all().delete()
    print(f"✅ Deleted {answer_count} answers")

    question_count = Question.objects.count()
    Question.objects.all().delete()
    print(f"✅ Deleted {question_count} questions")

    questionaire_count = Questionaire.objects.count()
    Questionaire.objects.all().delete()
    print(f"✅ Deleted {questionaire_count} questionnaires")

    # Clear products
    product_count = Product.objects.count()
    Product.objects.all().delete()
    print(f"✅ Deleted {product_count} products")

    # Clear users (except superusers)
    user_count = User.objects.filter(is_superuser=False).count()
    User.objects.filter(is_superuser=False).delete()
    print(f"✅ Deleted {user_count} non-superuser users")

    print("🎉 Database cleared successfully!")


if __name__ == "__main__":
    clear_database()
