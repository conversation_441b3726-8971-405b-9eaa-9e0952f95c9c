from __future__ import annotations

from typing import Any

from django.contrib.auth import authenticate, get_user_model
from django.middleware.csrf import get_token
from django.urls import register_converter
from django.shortcuts import get_object_or_404
from django.utils import timezone
from loguru import logger
from ninja.errors import HttpError
from ninja.security import API<PERSON>ey<PERSON><PERSON><PERSON>, HttpBasic<PERSON>uth, HttpBearer

# Import the new consolidated API setup
from helpers.api_config import setup_api

User = get_user_model()


# --------------------------------------------------------------------------- #
# Authentication backends
# --------------------------------------------------------------------------- #
class CookieAuth(APIKeyCookie):
    """Simple cookie‑based authentication for internal tooling only."""

    param_name = "auth"

    def authenticate(self, request, key: str | None) -> bool | None:
        return True if key == self.param_name else None


class BasicAuth(HttpBasicAuth):
    """Fallback HTTP basic auth; avoid in production unless over TLS."""

    def authenticate(self, request, username: str, password: str) -> User:
        user = authenticate(username=username, password=password)
        if user:
            return user
        raise HttpError(401, "Invalid credentials")


class TokenAuth(HttpBearer):
    """Authenticate using the default Django `auth_token` field."""

    def authenticate(self, request, token: str) -> User:
        user = get_object_or_404(User, auth_token=token)
        request.user = user  # type: ignore[attr-defined]
        return user


class AuthBearer(HttpBearer):
    """Temporary bearer token backend; replace with real provider ASAP."""

    TOKEN = "supersecret"

    def authenticate(self, request, token: str) -> str | None:
        if token == self.TOKEN:
            return token
        logger.warning(
            "Invalid bearer token supplied from %s", request.META.get("REMOTE_ADDR")
        )
        return None


# --------------------------------------------------------------------------- #
# Converters
# --------------------------------------------------------------------------- #
class SignedIntConverter:
    """URL converter that accepts negative integers (e.g. /-4/)."""

    regex = r"-?\d+"

    def to_python(self, value: str) -> int:
        return int(value)

    def to_url(self, value: int) -> str:
        return str(value)


register_converter(SignedIntConverter, "sint")

# --------------------------------------------------------------------------- #
# API instance - Use consolidated setup
# --------------------------------------------------------------------------- #
api = setup_api()


# --------------------------------------------------------------------------- #
# Utility endpoints
# --------------------------------------------------------------------------- #
@api.get("/bearer", auth=AuthBearer(), tags=["token"])
def bearer(request):
    """Return a CSRF token when the bearer auth succeeds."""

    return {"token": get_token(request)}


@api.get("/csrf", auth=None, tags=["token"])
def get_csrf_token(request):
    """Explicit endpoint to fetch a CSRF token."""

    return {"csrfToken": get_token(request)}


@api.get("/process_time", tags=["1Ops"])
def current_server_time(request):
    """Helpful endpoint for latency checks & homogenous server time."""

    current_time = timezone.now()
    logger.debug(f"Current time on the server: {current_time}")
    return current_time


# --------------------------------------------------------------------------- #
# Exception handling
# --------------------------------------------------------------------------- #
@api.exception_handler(HttpError)
def custom_http_error_handler(request, exc: HttpError):
    """Global handler returning a consistent error envelope and structured log.

    We cater for both `detail` (newer Django‑Ninja) and `message` (older) attribute
    names to stay backward‑compatible and prevent `AttributeError`.
    """

    # Django‑Ninja <=0.19 uses `message`, >=0.20 renamed it to `detail`.
    error_text: str = getattr(exc, "detail", getattr(exc, "message", str(exc)))

    logger.bind(
        status=exc.status_code,
        method=request.method,
        path=request.path,
        user=getattr(request, "user", None),
    ).warning("%s", error_text)

    payload: dict[str, Any] = {
        "status": exc.status_code,
        "error": error_text,
        "path": request.path,
        "timestamp": timezone.now().isoformat(),
    }
    return api.create_response(request, payload, status=exc.status_code)
