"""
Django settings module selector.

This module automatically selects the appropriate settings file based on
the DJANGO_SETTINGS_MODULE environment variable or defaults to development.
"""

import os

# Determine which settings to use based on environment
settings_module = os.environ.get("DJANGO_SETTINGS_MODULE")

if not settings_module:
    # Default to development if no settings module is specified
    env = os.environ.get("ENV", "DEV").upper()

    if env == "PROD" or env == "PRODUCTION":
        os.environ.setdefault(
            "DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.production"
        )
    elif env == "TEST" or env == "TESTING":
        os.environ.setdefault(
            "DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.testing"
        )
    else:
        os.environ.setdefault(
            "DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.development"
        )

# Import the selected settings
settings_module = os.environ.get("DJANGO_SETTINGS_MODULE")
if "production" in settings_module:
    from .production import *
elif "testing" in settings_module:
    from .testing import *
else:
    from .development import *
